#!/usr/bin/env python3
"""
🎯 TESTE DO SISTEMA DE PADRONIZAÇÃO DE PRODUTOS SANTACLARA
Mostra antes/depois da padronização e análises melhoradas
"""

import pandas as pd
import sys
import os

# Adicionar o caminho das melhorias avançadas
sys.path.append('melhorias_avancadas')

from dashboard_web import dashboard_data

def testar_padronizacao():
    """Testa o sistema de padronização de produtos"""
    
    print("🎯 TESTANDO SISTEMA DE PADRONIZAÇÃO SANTACLARA")
    print("=" * 60)
    
    # Usar dados já carregados
    print("📊 Usando dados já carregados do dashboard...")
    dashboard = dashboard_data
    
    if dashboard.df_produtos is None:
        print("❌ Erro: Arquivo de produtos não encontrado!")
        print("📁 Certifique-se que 'controle_produtos.xlsx' existe na pasta 'dados/'")
        return
    
    print(f"✅ {len(dashboard.df_produtos)} produtos carregados")
    
    # Verificar se a padronização foi aplicada
    if 'Produto_Padronizado' not in dashboard.df_produtos.columns:
        print("❌ Erro: Padronização não foi aplicada!")
        return
    
    print("\n🔍 ANÁLISE ANTES/DEPOIS DA PADRONIZAÇÃO")
    print("-" * 50)
    
    # Mostrar exemplos de padronização
    print("\n📋 EXEMPLOS DE PADRONIZAÇÃO:")
    exemplos = dashboard.df_produtos[['Descricao Produto', 'Produto_Padronizado']].drop_duplicates().head(10)
    
    for _, row in exemplos.iterrows():
        original = row['Descricao Produto']
        padronizado = row['Produto_Padronizado']
        
        if len(original) > 50:
            original = original[:47] + "..."
        if len(padronizado) > 30:
            padronizado = padronizado[:27] + "..."
            
        print(f"ANTES: {original}")
        print(f"DEPOIS: {padronizado}")
        print("-" * 30)
    
    # Estatísticas de padronização
    produtos_originais = dashboard.df_produtos['Descricao Produto'].nunique()
    produtos_padronizados = dashboard.df_produtos['Produto_Padronizado'].nunique()
    
    print(f"\n📊 ESTATÍSTICAS DE PADRONIZAÇÃO:")
    print(f"🔸 Produtos únicos ANTES: {produtos_originais}")
    print(f"🔸 Produtos únicos DEPOIS: {produtos_padronizados}")
    print(f"🔸 Redução: {produtos_originais - produtos_padronizados} produtos ({((produtos_originais - produtos_padronizados) / produtos_originais * 100):.1f}%)")
    
    # Top produtos padronizados
    print(f"\n🏆 TOP 10 PRODUTOS PADRONIZADOS (por valor):")
    top_produtos = dashboard.get_top_produtos_padronizados(limit=10)
    
    if top_produtos['labels']:
        for i, (produto, valor, detalhe) in enumerate(zip(top_produtos['labels'], top_produtos['data'], top_produtos['detalhes']), 1):
            print(f"{i:2d}. {produto}")
            print(f"    💰 Valor Total: R$ {valor:,.2f}")
            print(f"    📦 Vendas: {detalhe['qtd_vendas']} | Unidades: {detalhe['qtd_unidades']} | Fornecedores: {detalhe['qtd_fornecedores']}")
            print()
    
    # Análise de oscilação de preços
    print(f"\n📈 ANÁLISE DE OSCILAÇÃO DE PREÇOS:")
    oscilacao = dashboard.get_analise_oscilacao_precos()
    
    if 'top_oscilacao' in oscilacao and oscilacao['top_oscilacao']:
        print("🔸 Top 5 produtos com maior oscilação de preço:")
        count = 0
        for produto, dados in oscilacao['top_oscilacao'].items():
            if count >= 5:
                break
            print(f"   {count+1}. {produto}")
            print(f"      💰 Preço: R$ {dados['min']:.2f} - R$ {dados['max']:.2f}")
            print(f"      📊 Oscilação: {dados['Oscilacao_Percentual']:.1f}%")
            count += 1
    
    print(f"\n✅ TESTE CONCLUÍDO!")
    print(f"🌐 Acesse o dashboard em: http://localhost:5000")
    print(f"🎯 As análises agora usam produtos padronizados!")

def testar_apis():
    """Testa as novas APIs"""
    print(f"\n🔗 TESTANDO NOVAS APIs:")
    print("📡 Você pode testar estas URLs no navegador:")
    print("   http://localhost:5000/api/top-produtos-padronizados")
    print("   http://localhost:5000/api/analise-oscilacao-precos")
    print("   http://localhost:5000/api/resumo")

if __name__ == "__main__":
    try:
        testar_padronizacao()
        testar_apis()
    except Exception as e:
        print(f"❌ Erro durante o teste: {e}")
        print("💡 Certifique-se que:")
        print("   1. O arquivo 'controle_produtos.xlsx' existe")
        print("   2. O dashboard está rodando (python melhorias_avancadas/dashboard_web.py)")
