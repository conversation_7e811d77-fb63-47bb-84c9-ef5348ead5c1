#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AUTOMAÇÃO DIÁRIA - SISTEMA DE INTELIGÊNCIA FINANCEIRA
Executa rotina automática diária de processamento e análise
"""

import os
import sys
import subprocess
from datetime import datetime
import json

def log_execucao(mensagem, tipo="INFO"):
    """Registra log da execução"""
    timestamp = datetime.now().strftime('%d/%m/%Y %H:%M:%S')
    emoji = "ℹ️" if tipo == "INFO" else "✅" if tipo == "SUCCESS" else "❌" if tipo == "ERROR" else "⚠️"
    print(f"{emoji} [{timestamp}] {mensagem}")
    
    # Salvar em arquivo de log
    try:
        with open('log_automacao_diaria.txt', 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {tipo}: {mensagem}\n")
    except:
        pass

def verificar_arquivos_necessarios():
    """Verifica se todos os arquivos necessários existem"""
    arquivos_necessarios = [
        'automacao_nf.py',
        'central_inteligencia_financeira.py',
        'dashboard_financeiro.py',
        'sistema_alertas.py',
        'relatorios_gerenciais.py'
    ]
    
    arquivos_faltando = []
    for arquivo in arquivos_necessarios:
        if not os.path.exists(arquivo):
            arquivos_faltando.append(arquivo)
    
    if arquivos_faltando:
        log_execucao(f"Arquivos faltando: {', '.join(arquivos_faltando)}", "ERROR")
        return False
    
    log_execucao("Todos os arquivos necessários encontrados", "SUCCESS")
    return True

def executar_processamento_emails():
    """Executa processamento de e-mails não lidos"""
    log_execucao("Iniciando processamento de e-mails não lidos...")
    
    try:
        # Executar automação de NFe com opção 1 (e-mails não lidos)
        resultado = subprocess.run(
            [sys.executable, 'automacao_nf.py'],
            input='1\n',  # Escolher opção 1
            capture_output=True,
            text=True,
            timeout=1800  # 30 minutos timeout
        )
        
        if resultado.returncode == 0:
            log_execucao("Processamento de e-mails concluído com sucesso", "SUCCESS")
            
            # Extrair estatísticas do output
            output_lines = resultado.stdout.split('\n')
            for line in output_lines:
                if 'processados com sucesso' in line.lower() or 'emails processados' in line.lower():
                    log_execucao(f"Estatística: {line.strip()}", "INFO")
            
            return True
        else:
            log_execucao(f"Erro no processamento de e-mails: {resultado.stderr}", "ERROR")
            return False
            
    except subprocess.TimeoutExpired:
        log_execucao("Timeout no processamento de e-mails (30 min)", "ERROR")
        return False
    except Exception as e:
        log_execucao(f"Erro inesperado no processamento: {e}", "ERROR")
        return False

def executar_analise_financeira():
    """Executa análise financeira completa"""
    log_execucao("Iniciando análise financeira completa...")
    
    try:
        # Executar central de inteligência com análise completa
        resultado = subprocess.run(
            [sys.executable, 'central_inteligencia_financeira.py', '--analise'],
            capture_output=True,
            text=True,
            timeout=300  # 5 minutos timeout
        )
        
        if resultado.returncode == 0:
            log_execucao("Análise financeira concluída com sucesso", "SUCCESS")
            
            # Extrair informações importantes do output
            output_lines = resultado.stdout.split('\n')
            for line in output_lines:
                if 'Valor Total Processado:' in line:
                    log_execucao(f"Total processado: {line.split(':')[1].strip()}", "INFO")
                elif 'Total de Notas Fiscais:' in line:
                    log_execucao(f"NFs processadas: {line.split(':')[1].strip()}", "INFO")
                elif 'Unidades Ativas:' in line:
                    log_execucao(f"Unidades ativas: {line.split(':')[1].strip()}", "INFO")
            
            return True
        else:
            log_execucao(f"Erro na análise financeira: {resultado.stderr}", "ERROR")
            return False
            
    except subprocess.TimeoutExpired:
        log_execucao("Timeout na análise financeira (5 min)", "ERROR")
        return False
    except Exception as e:
        log_execucao(f"Erro inesperado na análise: {e}", "ERROR")
        return False

def gerar_resumo_diario():
    """Gera resumo diário da execução"""
    log_execucao("Gerando resumo diário...")
    
    try:
        # Verificar arquivos gerados
        arquivos_gerados = []
        
        # Verificar planilhas
        planilhas = ['controle_produtos.xlsx', 'controle_faturamento_geral.xlsx', 'controle_boletos.xlsx']
        for planilha in planilhas:
            if os.path.exists(planilha):
                import pandas as pd
                df = pd.read_excel(planilha)
                arquivos_gerados.append(f"{planilha}: {len(df)} registros")
        
        # Verificar relatórios
        relatorios = [f for f in os.listdir('.') if f.startswith('relatorio_gerencial_') and f.endswith('.xlsx')]
        if relatorios:
            arquivos_gerados.extend([f"Relatório: {r}" for r in relatorios])
        
        # Verificar alertas
        if os.path.exists('alertas_sistema.json'):
            with open('alertas_sistema.json', 'r', encoding='utf-8') as f:
                alertas = json.load(f)
            arquivos_gerados.append(f"Alertas: {len(alertas)} alertas ativos")
        
        # Gerar resumo
        resumo = {
            'data_execucao': datetime.now().strftime('%d/%m/%Y %H:%M:%S'),
            'arquivos_gerados': arquivos_gerados,
            'status': 'SUCESSO'
        }
        
        # Salvar resumo
        with open('resumo_execucao_diaria.json', 'w', encoding='utf-8') as f:
            json.dump(resumo, f, ensure_ascii=False, indent=2)
        
        log_execucao(f"Resumo diário gerado: {len(arquivos_gerados)} arquivos processados", "SUCCESS")
        return True
        
    except Exception as e:
        log_execucao(f"Erro ao gerar resumo diário: {e}", "ERROR")
        return False

def enviar_notificacao_conclusao(sucesso_total):
    """Envia notificação de conclusão"""
    status = "SUCESSO" if sucesso_total else "FALHA"
    emoji = "🎉" if sucesso_total else "❌"
    
    log_execucao(f"{emoji} AUTOMAÇÃO DIÁRIA CONCLUÍDA - STATUS: {status}", "SUCCESS" if sucesso_total else "ERROR")
    
    # Aqui você pode adicionar integração com:
    # - E-mail
    # - WhatsApp Business API
    # - Slack
    # - Teams
    # - Telegram
    
    print(f"\n{emoji} AUTOMAÇÃO DIÁRIA FINALIZADA - {status}")
    print(f"📅 Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print(f"📋 Log completo salvo em: log_automacao_diaria.txt")
    print(f"📊 Resumo salvo em: resumo_execucao_diaria.json")

def main():
    """Função principal da automação diária"""
    print("🤖 INICIANDO AUTOMAÇÃO DIÁRIA - SISTEMA DE INTELIGÊNCIA FINANCEIRA")
    print("="*80)
    
    log_execucao("Iniciando automação diária do sistema financeiro", "INFO")
    
    # 1. Verificar arquivos necessários
    if not verificar_arquivos_necessarios():
        log_execucao("Automação interrompida - arquivos faltando", "ERROR")
        enviar_notificacao_conclusao(False)
        return
    
    # 2. Executar processamento de e-mails
    sucesso_emails = executar_processamento_emails()
    
    # 3. Executar análise financeira (mesmo se e-mails falharam)
    sucesso_analise = executar_analise_financeira()
    
    # 4. Gerar resumo diário
    sucesso_resumo = gerar_resumo_diario()
    
    # 5. Determinar sucesso total
    sucesso_total = sucesso_emails and sucesso_analise and sucesso_resumo
    
    # 6. Enviar notificação de conclusão
    enviar_notificacao_conclusao(sucesso_total)
    
    # 7. Código de saída
    sys.exit(0 if sucesso_total else 1)

if __name__ == '__main__':
    main()
