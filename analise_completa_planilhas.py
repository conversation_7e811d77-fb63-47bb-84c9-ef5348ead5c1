#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import numpy as np
from datetime import datetime
import os

def limpar_valor(serie):
    """Limpa valores monetários e numéricos formatados"""
    return pd.to_numeric(serie.astype(str).str.replace('R$ ', '').str.replace('.', '').str.replace(',', '.'), errors='coerce')

def formatar_moeda(valor):
    """Formata valor para moeda brasileira"""
    return f"R$ {valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')

def formatar_numero(valor):
    """Formata número com separador de milhares brasileiro"""
    return f"{int(valor):,}".replace(',', '.')

print('🔍 ANÁLISE COMPLETA DAS PLANILHAS - SCOUT DETALHADO')
print('=' * 80)
print(f'⏰ Análise realizada em: {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}')
print()

# Verificar arquivos disponíveis
print('📁 ARQUIVOS DISPONÍVEIS:')
arquivos = [f for f in os.listdir('.') if f.endswith('.xlsx')]
for arquivo in arquivos:
    try:
        size = os.path.getsize(arquivo) / 1024 / 1024  # MB
        print(f'   📄 {arquivo} ({size:.1f} MB)')
    except:
        print(f'   📄 {arquivo} (erro ao ler tamanho)')
print()

# 1. ANÁLISE DO CONTROLE DE PRODUTOS
print('📊 1. ANÁLISE DO CONTROLE DE PRODUTOS')
print('-' * 50)
try:
    df_produtos = pd.read_excel('controle_produtos.xlsx')
    print(f'✅ Arquivo carregado: controle_produtos.xlsx')
    print(f'📋 Total de registros: {len(df_produtos):,}')
    print(f'📋 Total de colunas: {len(df_produtos.columns)}')
    print()
    
    # Verificar novas colunas
    novas_colunas = ['Fabricante', 'Lote', 'Data Validade']
    print('🆕 STATUS DAS NOVAS COLUNAS:')
    for col in novas_colunas:
        if col in df_produtos.columns:
            preenchidos = df_produtos[col].notna().sum()
            total = len(df_produtos)
            percentual = (preenchidos / total) * 100
            print(f'   ✅ {col}: {formatar_numero(preenchidos)} de {formatar_numero(total)} preenchidos ({percentual:.1f}%)')
        else:
            print(f'   ❌ {col}: Coluna não encontrada')
    print()
    
    # Estatísticas por unidade
    if 'Unidade' in df_produtos.columns:
        print('🏢 ESTATÍSTICAS POR UNIDADE:')
        
        unidades_stats = df_produtos.groupby('Unidade').agg({
            'Numero NF': 'nunique',
            'Valor Total Item': lambda x: limpar_valor(x).sum(),
            'Quantidade': lambda x: limpar_valor(x).sum()
        }).round(2)
        
        unidades_stats.columns = ['NFes_Únicas', 'Valor_Total_R$', 'Quantidade_Total']
        unidades_stats = unidades_stats.sort_values('Valor_Total_R$', ascending=False)
        
        for unidade, row in unidades_stats.iterrows():
            print(f'   📍 {unidade}:')
            print(f'      📄 NFes: {formatar_numero(row["NFes_Únicas"])}')
            print(f'      💰 Valor: {formatar_moeda(row["Valor_Total_R$"])}')
            print(f'      📦 Quantidade: {formatar_numero(row["Quantidade_Total"])}')
            print()
        
        print(f'🏢 TOTAL DE UNIDADES ENCONTRADAS: {len(unidades_stats)}')
        print()
        
        # Totais gerais
        total_nfes = df_produtos['Numero NF'].nunique()
        total_valor = limpar_valor(df_produtos['Valor Total Item']).sum()
        total_quantidade = limpar_valor(df_produtos['Quantidade']).sum()
        
        print('💰 TOTAIS GERAIS - PRODUTOS:')
        print(f'   📄 Total de NFes únicas: {formatar_numero(total_nfes)}')
        print(f'   💰 Valor total: {formatar_moeda(total_valor)}')
        print(f'   📦 Quantidade total: {formatar_numero(total_quantidade)}')
        print(f'   🏷️ Produtos únicos: {formatar_numero(df_produtos["Descricao Produto"].nunique())}')
        print()
    
except Exception as e:
    print(f'❌ Erro ao analisar controle_produtos.xlsx: {e}')
    print()

# 2. ANÁLISE DO CONTROLE DE FATURAMENTO
print('📊 2. ANÁLISE DO CONTROLE DE FATURAMENTO')
print('-' * 50)
try:
    df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
    print(f'✅ Arquivo carregado: controle_faturamento_geral.xlsx')
    print(f'📋 Total de registros: {len(df_faturamento):,}')
    print(f'📋 Total de colunas: {len(df_faturamento.columns)}')
    print()
    
    # Estatísticas de faturamento por unidade
    if 'Unidade' in df_faturamento.columns:
        print('💳 FATURAMENTO POR UNIDADE:')
        fat_stats = df_faturamento.groupby('Unidade').agg({
            'Numero NF': 'nunique',
            'Valor Total NF': lambda x: limpar_valor(x).sum()
        }).round(2)
        fat_stats = fat_stats.sort_values('Valor Total NF', ascending=False)
        
        for unidade, row in fat_stats.iterrows():
            print(f'   📍 {unidade}: {formatar_numero(row["Numero NF"])} NFes - {formatar_moeda(row["Valor Total NF"])}')
        print()
        
        # Totais de faturamento
        total_fat_nfes = df_faturamento['Numero NF'].nunique()
        total_fat_valor = limpar_valor(df_faturamento['Valor Total NF']).sum()
        
        print('💰 TOTAIS GERAIS - FATURAMENTO:')
        print(f'   📄 Total de NFes únicas: {formatar_numero(total_fat_nfes)}')
        print(f'   💰 Valor total: {formatar_moeda(total_fat_valor)}')
        print()
        
except Exception as e:
    print(f'❌ Erro ao analisar controle_faturamento_geral.xlsx: {e}')
    print()

# 3. ANÁLISE DO CONTROLE DE BOLETOS
print('📊 3. ANÁLISE DO CONTROLE DE BOLETOS')
print('-' * 50)
try:
    df_boletos = pd.read_excel('controle_boletos.xlsx')
    print(f'✅ Arquivo carregado: controle_boletos.xlsx')
    print(f'📋 Total de registros: {len(df_boletos):,}')
    print()
    
    if 'Unidade' in df_boletos.columns:
        boletos_por_unidade = df_boletos['Unidade'].value_counts()
        print('🧾 BOLETOS POR UNIDADE:')
        for unidade, count in boletos_por_unidade.items():
            print(f'   📍 {unidade}: {formatar_numero(count)} boletos')
        print()
        
        print(f'🧾 TOTAL DE BOLETOS: {formatar_numero(len(df_boletos))}')
        print()
        
except Exception as e:
    print(f'❌ Erro ao analisar controle_boletos.xlsx: {e}')
    print()

print('🔍 ANÁLISE CONCLUÍDA!')
