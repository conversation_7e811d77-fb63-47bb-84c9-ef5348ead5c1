r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Insights
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values

from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class CallSummariesInstance(InstanceResource):

    class AnsweredBy(object):
        UNKNOWN = "unknown"
        MACHINE_START = "machine_start"
        MACHINE_END_BEEP = "machine_end_beep"
        MACHINE_END_SILENCE = "machine_end_silence"
        MACHINE_END_OTHER = "machine_end_other"
        HUMAN = "human"
        FAX = "fax"

    class CallState(object):
        RINGING = "ringing"
        COMPLETED = "completed"
        BUSY = "busy"
        FAIL = "fail"
        NOANSWER = "noanswer"
        CANCELED = "canceled"
        ANSWERED = "answered"
        UNDIALED = "undialed"

    class CallType(object):
        CARRIER = "carrier"
        SIP = "sip"
        TRUNKING = "trunking"
        CLIENT = "client"
        WHATSAPP = "whatsapp"

    class ProcessingState(object):
        COMPLETE = "complete"
        PARTIAL = "partial"

    class ProcessingStateRequest(object):
        COMPLETED = "completed"
        STARTED = "started"
        PARTIAL = "partial"
        ALL = "all"

    class SortBy(object):
        START_TIME = "start_time"
        END_TIME = "end_time"

    """
    :ivar account_sid: The unique SID identifier of the Account.
    :ivar call_sid: The unique SID identifier of the Call.
    :ivar answered_by: 
    :ivar call_type: 
    :ivar call_state: 
    :ivar processing_state: 
    :ivar created_time: The time at which the Call was created, given in ISO 8601 format. Can be different from `start_time` in the event of queueing due to CPS
    :ivar start_time: The time at which the Call was started, given in ISO 8601 format.
    :ivar end_time: The time at which the Call was ended, given in ISO 8601 format.
    :ivar duration: Duration between when the call was initiated and the call was ended
    :ivar connect_duration: Duration between when the call was answered and when it ended
    :ivar _from: The calling party.
    :ivar to: The called party.
    :ivar carrier_edge: Contains metrics and properties for the Twilio media gateway of a PSTN call.
    :ivar client_edge: Contains metrics and properties for the Twilio media gateway of a Client call.
    :ivar sdk_edge: Contains metrics and properties for the SDK sensor library for Client calls.
    :ivar sip_edge: Contains metrics and properties for the Twilio media gateway of a SIP Interface or Trunking call.
    :ivar tags: Tags applied to calls by Voice Insights analysis indicating a condition that could result in subjective degradation of the call quality.
    :ivar url: The URL of this resource.
    :ivar attributes: Attributes capturing call-flow-specific details.
    :ivar properties: Contains edge-agnostic call-level details.
    :ivar trust: Contains trusted communications details including Branded Call and verified caller ID.
    :ivar annotation: 
    """

    def __init__(self, version: Version, payload: Dict[str, Any]):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.call_sid: Optional[str] = payload.get("call_sid")
        self.answered_by: Optional["CallSummariesInstance.AnsweredBy"] = payload.get(
            "answered_by"
        )
        self.call_type: Optional["CallSummariesInstance.CallType"] = payload.get(
            "call_type"
        )
        self.call_state: Optional["CallSummariesInstance.CallState"] = payload.get(
            "call_state"
        )
        self.processing_state: Optional["CallSummariesInstance.ProcessingState"] = (
            payload.get("processing_state")
        )
        self.created_time: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("created_time")
        )
        self.start_time: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("start_time")
        )
        self.end_time: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("end_time")
        )
        self.duration: Optional[int] = deserialize.integer(payload.get("duration"))
        self.connect_duration: Optional[int] = deserialize.integer(
            payload.get("connect_duration")
        )
        self._from: Optional[Dict[str, object]] = payload.get("from")
        self.to: Optional[Dict[str, object]] = payload.get("to")
        self.carrier_edge: Optional[Dict[str, object]] = payload.get("carrier_edge")
        self.client_edge: Optional[Dict[str, object]] = payload.get("client_edge")
        self.sdk_edge: Optional[Dict[str, object]] = payload.get("sdk_edge")
        self.sip_edge: Optional[Dict[str, object]] = payload.get("sip_edge")
        self.tags: Optional[List[str]] = payload.get("tags")
        self.url: Optional[str] = payload.get("url")
        self.attributes: Optional[Dict[str, object]] = payload.get("attributes")
        self.properties: Optional[Dict[str, object]] = payload.get("properties")
        self.trust: Optional[Dict[str, object]] = payload.get("trust")
        self.annotation: Optional[Dict[str, object]] = payload.get("annotation")

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """

        return "<Twilio.Insights.V1.CallSummariesInstance>"


class CallSummariesPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> CallSummariesInstance:
        """
        Build an instance of CallSummariesInstance

        :param payload: Payload response from the API
        """
        return CallSummariesInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Insights.V1.CallSummariesPage>"


class CallSummariesList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the CallSummariesList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Voice/Summaries"

    def stream(
        self,
        from_: Union[str, object] = values.unset,
        to: Union[str, object] = values.unset,
        from_carrier: Union[str, object] = values.unset,
        to_carrier: Union[str, object] = values.unset,
        from_country_code: Union[str, object] = values.unset,
        to_country_code: Union[str, object] = values.unset,
        verified_caller: Union[bool, object] = values.unset,
        has_tag: Union[bool, object] = values.unset,
        start_time: Union[str, object] = values.unset,
        end_time: Union[str, object] = values.unset,
        call_type: Union[str, object] = values.unset,
        call_state: Union[str, object] = values.unset,
        direction: Union[str, object] = values.unset,
        processing_state: Union[
            "CallSummariesInstance.ProcessingStateRequest", object
        ] = values.unset,
        sort_by: Union["CallSummariesInstance.SortBy", object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        abnormal_session: Union[bool, object] = values.unset,
        answered_by: Union["CallSummariesInstance.AnsweredBy", object] = values.unset,
        answered_by_annotation: Union[str, object] = values.unset,
        connectivity_issue_annotation: Union[str, object] = values.unset,
        quality_issue_annotation: Union[str, object] = values.unset,
        spam_annotation: Union[bool, object] = values.unset,
        call_score_annotation: Union[str, object] = values.unset,
        branded_enabled: Union[bool, object] = values.unset,
        voice_integrity_enabled: Union[bool, object] = values.unset,
        branded_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_use_case: Union[str, object] = values.unset,
        business_profile_identity: Union[str, object] = values.unset,
        business_profile_industry: Union[str, object] = values.unset,
        business_profile_bundle_sid: Union[str, object] = values.unset,
        business_profile_type: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[CallSummariesInstance]:
        """
        Streams CallSummariesInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str from_: A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param str to: A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param str from_carrier: An origination carrier.
        :param str to_carrier: A destination carrier.
        :param str from_country_code: A source country code based on phone number in From.
        :param str to_country_code: A destination country code. Based on phone number in To.
        :param bool verified_caller: A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.One of 'true' or 'false'.
        :param bool has_tag: A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
        :param str start_time: A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
        :param str end_time: An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
        :param str call_type: A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
        :param str call_state: A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
        :param str direction: A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
        :param &quot;CallSummariesInstance.ProcessingStateRequest&quot; processing_state: A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
        :param &quot;CallSummariesInstance.SortBy&quot; sort_by: A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
        :param str subaccount: A unique SID identifier of a Subaccount.
        :param bool abnormal_session: A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
        :param &quot;CallSummariesInstance.AnsweredBy&quot; answered_by: An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
        :param str answered_by_annotation: Either machine or human.
        :param str connectivity_issue_annotation: A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
        :param str quality_issue_annotation: A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
        :param bool spam_annotation: A boolean flag indicating spam calls.
        :param str call_score_annotation: A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
        :param bool branded_enabled: A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls. One of 'true' or 'false'
        :param bool voice_integrity_enabled: A boolean flag indicating whether or not the phone number had voice integrity enabled.One of 'true' or 'false'
        :param str branded_bundle_sid: A unique SID identifier of the Branded Call.
        :param str voice_integrity_bundle_sid: A unique SID identifier of the Voice Integrity Profile.
        :param str voice_integrity_use_case: A Voice Integrity Use Case . Is of type enum. One of 'abandoned_cart', 'appointment_reminders', 'appointment_scheduling', 'asset_management', 'automated_support', 'call_tracking', 'click_to_call', 'contact_tracing', 'contactless_delivery', 'customer_support', 'dating/social', 'delivery_notifications', 'distance_learning', 'emergency_notifications', 'employee_notifications', 'exam_proctoring', 'field_notifications', 'first_responder', 'fraud_alerts', 'group_messaging', 'identify_&_verification', 'intelligent_routing', 'lead_alerts', 'lead_distribution', 'lead_generation', 'lead_management', 'lead_nurturing', 'marketing_events', 'mass_alerts', 'meetings/collaboration', 'order_notifications', 'outbound_dialer', 'pharmacy', 'phone_system', 'purchase_confirmation', 'remote_appointments', 'rewards_program', 'self-service', 'service_alerts', 'shift_management', 'survey/research', 'telehealth', 'telemarketing', 'therapy_(individual+group)'.
        :param str business_profile_identity: A Business Identity of the calls. Is of type enum. One of 'direct_customer', 'isv_reseller_or_partner'.
        :param str business_profile_industry: A Business Industry of the calls. Is of type enum. One of 'automotive', 'agriculture', 'banking', 'consumer', 'construction', 'education', 'engineering', 'energy', 'oil_and_gas', 'fast_moving_consumer_goods', 'financial', 'fintech', 'food_and_beverage', 'government', 'healthcare', 'hospitality', 'insurance', 'legal', 'manufacturing', 'media', 'online', 'professional_services', 'raw_materials', 'real_estate', 'religion', 'retail', 'jewelry', 'technology', 'telecommunications', 'transportation', 'travel', 'electronics', 'not_for_profit'
        :param str business_profile_bundle_sid: A unique SID identifier of the Business Profile.
        :param str business_profile_type: A Business Profile Type of the calls. Is of type enum. One of 'primary', 'secondary'.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            from_=from_,
            to=to,
            from_carrier=from_carrier,
            to_carrier=to_carrier,
            from_country_code=from_country_code,
            to_country_code=to_country_code,
            verified_caller=verified_caller,
            has_tag=has_tag,
            start_time=start_time,
            end_time=end_time,
            call_type=call_type,
            call_state=call_state,
            direction=direction,
            processing_state=processing_state,
            sort_by=sort_by,
            subaccount=subaccount,
            abnormal_session=abnormal_session,
            answered_by=answered_by,
            answered_by_annotation=answered_by_annotation,
            connectivity_issue_annotation=connectivity_issue_annotation,
            quality_issue_annotation=quality_issue_annotation,
            spam_annotation=spam_annotation,
            call_score_annotation=call_score_annotation,
            branded_enabled=branded_enabled,
            voice_integrity_enabled=voice_integrity_enabled,
            branded_bundle_sid=branded_bundle_sid,
            voice_integrity_bundle_sid=voice_integrity_bundle_sid,
            voice_integrity_use_case=voice_integrity_use_case,
            business_profile_identity=business_profile_identity,
            business_profile_industry=business_profile_industry,
            business_profile_bundle_sid=business_profile_bundle_sid,
            business_profile_type=business_profile_type,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        from_: Union[str, object] = values.unset,
        to: Union[str, object] = values.unset,
        from_carrier: Union[str, object] = values.unset,
        to_carrier: Union[str, object] = values.unset,
        from_country_code: Union[str, object] = values.unset,
        to_country_code: Union[str, object] = values.unset,
        verified_caller: Union[bool, object] = values.unset,
        has_tag: Union[bool, object] = values.unset,
        start_time: Union[str, object] = values.unset,
        end_time: Union[str, object] = values.unset,
        call_type: Union[str, object] = values.unset,
        call_state: Union[str, object] = values.unset,
        direction: Union[str, object] = values.unset,
        processing_state: Union[
            "CallSummariesInstance.ProcessingStateRequest", object
        ] = values.unset,
        sort_by: Union["CallSummariesInstance.SortBy", object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        abnormal_session: Union[bool, object] = values.unset,
        answered_by: Union["CallSummariesInstance.AnsweredBy", object] = values.unset,
        answered_by_annotation: Union[str, object] = values.unset,
        connectivity_issue_annotation: Union[str, object] = values.unset,
        quality_issue_annotation: Union[str, object] = values.unset,
        spam_annotation: Union[bool, object] = values.unset,
        call_score_annotation: Union[str, object] = values.unset,
        branded_enabled: Union[bool, object] = values.unset,
        voice_integrity_enabled: Union[bool, object] = values.unset,
        branded_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_use_case: Union[str, object] = values.unset,
        business_profile_identity: Union[str, object] = values.unset,
        business_profile_industry: Union[str, object] = values.unset,
        business_profile_bundle_sid: Union[str, object] = values.unset,
        business_profile_type: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[CallSummariesInstance]:
        """
        Asynchronously streams CallSummariesInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str from_: A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param str to: A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param str from_carrier: An origination carrier.
        :param str to_carrier: A destination carrier.
        :param str from_country_code: A source country code based on phone number in From.
        :param str to_country_code: A destination country code. Based on phone number in To.
        :param bool verified_caller: A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.One of 'true' or 'false'.
        :param bool has_tag: A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
        :param str start_time: A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
        :param str end_time: An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
        :param str call_type: A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
        :param str call_state: A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
        :param str direction: A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
        :param &quot;CallSummariesInstance.ProcessingStateRequest&quot; processing_state: A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
        :param &quot;CallSummariesInstance.SortBy&quot; sort_by: A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
        :param str subaccount: A unique SID identifier of a Subaccount.
        :param bool abnormal_session: A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
        :param &quot;CallSummariesInstance.AnsweredBy&quot; answered_by: An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
        :param str answered_by_annotation: Either machine or human.
        :param str connectivity_issue_annotation: A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
        :param str quality_issue_annotation: A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
        :param bool spam_annotation: A boolean flag indicating spam calls.
        :param str call_score_annotation: A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
        :param bool branded_enabled: A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls. One of 'true' or 'false'
        :param bool voice_integrity_enabled: A boolean flag indicating whether or not the phone number had voice integrity enabled.One of 'true' or 'false'
        :param str branded_bundle_sid: A unique SID identifier of the Branded Call.
        :param str voice_integrity_bundle_sid: A unique SID identifier of the Voice Integrity Profile.
        :param str voice_integrity_use_case: A Voice Integrity Use Case . Is of type enum. One of 'abandoned_cart', 'appointment_reminders', 'appointment_scheduling', 'asset_management', 'automated_support', 'call_tracking', 'click_to_call', 'contact_tracing', 'contactless_delivery', 'customer_support', 'dating/social', 'delivery_notifications', 'distance_learning', 'emergency_notifications', 'employee_notifications', 'exam_proctoring', 'field_notifications', 'first_responder', 'fraud_alerts', 'group_messaging', 'identify_&_verification', 'intelligent_routing', 'lead_alerts', 'lead_distribution', 'lead_generation', 'lead_management', 'lead_nurturing', 'marketing_events', 'mass_alerts', 'meetings/collaboration', 'order_notifications', 'outbound_dialer', 'pharmacy', 'phone_system', 'purchase_confirmation', 'remote_appointments', 'rewards_program', 'self-service', 'service_alerts', 'shift_management', 'survey/research', 'telehealth', 'telemarketing', 'therapy_(individual+group)'.
        :param str business_profile_identity: A Business Identity of the calls. Is of type enum. One of 'direct_customer', 'isv_reseller_or_partner'.
        :param str business_profile_industry: A Business Industry of the calls. Is of type enum. One of 'automotive', 'agriculture', 'banking', 'consumer', 'construction', 'education', 'engineering', 'energy', 'oil_and_gas', 'fast_moving_consumer_goods', 'financial', 'fintech', 'food_and_beverage', 'government', 'healthcare', 'hospitality', 'insurance', 'legal', 'manufacturing', 'media', 'online', 'professional_services', 'raw_materials', 'real_estate', 'religion', 'retail', 'jewelry', 'technology', 'telecommunications', 'transportation', 'travel', 'electronics', 'not_for_profit'
        :param str business_profile_bundle_sid: A unique SID identifier of the Business Profile.
        :param str business_profile_type: A Business Profile Type of the calls. Is of type enum. One of 'primary', 'secondary'.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            from_=from_,
            to=to,
            from_carrier=from_carrier,
            to_carrier=to_carrier,
            from_country_code=from_country_code,
            to_country_code=to_country_code,
            verified_caller=verified_caller,
            has_tag=has_tag,
            start_time=start_time,
            end_time=end_time,
            call_type=call_type,
            call_state=call_state,
            direction=direction,
            processing_state=processing_state,
            sort_by=sort_by,
            subaccount=subaccount,
            abnormal_session=abnormal_session,
            answered_by=answered_by,
            answered_by_annotation=answered_by_annotation,
            connectivity_issue_annotation=connectivity_issue_annotation,
            quality_issue_annotation=quality_issue_annotation,
            spam_annotation=spam_annotation,
            call_score_annotation=call_score_annotation,
            branded_enabled=branded_enabled,
            voice_integrity_enabled=voice_integrity_enabled,
            branded_bundle_sid=branded_bundle_sid,
            voice_integrity_bundle_sid=voice_integrity_bundle_sid,
            voice_integrity_use_case=voice_integrity_use_case,
            business_profile_identity=business_profile_identity,
            business_profile_industry=business_profile_industry,
            business_profile_bundle_sid=business_profile_bundle_sid,
            business_profile_type=business_profile_type,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        from_: Union[str, object] = values.unset,
        to: Union[str, object] = values.unset,
        from_carrier: Union[str, object] = values.unset,
        to_carrier: Union[str, object] = values.unset,
        from_country_code: Union[str, object] = values.unset,
        to_country_code: Union[str, object] = values.unset,
        verified_caller: Union[bool, object] = values.unset,
        has_tag: Union[bool, object] = values.unset,
        start_time: Union[str, object] = values.unset,
        end_time: Union[str, object] = values.unset,
        call_type: Union[str, object] = values.unset,
        call_state: Union[str, object] = values.unset,
        direction: Union[str, object] = values.unset,
        processing_state: Union[
            "CallSummariesInstance.ProcessingStateRequest", object
        ] = values.unset,
        sort_by: Union["CallSummariesInstance.SortBy", object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        abnormal_session: Union[bool, object] = values.unset,
        answered_by: Union["CallSummariesInstance.AnsweredBy", object] = values.unset,
        answered_by_annotation: Union[str, object] = values.unset,
        connectivity_issue_annotation: Union[str, object] = values.unset,
        quality_issue_annotation: Union[str, object] = values.unset,
        spam_annotation: Union[bool, object] = values.unset,
        call_score_annotation: Union[str, object] = values.unset,
        branded_enabled: Union[bool, object] = values.unset,
        voice_integrity_enabled: Union[bool, object] = values.unset,
        branded_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_use_case: Union[str, object] = values.unset,
        business_profile_identity: Union[str, object] = values.unset,
        business_profile_industry: Union[str, object] = values.unset,
        business_profile_bundle_sid: Union[str, object] = values.unset,
        business_profile_type: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[CallSummariesInstance]:
        """
        Lists CallSummariesInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str from_: A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param str to: A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param str from_carrier: An origination carrier.
        :param str to_carrier: A destination carrier.
        :param str from_country_code: A source country code based on phone number in From.
        :param str to_country_code: A destination country code. Based on phone number in To.
        :param bool verified_caller: A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.One of 'true' or 'false'.
        :param bool has_tag: A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
        :param str start_time: A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
        :param str end_time: An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
        :param str call_type: A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
        :param str call_state: A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
        :param str direction: A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
        :param &quot;CallSummariesInstance.ProcessingStateRequest&quot; processing_state: A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
        :param &quot;CallSummariesInstance.SortBy&quot; sort_by: A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
        :param str subaccount: A unique SID identifier of a Subaccount.
        :param bool abnormal_session: A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
        :param &quot;CallSummariesInstance.AnsweredBy&quot; answered_by: An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
        :param str answered_by_annotation: Either machine or human.
        :param str connectivity_issue_annotation: A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
        :param str quality_issue_annotation: A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
        :param bool spam_annotation: A boolean flag indicating spam calls.
        :param str call_score_annotation: A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
        :param bool branded_enabled: A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls. One of 'true' or 'false'
        :param bool voice_integrity_enabled: A boolean flag indicating whether or not the phone number had voice integrity enabled.One of 'true' or 'false'
        :param str branded_bundle_sid: A unique SID identifier of the Branded Call.
        :param str voice_integrity_bundle_sid: A unique SID identifier of the Voice Integrity Profile.
        :param str voice_integrity_use_case: A Voice Integrity Use Case . Is of type enum. One of 'abandoned_cart', 'appointment_reminders', 'appointment_scheduling', 'asset_management', 'automated_support', 'call_tracking', 'click_to_call', 'contact_tracing', 'contactless_delivery', 'customer_support', 'dating/social', 'delivery_notifications', 'distance_learning', 'emergency_notifications', 'employee_notifications', 'exam_proctoring', 'field_notifications', 'first_responder', 'fraud_alerts', 'group_messaging', 'identify_&_verification', 'intelligent_routing', 'lead_alerts', 'lead_distribution', 'lead_generation', 'lead_management', 'lead_nurturing', 'marketing_events', 'mass_alerts', 'meetings/collaboration', 'order_notifications', 'outbound_dialer', 'pharmacy', 'phone_system', 'purchase_confirmation', 'remote_appointments', 'rewards_program', 'self-service', 'service_alerts', 'shift_management', 'survey/research', 'telehealth', 'telemarketing', 'therapy_(individual+group)'.
        :param str business_profile_identity: A Business Identity of the calls. Is of type enum. One of 'direct_customer', 'isv_reseller_or_partner'.
        :param str business_profile_industry: A Business Industry of the calls. Is of type enum. One of 'automotive', 'agriculture', 'banking', 'consumer', 'construction', 'education', 'engineering', 'energy', 'oil_and_gas', 'fast_moving_consumer_goods', 'financial', 'fintech', 'food_and_beverage', 'government', 'healthcare', 'hospitality', 'insurance', 'legal', 'manufacturing', 'media', 'online', 'professional_services', 'raw_materials', 'real_estate', 'religion', 'retail', 'jewelry', 'technology', 'telecommunications', 'transportation', 'travel', 'electronics', 'not_for_profit'
        :param str business_profile_bundle_sid: A unique SID identifier of the Business Profile.
        :param str business_profile_type: A Business Profile Type of the calls. Is of type enum. One of 'primary', 'secondary'.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                from_=from_,
                to=to,
                from_carrier=from_carrier,
                to_carrier=to_carrier,
                from_country_code=from_country_code,
                to_country_code=to_country_code,
                verified_caller=verified_caller,
                has_tag=has_tag,
                start_time=start_time,
                end_time=end_time,
                call_type=call_type,
                call_state=call_state,
                direction=direction,
                processing_state=processing_state,
                sort_by=sort_by,
                subaccount=subaccount,
                abnormal_session=abnormal_session,
                answered_by=answered_by,
                answered_by_annotation=answered_by_annotation,
                connectivity_issue_annotation=connectivity_issue_annotation,
                quality_issue_annotation=quality_issue_annotation,
                spam_annotation=spam_annotation,
                call_score_annotation=call_score_annotation,
                branded_enabled=branded_enabled,
                voice_integrity_enabled=voice_integrity_enabled,
                branded_bundle_sid=branded_bundle_sid,
                voice_integrity_bundle_sid=voice_integrity_bundle_sid,
                voice_integrity_use_case=voice_integrity_use_case,
                business_profile_identity=business_profile_identity,
                business_profile_industry=business_profile_industry,
                business_profile_bundle_sid=business_profile_bundle_sid,
                business_profile_type=business_profile_type,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        from_: Union[str, object] = values.unset,
        to: Union[str, object] = values.unset,
        from_carrier: Union[str, object] = values.unset,
        to_carrier: Union[str, object] = values.unset,
        from_country_code: Union[str, object] = values.unset,
        to_country_code: Union[str, object] = values.unset,
        verified_caller: Union[bool, object] = values.unset,
        has_tag: Union[bool, object] = values.unset,
        start_time: Union[str, object] = values.unset,
        end_time: Union[str, object] = values.unset,
        call_type: Union[str, object] = values.unset,
        call_state: Union[str, object] = values.unset,
        direction: Union[str, object] = values.unset,
        processing_state: Union[
            "CallSummariesInstance.ProcessingStateRequest", object
        ] = values.unset,
        sort_by: Union["CallSummariesInstance.SortBy", object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        abnormal_session: Union[bool, object] = values.unset,
        answered_by: Union["CallSummariesInstance.AnsweredBy", object] = values.unset,
        answered_by_annotation: Union[str, object] = values.unset,
        connectivity_issue_annotation: Union[str, object] = values.unset,
        quality_issue_annotation: Union[str, object] = values.unset,
        spam_annotation: Union[bool, object] = values.unset,
        call_score_annotation: Union[str, object] = values.unset,
        branded_enabled: Union[bool, object] = values.unset,
        voice_integrity_enabled: Union[bool, object] = values.unset,
        branded_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_use_case: Union[str, object] = values.unset,
        business_profile_identity: Union[str, object] = values.unset,
        business_profile_industry: Union[str, object] = values.unset,
        business_profile_bundle_sid: Union[str, object] = values.unset,
        business_profile_type: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[CallSummariesInstance]:
        """
        Asynchronously lists CallSummariesInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str from_: A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param str to: A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param str from_carrier: An origination carrier.
        :param str to_carrier: A destination carrier.
        :param str from_country_code: A source country code based on phone number in From.
        :param str to_country_code: A destination country code. Based on phone number in To.
        :param bool verified_caller: A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.One of 'true' or 'false'.
        :param bool has_tag: A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
        :param str start_time: A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
        :param str end_time: An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
        :param str call_type: A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
        :param str call_state: A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
        :param str direction: A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
        :param &quot;CallSummariesInstance.ProcessingStateRequest&quot; processing_state: A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
        :param &quot;CallSummariesInstance.SortBy&quot; sort_by: A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
        :param str subaccount: A unique SID identifier of a Subaccount.
        :param bool abnormal_session: A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
        :param &quot;CallSummariesInstance.AnsweredBy&quot; answered_by: An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
        :param str answered_by_annotation: Either machine or human.
        :param str connectivity_issue_annotation: A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
        :param str quality_issue_annotation: A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
        :param bool spam_annotation: A boolean flag indicating spam calls.
        :param str call_score_annotation: A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
        :param bool branded_enabled: A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls. One of 'true' or 'false'
        :param bool voice_integrity_enabled: A boolean flag indicating whether or not the phone number had voice integrity enabled.One of 'true' or 'false'
        :param str branded_bundle_sid: A unique SID identifier of the Branded Call.
        :param str voice_integrity_bundle_sid: A unique SID identifier of the Voice Integrity Profile.
        :param str voice_integrity_use_case: A Voice Integrity Use Case . Is of type enum. One of 'abandoned_cart', 'appointment_reminders', 'appointment_scheduling', 'asset_management', 'automated_support', 'call_tracking', 'click_to_call', 'contact_tracing', 'contactless_delivery', 'customer_support', 'dating/social', 'delivery_notifications', 'distance_learning', 'emergency_notifications', 'employee_notifications', 'exam_proctoring', 'field_notifications', 'first_responder', 'fraud_alerts', 'group_messaging', 'identify_&_verification', 'intelligent_routing', 'lead_alerts', 'lead_distribution', 'lead_generation', 'lead_management', 'lead_nurturing', 'marketing_events', 'mass_alerts', 'meetings/collaboration', 'order_notifications', 'outbound_dialer', 'pharmacy', 'phone_system', 'purchase_confirmation', 'remote_appointments', 'rewards_program', 'self-service', 'service_alerts', 'shift_management', 'survey/research', 'telehealth', 'telemarketing', 'therapy_(individual+group)'.
        :param str business_profile_identity: A Business Identity of the calls. Is of type enum. One of 'direct_customer', 'isv_reseller_or_partner'.
        :param str business_profile_industry: A Business Industry of the calls. Is of type enum. One of 'automotive', 'agriculture', 'banking', 'consumer', 'construction', 'education', 'engineering', 'energy', 'oil_and_gas', 'fast_moving_consumer_goods', 'financial', 'fintech', 'food_and_beverage', 'government', 'healthcare', 'hospitality', 'insurance', 'legal', 'manufacturing', 'media', 'online', 'professional_services', 'raw_materials', 'real_estate', 'religion', 'retail', 'jewelry', 'technology', 'telecommunications', 'transportation', 'travel', 'electronics', 'not_for_profit'
        :param str business_profile_bundle_sid: A unique SID identifier of the Business Profile.
        :param str business_profile_type: A Business Profile Type of the calls. Is of type enum. One of 'primary', 'secondary'.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                from_=from_,
                to=to,
                from_carrier=from_carrier,
                to_carrier=to_carrier,
                from_country_code=from_country_code,
                to_country_code=to_country_code,
                verified_caller=verified_caller,
                has_tag=has_tag,
                start_time=start_time,
                end_time=end_time,
                call_type=call_type,
                call_state=call_state,
                direction=direction,
                processing_state=processing_state,
                sort_by=sort_by,
                subaccount=subaccount,
                abnormal_session=abnormal_session,
                answered_by=answered_by,
                answered_by_annotation=answered_by_annotation,
                connectivity_issue_annotation=connectivity_issue_annotation,
                quality_issue_annotation=quality_issue_annotation,
                spam_annotation=spam_annotation,
                call_score_annotation=call_score_annotation,
                branded_enabled=branded_enabled,
                voice_integrity_enabled=voice_integrity_enabled,
                branded_bundle_sid=branded_bundle_sid,
                voice_integrity_bundle_sid=voice_integrity_bundle_sid,
                voice_integrity_use_case=voice_integrity_use_case,
                business_profile_identity=business_profile_identity,
                business_profile_industry=business_profile_industry,
                business_profile_bundle_sid=business_profile_bundle_sid,
                business_profile_type=business_profile_type,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        from_: Union[str, object] = values.unset,
        to: Union[str, object] = values.unset,
        from_carrier: Union[str, object] = values.unset,
        to_carrier: Union[str, object] = values.unset,
        from_country_code: Union[str, object] = values.unset,
        to_country_code: Union[str, object] = values.unset,
        verified_caller: Union[bool, object] = values.unset,
        has_tag: Union[bool, object] = values.unset,
        start_time: Union[str, object] = values.unset,
        end_time: Union[str, object] = values.unset,
        call_type: Union[str, object] = values.unset,
        call_state: Union[str, object] = values.unset,
        direction: Union[str, object] = values.unset,
        processing_state: Union[
            "CallSummariesInstance.ProcessingStateRequest", object
        ] = values.unset,
        sort_by: Union["CallSummariesInstance.SortBy", object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        abnormal_session: Union[bool, object] = values.unset,
        answered_by: Union["CallSummariesInstance.AnsweredBy", object] = values.unset,
        answered_by_annotation: Union[str, object] = values.unset,
        connectivity_issue_annotation: Union[str, object] = values.unset,
        quality_issue_annotation: Union[str, object] = values.unset,
        spam_annotation: Union[bool, object] = values.unset,
        call_score_annotation: Union[str, object] = values.unset,
        branded_enabled: Union[bool, object] = values.unset,
        voice_integrity_enabled: Union[bool, object] = values.unset,
        branded_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_use_case: Union[str, object] = values.unset,
        business_profile_identity: Union[str, object] = values.unset,
        business_profile_industry: Union[str, object] = values.unset,
        business_profile_bundle_sid: Union[str, object] = values.unset,
        business_profile_type: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> CallSummariesPage:
        """
        Retrieve a single page of CallSummariesInstance records from the API.
        Request is executed immediately

        :param from_: A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param to: A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param from_carrier: An origination carrier.
        :param to_carrier: A destination carrier.
        :param from_country_code: A source country code based on phone number in From.
        :param to_country_code: A destination country code. Based on phone number in To.
        :param verified_caller: A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.One of 'true' or 'false'.
        :param has_tag: A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
        :param start_time: A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
        :param end_time: An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
        :param call_type: A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
        :param call_state: A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
        :param direction: A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
        :param processing_state: A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
        :param sort_by: A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
        :param subaccount: A unique SID identifier of a Subaccount.
        :param abnormal_session: A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
        :param answered_by: An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
        :param answered_by_annotation: Either machine or human.
        :param connectivity_issue_annotation: A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
        :param quality_issue_annotation: A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
        :param spam_annotation: A boolean flag indicating spam calls.
        :param call_score_annotation: A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
        :param branded_enabled: A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls. One of 'true' or 'false'
        :param voice_integrity_enabled: A boolean flag indicating whether or not the phone number had voice integrity enabled.One of 'true' or 'false'
        :param branded_bundle_sid: A unique SID identifier of the Branded Call.
        :param voice_integrity_bundle_sid: A unique SID identifier of the Voice Integrity Profile.
        :param voice_integrity_use_case: A Voice Integrity Use Case . Is of type enum. One of 'abandoned_cart', 'appointment_reminders', 'appointment_scheduling', 'asset_management', 'automated_support', 'call_tracking', 'click_to_call', 'contact_tracing', 'contactless_delivery', 'customer_support', 'dating/social', 'delivery_notifications', 'distance_learning', 'emergency_notifications', 'employee_notifications', 'exam_proctoring', 'field_notifications', 'first_responder', 'fraud_alerts', 'group_messaging', 'identify_&_verification', 'intelligent_routing', 'lead_alerts', 'lead_distribution', 'lead_generation', 'lead_management', 'lead_nurturing', 'marketing_events', 'mass_alerts', 'meetings/collaboration', 'order_notifications', 'outbound_dialer', 'pharmacy', 'phone_system', 'purchase_confirmation', 'remote_appointments', 'rewards_program', 'self-service', 'service_alerts', 'shift_management', 'survey/research', 'telehealth', 'telemarketing', 'therapy_(individual+group)'.
        :param business_profile_identity: A Business Identity of the calls. Is of type enum. One of 'direct_customer', 'isv_reseller_or_partner'.
        :param business_profile_industry: A Business Industry of the calls. Is of type enum. One of 'automotive', 'agriculture', 'banking', 'consumer', 'construction', 'education', 'engineering', 'energy', 'oil_and_gas', 'fast_moving_consumer_goods', 'financial', 'fintech', 'food_and_beverage', 'government', 'healthcare', 'hospitality', 'insurance', 'legal', 'manufacturing', 'media', 'online', 'professional_services', 'raw_materials', 'real_estate', 'religion', 'retail', 'jewelry', 'technology', 'telecommunications', 'transportation', 'travel', 'electronics', 'not_for_profit'
        :param business_profile_bundle_sid: A unique SID identifier of the Business Profile.
        :param business_profile_type: A Business Profile Type of the calls. Is of type enum. One of 'primary', 'secondary'.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of CallSummariesInstance
        """
        data = values.of(
            {
                "From": from_,
                "To": to,
                "FromCarrier": from_carrier,
                "ToCarrier": to_carrier,
                "FromCountryCode": from_country_code,
                "ToCountryCode": to_country_code,
                "VerifiedCaller": serialize.boolean_to_string(verified_caller),
                "HasTag": serialize.boolean_to_string(has_tag),
                "StartTime": start_time,
                "EndTime": end_time,
                "CallType": call_type,
                "CallState": call_state,
                "Direction": direction,
                "ProcessingState": processing_state,
                "SortBy": sort_by,
                "Subaccount": subaccount,
                "AbnormalSession": serialize.boolean_to_string(abnormal_session),
                "AnsweredBy": answered_by,
                "AnsweredByAnnotation": answered_by_annotation,
                "ConnectivityIssueAnnotation": connectivity_issue_annotation,
                "QualityIssueAnnotation": quality_issue_annotation,
                "SpamAnnotation": serialize.boolean_to_string(spam_annotation),
                "CallScoreAnnotation": call_score_annotation,
                "BrandedEnabled": serialize.boolean_to_string(branded_enabled),
                "VoiceIntegrityEnabled": serialize.boolean_to_string(
                    voice_integrity_enabled
                ),
                "BrandedBundleSid": branded_bundle_sid,
                "VoiceIntegrityBundleSid": voice_integrity_bundle_sid,
                "VoiceIntegrityUseCase": voice_integrity_use_case,
                "BusinessProfileIdentity": business_profile_identity,
                "BusinessProfileIndustry": business_profile_industry,
                "BusinessProfileBundleSid": business_profile_bundle_sid,
                "BusinessProfileType": business_profile_type,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return CallSummariesPage(self._version, response)

    async def page_async(
        self,
        from_: Union[str, object] = values.unset,
        to: Union[str, object] = values.unset,
        from_carrier: Union[str, object] = values.unset,
        to_carrier: Union[str, object] = values.unset,
        from_country_code: Union[str, object] = values.unset,
        to_country_code: Union[str, object] = values.unset,
        verified_caller: Union[bool, object] = values.unset,
        has_tag: Union[bool, object] = values.unset,
        start_time: Union[str, object] = values.unset,
        end_time: Union[str, object] = values.unset,
        call_type: Union[str, object] = values.unset,
        call_state: Union[str, object] = values.unset,
        direction: Union[str, object] = values.unset,
        processing_state: Union[
            "CallSummariesInstance.ProcessingStateRequest", object
        ] = values.unset,
        sort_by: Union["CallSummariesInstance.SortBy", object] = values.unset,
        subaccount: Union[str, object] = values.unset,
        abnormal_session: Union[bool, object] = values.unset,
        answered_by: Union["CallSummariesInstance.AnsweredBy", object] = values.unset,
        answered_by_annotation: Union[str, object] = values.unset,
        connectivity_issue_annotation: Union[str, object] = values.unset,
        quality_issue_annotation: Union[str, object] = values.unset,
        spam_annotation: Union[bool, object] = values.unset,
        call_score_annotation: Union[str, object] = values.unset,
        branded_enabled: Union[bool, object] = values.unset,
        voice_integrity_enabled: Union[bool, object] = values.unset,
        branded_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_bundle_sid: Union[str, object] = values.unset,
        voice_integrity_use_case: Union[str, object] = values.unset,
        business_profile_identity: Union[str, object] = values.unset,
        business_profile_industry: Union[str, object] = values.unset,
        business_profile_bundle_sid: Union[str, object] = values.unset,
        business_profile_type: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> CallSummariesPage:
        """
        Asynchronously retrieve a single page of CallSummariesInstance records from the API.
        Request is executed immediately

        :param from_: A calling party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param to: A called party. Could be an E.164 number, a SIP URI, or a Twilio Client registered name.
        :param from_carrier: An origination carrier.
        :param to_carrier: A destination carrier.
        :param from_country_code: A source country code based on phone number in From.
        :param to_country_code: A destination country code. Based on phone number in To.
        :param verified_caller: A boolean flag indicating whether or not the caller was verified using SHAKEN/STIR.One of 'true' or 'false'.
        :param has_tag: A boolean flag indicating the presence of one or more [Voice Insights Call Tags](https://www.twilio.com/docs/voice/voice-insights/api/call/details-call-tags).
        :param start_time: A Start time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 4h.
        :param end_time: An End Time of the calls. xm (x minutes), xh (x hours), xd (x days), 1w, 30m, 3d, 4w or datetime-ISO. Defaults to 0m.
        :param call_type: A Call Type of the calls. One of `carrier`, `sip`, `trunking` or `client`.
        :param call_state: A Call State of the calls. One of `ringing`, `completed`, `busy`, `fail`, `noanswer`, `canceled`, `answered`, `undialed`.
        :param direction: A Direction of the calls. One of `outbound_api`, `outbound_dial`, `inbound`, `trunking_originating`, `trunking_terminating`.
        :param processing_state: A Processing State of the Call Summaries. One of `completed`, `partial` or `all`.
        :param sort_by: A Sort By criterion for the returned list of Call Summaries. One of `start_time` or `end_time`.
        :param subaccount: A unique SID identifier of a Subaccount.
        :param abnormal_session: A boolean flag indicating an abnormal session where the last SIP response was not 200 OK.
        :param answered_by: An Answered By value for the calls based on `Answering Machine Detection (AMD)`. One of `unknown`, `machine_start`, `machine_end_beep`, `machine_end_silence`, `machine_end_other`, `human` or `fax`.
        :param answered_by_annotation: Either machine or human.
        :param connectivity_issue_annotation: A Connectivity Issue with the calls. One of `no_connectivity_issue`, `invalid_number`, `caller_id`, `dropped_call`, or `number_reachability`.
        :param quality_issue_annotation: A subjective Quality Issue with the calls. One of `no_quality_issue`, `low_volume`, `choppy_robotic`, `echo`, `dtmf`, `latency`, `owa`, `static_noise`.
        :param spam_annotation: A boolean flag indicating spam calls.
        :param call_score_annotation: A Call Score of the calls. Use a range of 1-5 to indicate the call experience score, with the following mapping as a reference for the rated call [5: Excellent, 4: Good, 3 : Fair, 2 : Poor, 1: Bad].
        :param branded_enabled: A boolean flag indicating whether or not the calls were branded using Twilio Branded Calls. One of 'true' or 'false'
        :param voice_integrity_enabled: A boolean flag indicating whether or not the phone number had voice integrity enabled.One of 'true' or 'false'
        :param branded_bundle_sid: A unique SID identifier of the Branded Call.
        :param voice_integrity_bundle_sid: A unique SID identifier of the Voice Integrity Profile.
        :param voice_integrity_use_case: A Voice Integrity Use Case . Is of type enum. One of 'abandoned_cart', 'appointment_reminders', 'appointment_scheduling', 'asset_management', 'automated_support', 'call_tracking', 'click_to_call', 'contact_tracing', 'contactless_delivery', 'customer_support', 'dating/social', 'delivery_notifications', 'distance_learning', 'emergency_notifications', 'employee_notifications', 'exam_proctoring', 'field_notifications', 'first_responder', 'fraud_alerts', 'group_messaging', 'identify_&_verification', 'intelligent_routing', 'lead_alerts', 'lead_distribution', 'lead_generation', 'lead_management', 'lead_nurturing', 'marketing_events', 'mass_alerts', 'meetings/collaboration', 'order_notifications', 'outbound_dialer', 'pharmacy', 'phone_system', 'purchase_confirmation', 'remote_appointments', 'rewards_program', 'self-service', 'service_alerts', 'shift_management', 'survey/research', 'telehealth', 'telemarketing', 'therapy_(individual+group)'.
        :param business_profile_identity: A Business Identity of the calls. Is of type enum. One of 'direct_customer', 'isv_reseller_or_partner'.
        :param business_profile_industry: A Business Industry of the calls. Is of type enum. One of 'automotive', 'agriculture', 'banking', 'consumer', 'construction', 'education', 'engineering', 'energy', 'oil_and_gas', 'fast_moving_consumer_goods', 'financial', 'fintech', 'food_and_beverage', 'government', 'healthcare', 'hospitality', 'insurance', 'legal', 'manufacturing', 'media', 'online', 'professional_services', 'raw_materials', 'real_estate', 'religion', 'retail', 'jewelry', 'technology', 'telecommunications', 'transportation', 'travel', 'electronics', 'not_for_profit'
        :param business_profile_bundle_sid: A unique SID identifier of the Business Profile.
        :param business_profile_type: A Business Profile Type of the calls. Is of type enum. One of 'primary', 'secondary'.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of CallSummariesInstance
        """
        data = values.of(
            {
                "From": from_,
                "To": to,
                "FromCarrier": from_carrier,
                "ToCarrier": to_carrier,
                "FromCountryCode": from_country_code,
                "ToCountryCode": to_country_code,
                "VerifiedCaller": serialize.boolean_to_string(verified_caller),
                "HasTag": serialize.boolean_to_string(has_tag),
                "StartTime": start_time,
                "EndTime": end_time,
                "CallType": call_type,
                "CallState": call_state,
                "Direction": direction,
                "ProcessingState": processing_state,
                "SortBy": sort_by,
                "Subaccount": subaccount,
                "AbnormalSession": serialize.boolean_to_string(abnormal_session),
                "AnsweredBy": answered_by,
                "AnsweredByAnnotation": answered_by_annotation,
                "ConnectivityIssueAnnotation": connectivity_issue_annotation,
                "QualityIssueAnnotation": quality_issue_annotation,
                "SpamAnnotation": serialize.boolean_to_string(spam_annotation),
                "CallScoreAnnotation": call_score_annotation,
                "BrandedEnabled": serialize.boolean_to_string(branded_enabled),
                "VoiceIntegrityEnabled": serialize.boolean_to_string(
                    voice_integrity_enabled
                ),
                "BrandedBundleSid": branded_bundle_sid,
                "VoiceIntegrityBundleSid": voice_integrity_bundle_sid,
                "VoiceIntegrityUseCase": voice_integrity_use_case,
                "BusinessProfileIdentity": business_profile_identity,
                "BusinessProfileIndustry": business_profile_industry,
                "BusinessProfileBundleSid": business_profile_bundle_sid,
                "BusinessProfileType": business_profile_type,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return CallSummariesPage(self._version, response)

    def get_page(self, target_url: str) -> CallSummariesPage:
        """
        Retrieve a specific page of CallSummariesInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of CallSummariesInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return CallSummariesPage(self._version, response)

    async def get_page_async(self, target_url: str) -> CallSummariesPage:
        """
        Asynchronously retrieve a specific page of CallSummariesInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of CallSummariesInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return CallSummariesPage(self._version, response)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Insights.V1.CallSummariesList>"
