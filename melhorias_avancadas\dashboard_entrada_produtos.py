#!/usr/bin/env python3
"""
🎯 DASHBOARD ENTRADA DE PRODUTOS - SANTACLARA
Sistema completo de análise de entrada de produtos com:
- Lista detalhada de produtos
- Top produtos por quantidade e valor
- Filtros avançados
- Análise de preços e variação histórica
- KPIs e gráficos interativos
"""

import pandas as pd
import numpy as np
from flask import Flask, render_template, jsonify, request
from datetime import datetime, timedelta
import os
import json
from collections import defaultdict
import re

app = Flask(__name__)

class DashboardEntradaProdutos:
    def __init__(self):
        self.df_produtos = None
        self.produtos_padronizados = {}
        self.carregar_dados()
    
    def carregar_dados(self):
        """Carrega dados de produtos"""
        print("🎯 CARREGANDO DADOS PARA DASHBOARD DE ENTRADA DE PRODUTOS")
        print("=" * 60)
        
        try:
            # Carregar dados de produtos
            if os.path.exists('controle_produtos.xlsx'):
                self.df_produtos = pd.read_excel('controle_produtos.xlsx')
                self.processar_dados_produtos()
                print(f"✅ Produtos carregados: {len(self.df_produtos):,} registros")
            else:
                print("❌ Arquivo controle_produtos.xlsx não encontrado")
                
        except Exception as e:
            print(f"❌ Erro ao carregar dados: {e}")
    
    def processar_dados_produtos(self):
        """Processa dados de produtos para análises"""
        if self.df_produtos is not None:
            # Converter valores
            self.df_produtos['Valor_Total_Numerico'] = self.df_produtos['Valor Total Item'].apply(self.converter_valor_brasileiro)
            self.df_produtos['Quantidade_Numerica'] = self.df_produtos['Quantidade'].apply(self.converter_quantidade_brasileira)
            self.df_produtos['Data_Emissao_Dt'] = pd.to_datetime(self.df_produtos['Data Emissao'], format='%d/%m/%Y', errors='coerce')
            
            # Calcular valor unitário
            self.df_produtos['Valor_Unitario'] = np.where(
                self.df_produtos['Quantidade_Numerica'] > 0,
                self.df_produtos['Valor_Total_Numerico'] / self.df_produtos['Quantidade_Numerica'],
                0
            )
            
            # Padronizar produtos
            print("🔄 Padronizando produtos...")
            self.df_produtos['Produto_Padronizado'] = self.df_produtos['Descricao Produto'].apply(self.padronizar_produto)
            
            # Adicionar análises temporais
            self.df_produtos['Ano_Mes'] = self.df_produtos['Data_Emissao_Dt'].dt.to_period('M')
            self.df_produtos['Ano'] = self.df_produtos['Data_Emissao_Dt'].dt.year
            self.df_produtos['Mes'] = self.df_produtos['Data_Emissao_Dt'].dt.month
            
            print(f"✅ Produtos únicos padronizados: {self.df_produtos['Produto_Padronizado'].nunique()}")
    
    def converter_valor_brasileiro(self, valor):
        """Converte valor brasileiro para float"""
        try:
            if pd.isna(valor) or valor == '':
                return 0.0
            valor_str = str(valor).replace('R$', '').replace(' ', '').replace('.', '').replace(',', '.')
            return float(valor_str)
        except:
            return 0.0
    
    def converter_quantidade_brasileira(self, quantidade):
        """Converte quantidade brasileira para float"""
        try:
            if pd.isna(quantidade) or quantidade == '':
                return 0.0
            qtd_str = str(quantidade).replace('.', '').replace(',', '.')
            return float(qtd_str)
        except:
            return 0.0
    
    def padronizar_produto(self, descricao):
        """Padroniza nome do produto usando mapeamento SantaClara"""
        if pd.isna(descricao):
            return 'PRODUTO NÃO IDENTIFICADO'
        
        descricao = str(descricao).upper().strip()
        
        # Mapeamento de produtos SantaClara (baseado na lista fornecida)
        mapeamento = {
            'TRIAXIM': ['TRIAXIM', 'TRIAXIM 1G', 'TRIAXIM 1 GRAMA'],
            'ABRYSVO': ['ABRYSVO'],
            'ADACEL TRIPLICE': ['ADACEL', 'TRIPLICE ACELULAR'],
            'AJOVY': ['AJOVY'],
            'ANTIPNEUMOCOCICA - PREVENAR': ['PREVENAR', 'PNEUMOCOCICA PREVENAR'],
            'ANTIPNEUMOCOCICA 15 VALENTE': ['PNEUMO 15', 'PNEUMOCOCICA 15V'],
            'ANTIPNEUMOCOCICA 20': ['PNEUMO 20', 'PNEUMOCOCICA 20V'],
            'ANTIPNEUMOCOCICA 23': ['PNEUMO 23', 'PNEUMOCOCICA 23V'],
            'AREXVY': ['AREXVY'],
            'BEXSERO': ['BEXSERO'],
            'BOOSTRIX': ['BOOSTRIX'],
            'CERVARIX': ['CERVARIX'],
            'COMIRNATY': ['COMIRNATY', 'PFIZER'],
            'DENGVAXIA': ['DENGVAXIA'],
            'DUPLA ADULTO': ['DUPLA ADULTO', 'DT ADULTO'],
            'FEBRE AMARELA': ['FEBRE AMARELA', 'FA'],
            'FLUARIX TETRA': ['FLUARIX', 'INFLUENZA TETRA'],
            'GARDASIL 9': ['GARDASIL', 'HPV'],
            'HAVRIX': ['HAVRIX', 'HEPATITE A'],
            'HBVAXPRO': ['HBVAXPRO', 'HEPATITE B'],
            'HEXAXIM': ['HEXAXIM'],
            'IMOVAX': ['IMOVAX', 'RAIVA'],
            'INFLUVAC': ['INFLUVAC', 'INFLUENZA'],
            'IXIARO': ['IXIARO', 'ENCEFALITE JAPONESA'],
            'MENACTRA': ['MENACTRA', 'MENINGITE ACWY'],
            'MENVEO': ['MENVEO', 'MENINGITE'],
            'NIMENRIX': ['NIMENRIX'],
            'PENTAVALENTE': ['PENTAVALENTE', 'DTP'],
            'PNEUMOVAX': ['PNEUMOVAX'],
            'QDENGA': ['QDENGA', 'DENGUE'],
            'ROTARIX': ['ROTARIX', 'ROTAVIRUS'],
            'ROTATEQ': ['ROTATEQ'],
            'SHINGRIX': ['SHINGRIX', 'HERPES ZOSTER'],
            'SYNFLORIX': ['SYNFLORIX'],
            'TRESIVAC': ['TRESIVAC', 'TRIPLICE VIRAL'],
            'TWINRIX': ['TWINRIX', 'HEPATITE A+B'],
            'TYPHIM': ['TYPHIM', 'FEBRE TIFOIDE'],
            'VARICELA': ['VARICELA', 'CATAPORA'],
            'VARILRIX': ['VARILRIX'],
            'VIVOTIF': ['VIVOTIF'],
            'ZOSTAVAX': ['ZOSTAVAX']
        }
        
        # Buscar correspondência
        for produto_padrao, variacoes in mapeamento.items():
            for variacao in variacoes:
                if variacao in descricao:
                    return produto_padrao
        
        # Se não encontrou, retornar descrição limpa
        descricao_limpa = re.sub(r'[^\w\s]', ' ', descricao)
        descricao_limpa = re.sub(r'\s+', ' ', descricao_limpa).strip()
        
        # Se muito longa, pegar primeiras palavras importantes
        palavras = descricao_limpa.split()
        if len(palavras) > 3:
            return ' '.join(palavras[:3])
        
        return descricao_limpa if descricao_limpa else 'PRODUTO NÃO IDENTIFICADO'
    
    def get_lista_produtos(self, filtros=None):
        """Retorna lista detalhada de produtos com filtros"""
        if self.df_produtos is None:
            return []
        
        df_temp = self.df_produtos.copy()
        
        # Aplicar filtros
        if filtros:
            if filtros.get('unidade') and filtros['unidade'] != 'TODAS':
                df_temp = df_temp[df_temp['Unidade'] == filtros['unidade']]
            
            if filtros.get('data_inicio'):
                data_inicio = pd.to_datetime(filtros['data_inicio'])
                df_temp = df_temp[df_temp['Data_Emissao_Dt'] >= data_inicio]
            
            if filtros.get('data_fim'):
                data_fim = pd.to_datetime(filtros['data_fim'])
                df_temp = df_temp[df_temp['Data_Emissao_Dt'] <= data_fim]
            
            if filtros.get('produto'):
                df_temp = df_temp[df_temp['Produto_Padronizado'].str.contains(filtros['produto'], case=False, na=False)]
            
            if filtros.get('fornecedor'):
                df_temp = df_temp[df_temp['Fornecedor'].str.contains(filtros['fornecedor'], case=False, na=False)]
            
            if filtros.get('numero_nf'):
                df_temp = df_temp[df_temp['Numero Nota'].astype(str).str.contains(str(filtros['numero_nf']), na=False)]
        
        # Preparar dados para retorno
        produtos_lista = []
        for _, row in df_temp.iterrows():
            produtos_lista.append({
                'unidade': row['Unidade'],
                'data': row['Data Emissao'],
                'numero_nf': row['Numero Nota'],
                'fornecedor': row['Fornecedor'],
                'produto': row['Produto_Padronizado'],
                'produto_original': row['Descricao Produto'],
                'quantidade': row['Quantidade_Numerica'],
                'valor_unitario': row['Valor_Unitario'],
                'valor_total': row['Valor_Total_Numerico'],
                'valor_unitario_formatado': f"R$ {row['Valor_Unitario']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
                'valor_total_formatado': f"R$ {row['Valor_Total_Numerico']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
            })
        
        return produtos_lista
    
    def get_top_produtos_quantidade(self, limit=10, filtros=None):
        """Top produtos por quantidade"""
        if self.df_produtos is None:
            return {'labels': [], 'data': [], 'detalhes': []}
        
        df_temp = self.df_produtos.copy()
        
        # Aplicar filtros se fornecidos
        if filtros:
            if filtros.get('unidade') and filtros['unidade'] != 'TODAS':
                df_temp = df_temp[df_temp['Unidade'] == filtros['unidade']]
        
        # Agrupar por produto padronizado
        top_produtos = df_temp.groupby('Produto_Padronizado').agg({
            'Quantidade_Numerica': 'sum',
            'Valor_Total_Numerico': 'sum',
            'Numero Nota': 'count'
        }).round(2)
        
        top_produtos.columns = ['Total_Quantidade', 'Total_Valor', 'Total_Compras']
        top_produtos = top_produtos.sort_values('Total_Quantidade', ascending=False).head(limit)
        
        return {
            'labels': list(top_produtos.index),
            'data': [float(qtd) for qtd in top_produtos['Total_Quantidade'].values],
            'valores': [float(val) for val in top_produtos['Total_Valor'].values],
            'compras': [int(comp) for comp in top_produtos['Total_Compras'].values]
        }
    
    def get_top_produtos_valor(self, limit=10, filtros=None):
        """Top produtos por valor total"""
        if self.df_produtos is None:
            return {'labels': [], 'data': [], 'detalhes': []}
        
        df_temp = self.df_produtos.copy()
        
        # Aplicar filtros se fornecidos
        if filtros:
            if filtros.get('unidade') and filtros['unidade'] != 'TODAS':
                df_temp = df_temp[df_temp['Unidade'] == filtros['unidade']]
        
        # Agrupar por produto padronizado
        top_produtos = df_temp.groupby('Produto_Padronizado').agg({
            'Valor_Total_Numerico': 'sum',
            'Quantidade_Numerica': 'sum',
            'Numero Nota': 'count'
        }).round(2)
        
        top_produtos.columns = ['Total_Valor', 'Total_Quantidade', 'Total_Compras']
        top_produtos = top_produtos.sort_values('Total_Valor', ascending=False).head(limit)
        
        return {
            'labels': list(top_produtos.index),
            'data': [float(val) for val in top_produtos['Total_Valor'].values],
            'quantidades': [float(qtd) for qtd in top_produtos['Total_Quantidade'].values],
            'compras': [int(comp) for comp in top_produtos['Total_Compras'].values]
        }

    def get_analise_precos(self, produto=None, periodo_meses=12):
        """Análise de preços médios e variação histórica"""
        if self.df_produtos is None:
            return {'erro': 'Dados não disponíveis'}

        df_temp = self.df_produtos.copy()

        # Filtrar por produto se especificado
        if produto and produto != 'TODOS':
            df_temp = df_temp[df_temp['Produto_Padronizado'] == produto]

        if len(df_temp) == 0:
            return {'erro': 'Nenhum dado encontrado'}

        # Análise por mês
        analise_mensal = df_temp.groupby(['Ano_Mes', 'Produto_Padronizado']).agg({
            'Valor_Unitario': ['mean', 'min', 'max', 'count'],
            'Quantidade_Numerica': 'sum',
            'Valor_Total_Numerico': 'sum'
        }).round(2)

        # Achatar colunas
        analise_mensal.columns = ['Preco_Medio', 'Preco_Min', 'Preco_Max', 'Compras', 'Quantidade_Total', 'Valor_Total']
        analise_mensal = analise_mensal.reset_index()

        # Converter período para string
        analise_mensal['Periodo'] = analise_mensal['Ano_Mes'].astype(str)

        # Últimos meses
        analise_mensal = analise_mensal.sort_values('Ano_Mes').tail(periodo_meses * len(df_temp['Produto_Padronizado'].unique()))

        return {
            'dados_mensais': analise_mensal.to_dict('records'),
            'produtos_unicos': list(df_temp['Produto_Padronizado'].unique()),
            'periodo_analisado': f"Últimos {periodo_meses} meses"
        }

    def get_kpis_produtos(self, filtros=None):
        """KPIs principais de produtos"""
        if self.df_produtos is None:
            return {}

        df_temp = self.df_produtos.copy()

        # Aplicar filtros
        if filtros:
            if filtros.get('unidade') and filtros['unidade'] != 'TODAS':
                df_temp = df_temp[df_temp['Unidade'] == filtros['unidade']]

        # Calcular KPIs
        total_produtos = len(df_temp)
        produtos_unicos = df_temp['Produto_Padronizado'].nunique()
        valor_total = df_temp['Valor_Total_Numerico'].sum()
        quantidade_total = df_temp['Quantidade_Numerica'].sum()
        fornecedores_unicos = df_temp['Fornecedor'].nunique()
        unidades_ativas = df_temp['Unidade'].nunique()

        # Ticket médio
        ticket_medio = valor_total / total_produtos if total_produtos > 0 else 0

        # Produto mais caro
        produto_mais_caro = df_temp.loc[df_temp['Valor_Unitario'].idxmax()] if len(df_temp) > 0 else None

        # Maior compra
        maior_compra = df_temp.loc[df_temp['Valor_Total_Numerico'].idxmax()] if len(df_temp) > 0 else None

        return {
            'total_produtos': total_produtos,
            'produtos_unicos': produtos_unicos,
            'valor_total': valor_total,
            'valor_total_formatado': f"R$ {valor_total:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'quantidade_total': quantidade_total,
            'fornecedores_unicos': fornecedores_unicos,
            'unidades_ativas': unidades_ativas,
            'ticket_medio': ticket_medio,
            'ticket_medio_formatado': f"R$ {ticket_medio:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'produto_mais_caro': {
                'nome': produto_mais_caro['Produto_Padronizado'] if produto_mais_caro is not None else 'N/A',
                'valor': produto_mais_caro['Valor_Unitario'] if produto_mais_caro is not None else 0,
                'valor_formatado': f"R$ {produto_mais_caro['Valor_Unitario']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.') if produto_mais_caro is not None else 'R$ 0,00'
            },
            'maior_compra': {
                'produto': maior_compra['Produto_Padronizado'] if maior_compra is not None else 'N/A',
                'valor': maior_compra['Valor_Total_Numerico'] if maior_compra is not None else 0,
                'valor_formatado': f"R$ {maior_compra['Valor_Total_Numerico']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.') if maior_compra is not None else 'R$ 0,00',
                'fornecedor': maior_compra['Fornecedor'] if maior_compra is not None else 'N/A'
            }
        }

    def get_produtos_por_mes(self, ano=None):
        """Lista de produtos e preços médios por mês"""
        if self.df_produtos is None:
            return {}

        df_temp = self.df_produtos.copy()

        # Filtrar por ano se especificado
        if ano:
            df_temp = df_temp[df_temp['Ano'] == int(ano)]

        # Agrupar por mês e produto
        produtos_mes = df_temp.groupby(['Ano', 'Mes', 'Produto_Padronizado']).agg({
            'Valor_Unitario': 'mean',
            'Quantidade_Numerica': 'sum',
            'Valor_Total_Numerico': 'sum',
            'Numero Nota': 'count'
        }).round(2)

        produtos_mes.columns = ['Preco_Medio', 'Quantidade_Total', 'Valor_Total', 'Compras']
        produtos_mes = produtos_mes.reset_index()

        # Criar estrutura por mês
        resultado = {}
        for _, row in produtos_mes.iterrows():
            mes_key = f"{int(row['Ano'])}-{int(row['Mes']):02d}"
            if mes_key not in resultado:
                resultado[mes_key] = []

            resultado[mes_key].append({
                'produto': row['Produto_Padronizado'],
                'preco_medio': row['Preco_Medio'],
                'preco_medio_formatado': f"R$ {row['Preco_Medio']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
                'quantidade_total': row['Quantidade_Total'],
                'valor_total': row['Valor_Total'],
                'valor_total_formatado': f"R$ {row['Valor_Total']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
                'compras': int(row['Compras'])
            })

        return resultado

    def get_unidades_disponiveis(self):
        """Lista de unidades disponíveis"""
        if self.df_produtos is None:
            return []
        return ['TODAS'] + sorted(self.df_produtos['Unidade'].unique().tolist())

    def get_produtos_disponiveis(self):
        """Lista de produtos padronizados disponíveis"""
        if self.df_produtos is None:
            return []
        return ['TODOS'] + sorted(self.df_produtos['Produto_Padronizado'].unique().tolist())

    def get_fornecedores_disponiveis(self):
        """Lista de fornecedores disponíveis"""
        if self.df_produtos is None:
            return []
        return sorted(self.df_produtos['Fornecedor'].unique().tolist())

# Instância global
dashboard_produtos = DashboardEntradaProdutos()

# ROTAS DA API
@app.route('/')
def dashboard_principal():
    """Página principal do dashboard de entrada de produtos"""
    return render_template('dashboard_entrada_produtos.html')

@app.route('/api/lista-produtos')
def api_lista_produtos():
    """API para lista de produtos com filtros"""
    filtros = {
        'unidade': request.args.get('unidade'),
        'data_inicio': request.args.get('data_inicio'),
        'data_fim': request.args.get('data_fim'),
        'produto': request.args.get('produto'),
        'fornecedor': request.args.get('fornecedor'),
        'numero_nf': request.args.get('numero_nf')
    }

    # Remover filtros vazios
    filtros = {k: v for k, v in filtros.items() if v and v != ''}

    return jsonify(dashboard_produtos.get_lista_produtos(filtros))

@app.route('/api/top-produtos-quantidade')
def api_top_produtos_quantidade():
    """API para top produtos por quantidade"""
    limit = int(request.args.get('limit', 10))
    unidade = request.args.get('unidade')

    filtros = {'unidade': unidade} if unidade else None

    return jsonify(dashboard_produtos.get_top_produtos_quantidade(limit, filtros))

@app.route('/api/top-produtos-valor')
def api_top_produtos_valor():
    """API para top produtos por valor"""
    limit = int(request.args.get('limit', 10))
    unidade = request.args.get('unidade')

    filtros = {'unidade': unidade} if unidade else None

    return jsonify(dashboard_produtos.get_top_produtos_valor(limit, filtros))

@app.route('/api/analise-precos')
def api_analise_precos():
    """API para análise de preços"""
    produto = request.args.get('produto')
    periodo = int(request.args.get('periodo', 12))

    return jsonify(dashboard_produtos.get_analise_precos(produto, periodo))

@app.route('/api/kpis-produtos')
def api_kpis_produtos():
    """API para KPIs de produtos"""
    unidade = request.args.get('unidade')
    filtros = {'unidade': unidade} if unidade else None

    return jsonify(dashboard_produtos.get_kpis_produtos(filtros))

@app.route('/api/produtos-por-mes')
def api_produtos_por_mes():
    """API para produtos por mês"""
    ano = request.args.get('ano')
    return jsonify(dashboard_produtos.get_produtos_por_mes(ano))

@app.route('/api/opcoes-filtros')
def api_opcoes_filtros():
    """API para opções de filtros"""
    return jsonify({
        'unidades': dashboard_produtos.get_unidades_disponiveis(),
        'produtos': dashboard_produtos.get_produtos_disponiveis(),
        'fornecedores': dashboard_produtos.get_fornecedores_disponiveis()
    })

if __name__ == '__main__':
    print("🎯 INICIANDO DASHBOARD DE ENTRADA DE PRODUTOS")
    print("=" * 60)
    print("📊 Dashboard disponível em: http://localhost:5001")
    print("🔄 Dados carregados e processados")
    print("📱 Interface responsiva e interativa")
    print("=" * 60)

    app.run(debug=True, host='0.0.0.0', port=5001)
