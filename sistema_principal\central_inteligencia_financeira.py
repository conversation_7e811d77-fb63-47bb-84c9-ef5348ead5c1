#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CENTRAL DE INTELIGÊNCIA FINANCEIRA
Sistema integrado de análise, alertas e relatórios
"""

import os
import sys
from datetime import datetime
import subprocess

# Importar módulos do sistema
try:
    from dashboard_financeiro import DashboardFinanceiro
    from sistema_alertas import SistemaAlertas
    from relatorios_gerenciais import GeradorRelatorios
except ImportError as e:
    print(f"❌ Erro ao importar módulos: {e}")
    print("Certifique-se de que todos os arquivos estão no mesmo diretório")
    sys.exit(1)

class CentralInteligenciaFinanceira:
    def __init__(self):
        self.dashboard = None
        self.sistema_alertas = None
        self.gerador_relatorios = None
        self.inicializar_componentes()
    
    def inicializar_componentes(self):
        """Inicializa todos os componentes do sistema"""
        try:
            print("🚀 INICIALIZANDO CENTRAL DE INTELIGÊNCIA FINANCEIRA")
            print("="*60)
            
            self.dashboard = DashboardFinanceiro()
            self.sistema_alertas = SistemaAlertas()
            self.gerador_relatorios = GeradorRelatorios()
            
            print("✅ Todos os componentes inicializados com sucesso!")
            
        except Exception as e:
            print(f"❌ Erro ao inicializar componentes: {e}")
    
    def executar_processamento_emails(self, modo='normal'):
        """Executa processamento de e-mails"""
        print(f"\n📧 EXECUTANDO PROCESSAMENTO DE E-MAILS - MODO: {modo.upper()}")
        print("-" * 50)
        
        try:
            if modo == 'reprocessamento':
                print("🔄 Iniciando reprocessamento de 5000 e-mails...")
                resultado = subprocess.run([sys.executable, 'reprocessar_5000_emails.py'], 
                                         capture_output=True, text=True, timeout=3600)
            else:
                print("📬 Processando e-mails não lidos...")
                resultado = subprocess.run([sys.executable, 'automacao_nf.py'], 
                                         input='1\n', capture_output=True, text=True, timeout=1800)
            
            if resultado.returncode == 0:
                print("✅ Processamento de e-mails concluído com sucesso!")
                return True
            else:
                print(f"❌ Erro no processamento: {resultado.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("⏰ Timeout no processamento de e-mails")
            return False
        except Exception as e:
            print(f"❌ Erro ao executar processamento: {e}")
            return False
    
    def executar_analise_completa(self):
        """Executa análise completa do sistema"""
        print(f"\n🔍 EXECUTANDO ANÁLISE COMPLETA")
        print("="*60)
        
        # 1. Dashboard Financeiro
        print("\n1️⃣ DASHBOARD FINANCEIRO:")
        try:
            self.dashboard.gerar_dashboard_completo()
        except Exception as e:
            print(f"❌ Erro no dashboard: {e}")
        
        # 2. Sistema de Alertas
        print("\n2️⃣ SISTEMA DE ALERTAS:")
        try:
            self.sistema_alertas.executar_monitoramento_completo()
        except Exception as e:
            print(f"❌ Erro no sistema de alertas: {e}")
        
        # 3. Relatórios Gerenciais
        print("\n3️⃣ RELATÓRIOS GERENCIAIS:")
        try:
            self.gerador_relatorios.gerar_relatorio_completo()
        except Exception as e:
            print(f"❌ Erro nos relatórios: {e}")
    
    def gerar_resumo_executivo(self):
        """Gera resumo executivo consolidado"""
        print(f"\n📊 RESUMO EXECUTIVO CONSOLIDADO")
        print("="*60)
        print(f"📅 Data/Hora: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        
        # Verificar status dos dados
        status_dados = self.verificar_status_dados()
        
        print(f"\n📋 STATUS DOS DADOS:")
        for arquivo, status in status_dados.items():
            emoji = "✅" if status['existe'] else "❌"
            print(f"   {emoji} {arquivo}: {status['registros']} registros")
        
        # Estatísticas rápidas
        if status_dados['controle_faturamento_geral.xlsx']['existe']:
            try:
                import pandas as pd
                df = pd.read_excel('controle_faturamento_geral.xlsx')
                
                # Converter valores para análise
                df['Valor_Numerico'] = df['Valor Total NF'].apply(self.converter_valor_brasileiro)
                
                valor_total = df['Valor_Numerico'].sum()
                unidades_ativas = df['Unidade'].nunique()
                fornecedores = df['Fornecedor'].nunique()
                
                print(f"\n💰 ESTATÍSTICAS GERAIS:")
                print(f"   💵 Valor Total Processado: R$ {valor_total:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'))
                print(f"   🏪 Unidades Ativas: {unidades_ativas}")
                print(f"   🏭 Fornecedores Únicos: {fornecedores}")
                
            except Exception as e:
                print(f"   ❌ Erro ao calcular estatísticas: {e}")
    
    def converter_valor_brasileiro(self, valor):
        """Converte valor brasileiro para float"""
        try:
            if pd.isna(valor) or valor == '':
                return 0.0
            valor_str = str(valor).replace('R$', '').replace(' ', '').replace('.', '').replace(',', '.')
            return float(valor_str)
        except:
            return 0.0
    
    def verificar_status_dados(self):
        """Verifica status dos arquivos de dados"""
        arquivos = [
            'controle_produtos.xlsx',
            'controle_faturamento_geral.xlsx',
            'controle_boletos.xlsx'
        ]
        
        status = {}
        
        for arquivo in arquivos:
            if os.path.exists(arquivo):
                try:
                    import pandas as pd
                    df = pd.read_excel(arquivo)
                    status[arquivo] = {
                        'existe': True,
                        'registros': len(df),
                        'tamanho': f"{os.path.getsize(arquivo) / 1024:.1f} KB"
                    }
                except Exception as e:
                    status[arquivo] = {
                        'existe': True,
                        'registros': f"Erro: {e}",
                        'tamanho': f"{os.path.getsize(arquivo) / 1024:.1f} KB"
                    }
            else:
                status[arquivo] = {
                    'existe': False,
                    'registros': 0,
                    'tamanho': "0 KB"
                }
        
        return status
    
    def menu_principal(self):
        """Menu principal do sistema"""
        while True:
            print(f"\n🏢 CENTRAL DE INTELIGÊNCIA FINANCEIRA")
            print("="*60)
            print("1. 📧 Processar e-mails não lidos")
            print("2. 🔄 Reprocessar 5000 e-mails")
            print("3. 📊 Dashboard Financeiro")
            print("4. 🚨 Sistema de Alertas")
            print("5. 📋 Relatórios Gerenciais")
            print("6. 🔍 Análise Completa")
            print("7. 📈 Resumo Executivo")
            print("8. ❌ Sair")
            
            opcao = input("\nEscolha uma opção (1-8): ").strip()
            
            if opcao == '1':
                self.executar_processamento_emails('normal')
            elif opcao == '2':
                confirmacao = input("⚠️ Reprocessar 5000 e-mails? (S/N): ").strip().upper()
                if confirmacao == 'S':
                    self.executar_processamento_emails('reprocessamento')
            elif opcao == '3':
                self.dashboard.gerar_dashboard_completo()
            elif opcao == '4':
                self.sistema_alertas.executar_monitoramento_completo()
            elif opcao == '5':
                self.gerador_relatorios.gerar_relatorio_completo()
            elif opcao == '6':
                self.executar_analise_completa()
            elif opcao == '7':
                self.gerar_resumo_executivo()
            elif opcao == '8':
                print("👋 Encerrando Central de Inteligência Financeira...")
                break
            else:
                print("❌ Opção inválida! Tente novamente.")
            
            input("\nPressione ENTER para continuar...")
    
    def executar_rotina_automatica(self):
        """Executa rotina automática diária"""
        print(f"\n🤖 EXECUTANDO ROTINA AUTOMÁTICA DIÁRIA")
        print("="*60)
        
        # 1. Processar e-mails não lidos
        print("1️⃣ Processando e-mails não lidos...")
        sucesso_emails = self.executar_processamento_emails('normal')
        
        if sucesso_emails:
            # 2. Executar análise completa
            print("2️⃣ Executando análise completa...")
            self.executar_analise_completa()
            
            # 3. Gerar resumo executivo
            print("3️⃣ Gerando resumo executivo...")
            self.gerar_resumo_executivo()
            
            print("\n🎉 ROTINA AUTOMÁTICA CONCLUÍDA COM SUCESSO!")
        else:
            print("\n❌ Falha na rotina automática - erro no processamento de e-mails")

def main():
    print("🚀 INICIANDO CENTRAL DE INTELIGÊNCIA FINANCEIRA")
    print("="*60)
    
    central = CentralInteligenciaFinanceira()
    
    # Verificar argumentos da linha de comando
    if len(sys.argv) > 1:
        if sys.argv[1] == '--auto':
            central.executar_rotina_automatica()
        elif sys.argv[1] == '--dashboard':
            central.dashboard.gerar_dashboard_completo()
        elif sys.argv[1] == '--alertas':
            central.sistema_alertas.executar_monitoramento_completo()
        elif sys.argv[1] == '--relatorios':
            central.gerador_relatorios.gerar_relatorio_completo()
        elif sys.argv[1] == '--analise':
            central.executar_analise_completa()
        else:
            print(f"❌ Argumento inválido: {sys.argv[1]}")
            print("Argumentos válidos: --auto, --dashboard, --alertas, --relatorios, --analise")
    else:
        # Menu interativo
        central.menu_principal()

if __name__ == '__main__':
    main()
