import os
import pickle
import base64 # Para decodificar os anexos
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import pandas as pd # Usaremos para a tabela de Unidades

# --- CONFIGURAÇÕES GLOBAIS ---
# ATENÇÃO: Mudamos o escopo para 'modify' para poder alterar os e-mails (ex: marcar como lido)
SCOPES = ['https://www.googleapis.com/auth/gmail.modify']

CAMINHO_CREDENCIAIS = 'credentials.json'
ARQUIVO_TOKEN = 'token.pickle'

PASTA_BASE_NOTAS = 'NotasFiscais'
PASTA_INCONSISTENCIAS = os.path.join(PASTA_BASE_NOTAS, 'INCONSISTENCIAS')

NOME_PLANILHA_CONTROLE = 'controle_notas_fiscais.xlsx'

TELEGRAM_BOT_TOKEN = 'SEU_TOKEN_AQUI'
TELEGRAM_CHAT_ID = 'SEU_CHAT_ID_AQUI'

ARQUIVO_UNIDADES_CNPJ = 'unidades_cnpjs.csv'


def autenticar_gmail():
    """Realiza a autenticação com a API do Gmail e retorna o serviço."""
    creds = None
    if os.path.exists(ARQUIVO_TOKEN):
        with open(ARQUIVO_TOKEN, 'rb') as token:
            creds = pickle.load(token)
    
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            if os.path.exists(ARQUIVO_TOKEN):
                os.remove(ARQUIVO_TOKEN)
            try:
                flow = InstalledAppFlow.from_client_secrets_file(CAMINHO_CREDENCIAIS, SCOPES)
                creds = flow.run_local_server(port=0)
            except FileNotFoundError:
                print(f"ERRO: Arquivo de credenciais '{CAMINHO_CREDENCIAIS}' não encontrado.")
                return None
        
        with open(ARQUIVO_TOKEN, 'wb') as token:
            pickle.dump(creds, token)
            print(f"Token salvo em '{ARQUIVO_TOKEN}'")

    try:
        service = build('gmail', 'v1', credentials=creds)
        print("Autenticação com Gmail bem-sucedida!")
        return service
    except HttpError as error:
        print(f'Ocorreu um erro ao construir o serviço do Gmail: {error}')
        return None

def buscar_emails_com_nfe(service, query="has:attachment filename:pdf is:unread"):
    """
    Busca e-mails que correspondem a uma query específica.
    Por padrão, busca e-mails não lidos com anexos PDF para evitar reprocessamento.
    Retorna uma lista de IDs de mensagens.
    """
    try:
        result = service.users().messages().list(userId='me', q=query).execute()
        messages = result.get('messages', [])
        return messages
    except HttpError as error:
        print(f'Ocorreu um erro ao buscar os e-mails: {error}')
        return []

def baixar_anexos(service, msg_id, pasta_destino):
    """
    Baixa os anexos PDF de um e-mail específico para uma pasta de destino.
    Retorna uma lista com os caminhos dos arquivos baixados.
    """
    caminhos_anexos_baixados = []
    try:
        message = service.users().messages().get(userId='me', id=msg_id).execute()
        parts = [message['payload']]
        while parts:
            part = parts.pop(0)
            if 'parts' in part:
                parts.extend(part['parts'])
            
            if 'filename' in part and part['filename'].lower().endswith('.pdf'):
                if 'body' in part and 'attachmentId' in part['body']:
                    attachment = service.users().messages().attachments().get(
                        userId='me', messageId=msg_id, id=part['body']['attachmentId']
                    ).execute()
                    
                    file_data = base64.urlsafe_b64decode(attachment['data'].encode('UTF-8'))
                    
                    # Garante que o nome do arquivo seja "limpo" para evitar problemas
                    nome_arquivo = "".join(c for c in part['filename'] if c.isalnum() or c in ('.', '-', '_')).rstrip()
                    caminho_arquivo = os.path.join(pasta_destino, nome_arquivo)
                    
                    with open(caminho_arquivo, 'wb') as f:
                        f.write(file_data)
                    
                    print(f"  -> Anexo '{nome_arquivo}' baixado para '{pasta_destino}'")
                    caminhos_anexos_baixados.append(caminho_arquivo)

        return caminhos_anexos_baixados

    except HttpError as error:
        print(f"  -> Erro ao baixar anexo do e-mail ID {msg_id}: {error}")
        return []

def processar_pdf(caminho_pdf):
    """(ESQUELETO) Processa o arquivo PDF para extrair os dados."""
    print(f"    -> Processando PDF: {os.path.basename(caminho_pdf)} (simulação)")
    # A lógica de extração com pdfplumber virá aqui.
    return True # Retorna True para simular sucesso

def marcar_email_como_processado(service, msg_id):
    """(ESQUELETO) Remove o marcador 'UNREAD' para não processar novamente."""
    print(f"    -> Marcando e-mail ID {msg_id} como processado.")
    try:
        service.users().messages().modify(
            userId='me', 
            id=msg_id, 
            body={'removeLabelIds': ['UNREAD']}
        ).execute()
    except HttpError as error:
        print(f"    -> Erro ao marcar e-mail como lido: {error}")


# --- Bloco Principal de Execução ---
if __name__ == '__main__':
    print("Iniciando aplicação de automação de notas fiscais...")
    
    if not os.path.exists(PASTA_BASE_NOTAS):
        os.makedirs(PASTA_BASE_NOTAS)
    if not os.path.exists(PASTA_INCONSISTENCIAS):
        os.makedirs(PASTA_INCONSISTENCIAS)

    servico_gmail = autenticar_gmail()
    
    if servico_gmail:
        print("\nBuscando e-mails com notas fiscais (PDFs não lidos)...")
        emails = buscar_emails_com_nfe(servico_gmail)
        
        if not emails:
            print("Nenhum e-mail novo com NF-e em PDF foi encontrado.")
        else:
            print(f"Encontrados {len(emails)} novos e-mails para processar.")
            for email_info in emails:
                msg_id = email_info['id']
                print(f"\n[+] Processando e-mail ID: {msg_id}")
                
                # Baixa os anexos para uma pasta temporária inicial
                caminhos_anexos = baixar_anexos(servico_gmail, msg_id, PASTA_BASE_NOTAS)
                
                if caminhos_anexos:
                    # Futuramente, iteraremos sobre cada anexo para processá-lo
                    for caminho in caminhos_anexos:
                        processar_pdf(caminho) # Por enquanto, só imprime uma mensagem

                    # Após processar todos os anexos de um e-mail com sucesso, o marcamos
                    marcar_email_como_processado(servico_gmail, msg_id)
                else:
                    print(f"  -> E-mail ID {msg_id} não continha anexos PDF válidos ou ocorreu um erro no download.")
    else:
        print("Não foi possível autenticar com o Gmail. A aplicação será encerrada.")

    print("\nFim da execução.")