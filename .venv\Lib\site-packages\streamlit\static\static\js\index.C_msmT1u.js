import{r,R as f,aE as m,b6 as l,j as t,b7 as B,bq as b,b5 as p,b8 as h}from"./index.C1z8KpLA.js";import{c as w}from"./createDownloadLinkElement.DZMwyjvU.js";function D(n,o,e){return w({enforceDownloadInNewTab:e,url:n.buildMediaURL(o),filename:""})}function R(n){const{disabled:o,element:e,widgetMgr:s,endpoints:a,fragmentId:c}=n,{libConfig:{enforceDownloadInNewTab:d=!1}}=f.useContext(m);let i=l.SECONDARY;e.type==="primary"?i=l.PRIMARY:e.type==="tertiary"&&(i=l.TERTIARY),r.useEffect(()=>{a.checkSourceUrlResponse(e.url,"Download Button")},[e.url,a]);const u=()=>{e.ignoreRerun||s.setTriggerValue(e,{fromUi:!0},c),D(a,e.url,d).click()};return t("div",{className:"stDownloadButton","data-testid":"stDownloadButton",children:t(B,{help:e.help,containerWidth:e.useContainerWidth,children:t(b,{kind:i,size:p.SMALL,disabled:o,onClick:u,containerWidth:e.useContainerWidth,children:t(h,{icon:e.icon,label:e.label})})})})}const L=r.memo(R);export{L as default};
