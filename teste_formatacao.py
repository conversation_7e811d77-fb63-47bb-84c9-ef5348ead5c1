#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TESTE DAS MELHORIAS DE FORMATAÇÃO BRASILEIRA
"""

import pandas as pd
from automacao_nf import formatar_valor_brasileiro, formatar_quantidade

def testar_formatacao():
    print("🧪 TESTANDO FORMATAÇÃO BRASILEIRA")
    print("=" * 50)
    
    # Teste formatação de valores
    print("\n💰 TESTE FORMATAÇÃO DE VALORES:")
    valores_teste = ['1234.56', '999.99', '10000', '0.50', '123456.78']
    
    for valor in valores_teste:
        formatado = formatar_valor_brasileiro(valor)
        print(f"  {valor} -> {formatado}")
    
    # Teste formatação de quantidades
    print("\n📊 TESTE FORMATAÇÃO DE QUANTIDADES:")
    quantidades_teste = ['1', '10.5', '100.123', '0.001', '1234.5678']
    
    for quantidade in quantidades_teste:
        formatado = formatar_quantidade(quantidade)
        print(f"  {quantidade} -> {formatado}")
    
    # Teste com dados reais das planilhas
    print("\n📋 TESTE COM DADOS DAS PLANILHAS:")
    
    try:
        # Planilha de produtos
        df_produtos = pd.read_excel('controle_produtos.xlsx')
        print(f"\n📦 PLANILHA PRODUTOS ({len(df_produtos)} registros):")
        print("Primeiros valores de Quantidade:", df_produtos['Quantidade'].head().tolist())
        print("Primeiros valores de Valor Unitario:", df_produtos['Valor Unitario'].head().tolist())
        print("Primeiros valores de Valor Total Item:", df_produtos['Valor Total Item'].head().tolist())
        
        # Planilha de faturamento
        df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
        print(f"\n💰 PLANILHA FATURAMENTO ({len(df_faturamento)} registros):")
        print("Primeiros valores de Valor Parcela:", df_faturamento['Valor Parcela'].head().tolist())
        print("Primeiros valores de Valor Total NF:", df_faturamento['Valor Total NF'].head().tolist())
        print("Primeiros valores de Valor Produtos:", df_faturamento['Valor Produtos'].head().tolist())
        
    except Exception as e:
        print(f"Erro ao ler planilhas: {e}")
    
    print("\n✅ TESTE CONCLUÍDO!")

if __name__ == '__main__':
    testar_formatacao()
