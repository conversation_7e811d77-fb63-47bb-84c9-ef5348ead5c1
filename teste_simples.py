#!/usr/bin/env python3
"""
🎯 TESTE SIMPLES - VERIFICAR SE PADRONIZAÇÃO ESTÁ FUNCIONANDO
"""

import requests
import json

def testar_dashboard():
    """Testa se o dashboard está funcionando e mostra dados padronizados"""
    
    print("🎯 TESTANDO DASHBOARD SANTACLARA COM PADRONIZAÇÃO")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    try:
        # 1. Testar se dashboard está rodando
        print("🔍 1. Verificando se dashboard está rodando...")
        response = requests.get(f"{base_url}/api/resumo", timeout=5)
        if response.status_code == 200:
            print("✅ Dashboard está funcionando!")
            resumo = response.json()
            print(f"   📦 Total de produtos: {resumo.get('total_produtos', 0)}")
            print(f"   📄 Total de NFes: {resumo.get('total_nfs', 0)}")
            print(f"   💰 Valor total: {resumo.get('valor_total_formatado', 'N/A')}")
        else:
            print("❌ Dashboard não está respondendo!")
            return
            
    except Exception as e:
        print(f"❌ Erro ao conectar com dashboard: {e}")
        print("💡 Certifique-se que o dashboard está rodando:")
        print("   python melhorias_avancadas/dashboard_web.py")
        return
    
    try:
        # 2. Testar API de produtos padronizados
        print("\n🔍 2. Testando produtos padronizados...")
        response = requests.get(f"{base_url}/api/top-produtos-padronizados", timeout=5)
        if response.status_code == 200:
            produtos = response.json()
            print("✅ API de produtos padronizados funcionando!")
            
            if produtos['labels']:
                print(f"\n🏆 TOP 5 PRODUTOS MAIS COMPRADOS (PADRONIZADOS):")
                for i in range(min(5, len(produtos['labels']))):
                    produto = produtos['labels'][i]
                    valor = produtos['data'][i]
                    detalhe = produtos['detalhes'][i]

                    print(f"{i+1}. {produto}")
                    print(f"   💰 Valor Total Comprado: R$ {valor:,.2f}")
                    print(f"   📦 Quantidade de Compras: {detalhe['qtd_compras']}")
                    print(f"   📊 Quantidade Total: {detalhe['quantidade_total_comprada']:.0f} unidades")
                    print(f"   🏢 Unidades Compradoras: {detalhe['qtd_unidades_compradoras']}")
                    print(f"   🏭 Fornecedores: {detalhe['qtd_fornecedores']}")
                    print(f"   💲 Preço Médio Unitário: R$ {detalhe['preco_medio_unitario']:.2f}")
                    if detalhe['variacao_preco_percentual'] > 0:
                        print(f"   📈 Variação de Preço: {detalhe['variacao_preco_percentual']:.1f}%")
                    print(f"   📅 Período: {detalhe['primeira_compra']} a {detalhe['ultima_compra']}")
                    print()
            else:
                print("⚠️ Nenhum produto encontrado")
        else:
            print("❌ API de produtos padronizados não está funcionando!")
            
    except Exception as e:
        print(f"❌ Erro ao testar produtos padronizados: {e}")
    
    try:
        # 3. Testar API de oscilação de preços
        print("\n🔍 3. Testando análise de oscilação de preços...")
        response = requests.get(f"{base_url}/api/analise-oscilacao-precos", timeout=5)
        if response.status_code == 200:
            oscilacao = response.json()
            print("✅ API de oscilação de preços funcionando!")
            
            if 'top_oscilacao' in oscilacao and oscilacao['top_oscilacao']:
                print(f"\n📈 TOP 3 PRODUTOS COM MAIOR OSCILAÇÃO:")
                count = 0
                for produto, dados in oscilacao['top_oscilacao'].items():
                    if count >= 3:
                        break
                    print(f"{count+1}. {produto}")
                    print(f"   💰 R$ {dados['min']:.2f} - R$ {dados['max']:.2f} ({dados['Oscilacao_Percentual']:.1f}%)")
                    count += 1
            else:
                print("⚠️ Nenhuma oscilação encontrada")
        else:
            print("❌ API de oscilação não está funcionando!")
            
    except Exception as e:
        print(f"❌ Erro ao testar oscilação: {e}")

    try:
        # 4. Testar API de vacinas compradas
        print("\n🔍 4. Testando análise de VACINAS compradas...")
        response = requests.get(f"{base_url}/api/analise-vacinas-compradas", timeout=5)
        if response.status_code == 200:
            vacinas = response.json()
            print("✅ API de vacinas funcionando!")

            if 'total_vacinas_compradas' in vacinas:
                print(f"\n💉 RESUMO DE VACINAS COMPRADAS:")
                print(f"   💰 Valor Total: R$ {vacinas['total_vacinas_compradas']:,.2f}")
                print(f"   💉 Total de Doses: {vacinas['total_doses_compradas']:,.0f}")
                print(f"   🧪 Tipos de Vacinas: {vacinas['tipos_vacinas']}")

                if 'top_10_vacinas' in vacinas and vacinas['top_10_vacinas']:
                    print(f"\n🏆 TOP 5 VACINAS MAIS COMPRADAS:")
                    count = 0
                    for vacina, dados in vacinas['top_10_vacinas'].items():
                        if count >= 5:
                            break
                        print(f"{count+1}. {vacina}")
                        print(f"   💰 R$ {dados['Valor_Total_Comprado']:,.2f}")
                        print(f"   💉 {dados['Doses_Compradas']:,.0f} doses")
                        print(f"   💲 R$ {dados['Preco_Medio_Dose']:.2f}/dose")
                        count += 1
            else:
                print("⚠️ Nenhuma vacina encontrada")
        else:
            print("❌ API de vacinas não está funcionando!")

    except Exception as e:
        print(f"❌ Erro ao testar vacinas: {e}")

    # 5. Instruções para o usuário
    print(f"\n🌐 COMO TESTAR NO NAVEGADOR:")
    print(f"1. Acesse: {base_url}")
    print(f"2. Veja os gráficos de produtos padronizados")
    print(f"3. Teste os filtros por unidade")
    print(f"4. Compare com dados anteriores")
    
    print(f"\n🔗 APIs PARA TESTAR:")
    print(f"• {base_url}/api/top-produtos-padronizados")
    print(f"• {base_url}/api/analise-vacinas-compradas")
    print(f"• {base_url}/api/analise-oscilacao-precos")
    print(f"• {base_url}/api/resumo")
    
    print(f"\n✅ TESTE CONCLUÍDO!")

if __name__ == "__main__":
    testar_dashboard()
