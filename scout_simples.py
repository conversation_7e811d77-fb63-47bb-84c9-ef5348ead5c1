import pandas as pd
import os
from datetime import datetime

print("SCOUT COMPLETO DAS PLANILHAS")
print("=" * 50)
print(f"Data: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
print()

# Verificar arquivos
print("ARQUIVOS ENCONTRADOS:")
for arquivo in os.listdir('.'):
    if arquivo.endswith('.xlsx'):
        size = os.path.getsize(arquivo) / 1024 / 1024
        print(f"  {arquivo} ({size:.1f} MB)")
print()

# 1. CONTROLE DE PRODUTOS
print("1. CONTROLE DE PRODUTOS")
print("-" * 30)
try:
    df = pd.read_excel('controle_produtos.xlsx')
    print(f"Registros: {len(df):,}")
    print(f"Colunas: {len(df.columns)}")
    
    if 'Unidade' in df.columns:
        print(f"Unidades: {df['Unidade'].nunique()}")
        print("Lista de unidades:")
        for unidade in sorted(df['Unidade'].unique()):
            count = len(df[df['Unidade'] == unidade])
            print(f"  - {unidade}: {count:,} registros")
    
    if 'Numero NF' in df.columns:
        print(f"NFes únicas: {df['Numero NF'].nunique():,}")
    
    # Verificar novas colunas
    novas_cols = ['Fabricante', 'Lote', 'Data Validade']
    print("Novas colunas:")
    for col in novas_cols:
        if col in df.columns:
            preenchidos = df[col].notna().sum()
            print(f"  - {col}: {preenchidos:,} preenchidos ({preenchidos/len(df)*100:.1f}%)")
        else:
            print(f"  - {col}: NÃO ENCONTRADA")
    
except Exception as e:
    print(f"ERRO: {e}")
print()

# 2. CONTROLE DE FATURAMENTO
print("2. CONTROLE DE FATURAMENTO")
print("-" * 30)
try:
    df = pd.read_excel('controle_faturamento_geral.xlsx')
    print(f"Registros: {len(df):,}")
    
    if 'Unidade' in df.columns:
        print(f"Unidades: {df['Unidade'].nunique()}")
    
    if 'Numero NF' in df.columns:
        print(f"NFes únicas: {df['Numero NF'].nunique():,}")
        
except Exception as e:
    print(f"ERRO: {e}")
print()

# 3. CONTROLE DE BOLETOS
print("3. CONTROLE DE BOLETOS")
print("-" * 30)
try:
    df = pd.read_excel('controle_boletos.xlsx')
    print(f"Registros: {len(df):,}")
    
    if 'Unidade' in df.columns:
        print(f"Unidades: {df['Unidade'].nunique()}")
        
except Exception as e:
    print(f"ERRO: {e}")
print()

print("SCOUT CONCLUÍDO!")
