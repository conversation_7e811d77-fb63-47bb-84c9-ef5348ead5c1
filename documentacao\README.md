# Automação de Documentos Fiscais via Gmail (v2.0)

Este sistema automatiza o processo de ler e-mails, baixar anexos (PDF e XML), classificar documentos, extrair dados detalhados e organizá-los em pastas e planilhas de controle.

## Funcionalidades

- **Conexão Segura com Gmail:** Usa OAuth 2.0.
- **Busca Completa com Paginação:** Processa todos os e-mails não lidos que correspondem aos critérios.
- **Extração em Camadas:** Utiliza uma hierarquia de métodos para máxima precisão na extração de dados de NF-e (XML > Templates > Análise de Tabela).
- **Organização Inteligente:** Salva arquivos em uma estrutura `[UNIDADE]/[ANO]/[MÊS]/[TIPO_DOCUMENTO]/`.
- **Planilhas de Controle:** Gera planilhas para Faturamento, Produtos, Boletos e Impostos, com sistema anti-duplicação.
- **Logging Profissional:** <PERSON>tra todas as operações em `automacao.log`.

## Instalação

1. **Clone o repositório ou crie a estrutura de arquivos manualmente.**

2. **Crie um Ambiente Virtual (Recomendado):**
   ```bash
   python -m venv venv
   # No Windows
   venv\Scripts\activate
   # No Linux/Mac
   source venv/bin/activate