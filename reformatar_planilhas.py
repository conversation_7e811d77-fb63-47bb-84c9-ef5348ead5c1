#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCRIPT PARA REFORMATAR PLANILHAS EXISTENTES COM FORMATAÇÃO BRASILEIRA
"""

import pandas as pd
import os
from automacao_nf import formatar_valor_brasileiro, formatar_quantidade

def reformatar_planilha_produtos():
    """Reformata a planilha de produtos com formatação brasileira"""
    try:
        print("📦 REFORMATANDO PLANILHA DE PRODUTOS...")
        
        # Carregar planilha
        df = pd.read_excel('controle_produtos.xlsx')
        print(f"   Carregados {len(df)} registros")
        
        # Aplicar formatação brasileira
        if 'Quantidade' in df.columns:
            df['Quantidade'] = df['Quantidade'].apply(lambda x: formatar_quantidade(x) if pd.notna(x) else '')
            print("   ✅ Quantidade formatada")
        
        if 'Valor Unitario' in df.columns:
            df['Valor Unitario'] = df['Valor Unitario'].apply(lambda x: formatar_valor_brasileiro(x) if pd.notna(x) else '')
            print("   ✅ Valor Unitário formatado")
        
        if 'Valor Total Item' in df.columns:
            df['Valor Total Item'] = df['Valor Total Item'].apply(lambda x: formatar_valor_brasileiro(x) if pd.notna(x) else '')
            print("   ✅ Valor Total Item formatado")
        
        # Formatação de impostos
        for coluna in ['ICMS Valor', 'PIS Valor', 'COFINS Valor']:
            if coluna in df.columns:
                df[coluna] = df[coluna].apply(lambda x: formatar_valor_brasileiro(x) if pd.notna(x) else '')
                print(f"   ✅ {coluna} formatado")
        
        # Salvar planilha reformatada
        df.to_excel('controle_produtos.xlsx', index=False, engine='openpyxl')
        print("   💾 Planilha salva com formatação brasileira!")
        
    except Exception as e:
        print(f"   ❌ Erro ao reformatar planilha de produtos: {e}")

def reformatar_planilha_faturamento():
    """Reformata a planilha de faturamento com formatação brasileira"""
    try:
        print("\n💰 REFORMATANDO PLANILHA DE FATURAMENTO...")
        
        # Carregar planilha
        df = pd.read_excel('controle_faturamento_geral.xlsx')
        print(f"   Carregados {len(df)} registros")
        
        # Aplicar formatação brasileira
        for coluna in ['Valor Parcela', 'Valor Total NF', 'Valor Produtos']:
            if coluna in df.columns:
                df[coluna] = df[coluna].apply(lambda x: formatar_valor_brasileiro(x) if pd.notna(x) else '')
                print(f"   ✅ {coluna} formatado")
        
        # Salvar planilha reformatada
        df.to_excel('controle_faturamento_geral.xlsx', index=False, engine='openpyxl')
        print("   💾 Planilha salva com formatação brasileira!")
        
    except Exception as e:
        print(f"   ❌ Erro ao reformatar planilha de faturamento: {e}")

def reformatar_planilha_boletos():
    """Reformata a planilha de boletos com formatação brasileira"""
    try:
        print("\n🧾 REFORMATANDO PLANILHA DE BOLETOS...")
        
        if not os.path.exists('controle_boletos.xlsx'):
            print("   ⚠️ Planilha de boletos não encontrada")
            return
        
        # Carregar planilha
        df = pd.read_excel('controle_boletos.xlsx')
        print(f"   Carregados {len(df)} registros")
        
        # Aplicar formatação brasileira
        if 'Valor' in df.columns:
            df['Valor'] = df['Valor'].apply(lambda x: formatar_valor_brasileiro(x) if pd.notna(x) else '')
            print("   ✅ Valor formatado")
        
        # Salvar planilha reformatada
        df.to_excel('controle_boletos.xlsx', index=False, engine='openpyxl')
        print("   💾 Planilha salva com formatação brasileira!")
        
    except Exception as e:
        print(f"   ❌ Erro ao reformatar planilha de boletos: {e}")

def main():
    print("🔄 INICIANDO REFORMATAÇÃO DAS PLANILHAS EXISTENTES")
    print("=" * 60)
    
    # Fazer backup das planilhas originais
    print("📋 FAZENDO BACKUP DAS PLANILHAS ORIGINAIS...")
    
    for planilha in ['controle_produtos.xlsx', 'controle_faturamento_geral.xlsx', 'controle_boletos.xlsx']:
        if os.path.exists(planilha):
            backup_name = f"{planilha}.backup"
            if not os.path.exists(backup_name):
                import shutil
                shutil.copy2(planilha, backup_name)
                print(f"   ✅ Backup criado: {backup_name}")
    
    # Reformatar planilhas
    reformatar_planilha_produtos()
    reformatar_planilha_faturamento()
    reformatar_planilha_boletos()
    
    print("\n🎉 REFORMATAÇÃO CONCLUÍDA!")
    print("=" * 60)
    print("✅ Todas as planilhas foram reformatadas com padrão brasileiro:")
    print("   📦 Produtos: Quantidade (1.234,567) e Valores (R$ 1.234,56)")
    print("   💰 Faturamento: Valores (R$ 1.234,56)")
    print("   🧾 Boletos: Valores (R$ 1.234,56)")
    print("\n📁 Backups das planilhas originais foram criados (.backup)")

if __name__ == '__main__':
    main()
