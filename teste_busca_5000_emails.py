#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TESTE DA BUSCA DE 5000 E-MAILS
Verifica se a paginação está funcionando corretamente
"""

import os
import sys
from datetime import datetime
from automacao_nf import autenticar_gmail, buscar_emails, carregar_unidades, ARQUIVO_UNIDADES_CNPJ

def testar_busca_emails():
    """Testa a busca de e-mails com diferentes limites"""
    print("🧪 TESTE DA BUSCA DE E-MAILS COM PAGINAÇÃO")
    print("=" * 60)
    
    # Autenticar com Gmail
    print("🔐 Autenticando com Gmail...")
    servico_gmail = autenticar_gmail()
    if not servico_gmail:
        print("❌ ERRO: Falha na autenticação com Gmail!")
        return
    
    print("✅ Autenticação bem-sucedida!")
    
    # Testes com diferentes limites
    testes = [
        (100, "Teste básico - 100 e-mails"),
        (500, "Teste limite de página - 500 e-mails"),
        (1000, "Teste paginação - 1000 e-mails"),
        (2000, "Teste paginação - 2000 e-mails"),
        (5000, "Teste completo - 5000 e-mails")
    ]
    
    resultados = {}
    
    for limite, descricao in testes:
        print(f"\n📧 {descricao}")
        print("-" * 40)
        
        inicio = datetime.now()
        
        try:
            emails = buscar_emails(servico_gmail, incluir_lidos=True, max_results=limite)
            
            fim = datetime.now()
            tempo_execucao = (fim - inicio).total_seconds()
            
            resultados[limite] = {
                'emails_encontrados': len(emails),
                'tempo_execucao': tempo_execucao,
                'sucesso': True
            }
            
            print(f"✅ Resultado: {len(emails)} e-mails encontrados")
            print(f"⏱️ Tempo de execução: {tempo_execucao:.2f} segundos")
            
            # Verificar se realmente encontrou o limite esperado ou se há menos e-mails
            if len(emails) < limite:
                print(f"ℹ️ Encontrados {len(emails)} e-mails (menos que o limite de {limite})")
                print("   Isso pode indicar que não há mais e-mails disponíveis")
            
        except Exception as e:
            print(f"❌ ERRO no teste: {e}")
            resultados[limite] = {
                'emails_encontrados': 0,
                'tempo_execucao': 0,
                'sucesso': False,
                'erro': str(e)
            }
    
    # Resumo dos resultados
    print(f"\n📊 RESUMO DOS TESTES")
    print("=" * 60)
    
    for limite, resultado in resultados.items():
        if resultado['sucesso']:
            print(f"✅ {limite:4d} e-mails: {resultado['emails_encontrados']:4d} encontrados em {resultado['tempo_execucao']:6.2f}s")
        else:
            print(f"❌ {limite:4d} e-mails: ERRO - {resultado.get('erro', 'Erro desconhecido')}")
    
    # Análise de eficiência
    print(f"\n📈 ANÁLISE DE EFICIÊNCIA:")
    
    sucessos = {k: v for k, v in resultados.items() if v['sucesso']}
    
    if len(sucessos) >= 2:
        # Calcular e-mails por segundo
        for limite, resultado in sucessos.items():
            if resultado['tempo_execucao'] > 0:
                emails_por_segundo = resultado['emails_encontrados'] / resultado['tempo_execucao']
                print(f"   {limite:4d} e-mails: {emails_por_segundo:.1f} e-mails/segundo")
    
    # Verificar se a paginação está funcionando
    print(f"\n🔍 VERIFICAÇÃO DA PAGINAÇÃO:")
    
    if 500 in sucessos and 1000 in sucessos:
        emails_500 = sucessos[500]['emails_encontrados']
        emails_1000 = sucessos[1000]['emails_encontrados']
        
        if emails_1000 > emails_500:
            print("✅ Paginação funcionando: mais e-mails encontrados com limite maior")
        elif emails_1000 == emails_500:
            print("⚠️ Possível limite de e-mails: mesmo resultado para 500 e 1000")
        else:
            print("❌ Problema na paginação: menos e-mails com limite maior")
    
    # Recomendações
    print(f"\n💡 RECOMENDAÇÕES:")
    
    if 5000 in sucessos:
        emails_5000 = sucessos[5000]['emails_encontrados']
        tempo_5000 = sucessos[5000]['tempo_execucao']
        
        if emails_5000 >= 4000:
            print("✅ Sistema pronto para processar 5000 e-mails")
        elif emails_5000 >= 1000:
            print(f"⚠️ Sistema encontrou {emails_5000} e-mails (menos que 5000)")
            print("   Considere ajustar o limite baseado na quantidade disponível")
        else:
            print(f"⚠️ Poucos e-mails encontrados ({emails_5000})")
            print("   Verifique os critérios de busca ou a caixa de entrada")
        
        if tempo_5000 > 300:  # 5 minutos
            print(f"⚠️ Tempo de execução alto ({tempo_5000:.1f}s)")
            print("   Considere processar em lotes menores")
        else:
            print(f"✅ Tempo de execução aceitável ({tempo_5000:.1f}s)")

def testar_busca_rapida():
    """Teste rápido apenas para verificar se a função está funcionando"""
    print("\n🚀 TESTE RÁPIDO - VERIFICAÇÃO BÁSICA")
    print("=" * 60)
    
    # Autenticar
    servico_gmail = autenticar_gmail()
    if not servico_gmail:
        print("❌ Falha na autenticação")
        return
    
    # Teste com 50 e-mails
    print("📧 Buscando 50 e-mails para teste rápido...")
    emails = buscar_emails(servico_gmail, incluir_lidos=True, max_results=50)
    
    print(f"✅ Teste rápido concluído: {len(emails)} e-mails encontrados")
    
    if len(emails) > 0:
        print("✅ Sistema funcionando corretamente")
        return True
    else:
        print("⚠️ Nenhum e-mail encontrado - verifique critérios de busca")
        return False

def main():
    """Função principal"""
    print("🧪 INICIANDO TESTES DE BUSCA DE E-MAILS")
    print("=" * 60)
    
    # Verificar se quer fazer teste completo ou rápido
    resposta = input("Fazer teste completo (C) ou rápido (R)? [R]: ").strip().upper()
    
    if resposta == 'C':
        testar_busca_emails()
    else:
        if testar_busca_rapida():
            print("\n💡 Para teste completo, execute novamente e escolha 'C'")

if __name__ == '__main__':
    main()
