#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 INICIALIZADOR PRINCIPAL DO SISTEMA
Sistema de Inteligência Financeira - Ponto de entrada único
"""

import os
import sys
import subprocess

def mostrar_menu_principal():
    """Mostra o menu principal do sistema"""
    print("🚀 SISTEMA DE INTELIGÊNCIA FINANCEIRA")
    print("=" * 60)
    print("🏢 Sistema Completo de Gestão Financeira")
    print("📊 Dashboard Web + WhatsApp + IA")
    print("=" * 60)
    
    print("\n📋 SISTEMA PRINCIPAL:")
    print("1. 📧 Processar E-mails (Sistema Base)")
    print("2. 🔄 Reprocessar 5000 E-mails")
    print("3. 🎯 Central de Inteligência Financeira")
    
    print("\n🚀 MELHORIAS AVANÇADAS:")
    print("4. 🌐 Dashboard Web Interativo")
    print("5. 📱 Sistema WhatsApp")
    print("6. 🤖 Inteligência Artificial")
    print("7. 🎯 Sistema Integrado Completo")
    
    print("\n📊 RELATÓRIOS E ANÁLISES:")
    print("8. 📈 Dashboard Financeiro")
    print("9. 🚨 Sistema de Alertas")
    print("10. 📋 Relatórios Gerenciais")
    
    print("\n⚙️ CONFIGURAÇÃO:")
    print("11. 📁 Organizar Arquivos")
    print("12. 📖 Ver Documentação")
    print("13. ❌ Sair")

def executar_opcao(opcao):
    """Executa a opção selecionada"""
    try:
        if opcao == '1':
            print("📧 Iniciando processamento de e-mails...")
            subprocess.run([sys.executable, 'sistema_principal/automacao_nf.py'])
        
        elif opcao == '2':
            print("🔄 Iniciando reprocessamento de 5000 e-mails...")
            subprocess.run([sys.executable, 'sistema_principal/reprocessar_5000_emails.py'])
        
        elif opcao == '3':
            print("🎯 Iniciando Central de Inteligência...")
            subprocess.run([sys.executable, 'sistema_principal/central_inteligencia_financeira.py'])
        
        elif opcao == '4':
            print("🌐 Iniciando Dashboard Web...")
            subprocess.run([sys.executable, 'melhorias_avancadas/dashboard_web.py'])
        
        elif opcao == '5':
            print("📱 Iniciando Sistema WhatsApp...")
            subprocess.run([sys.executable, 'melhorias_avancadas/sistema_notificacoes_whatsapp.py'])
        
        elif opcao == '6':
            print("🤖 Iniciando IA...")
            subprocess.run([sys.executable, 'melhorias_avancadas/ia_previsoes_financeiras.py'])
        
        elif opcao == '7':
            print("🎯 Iniciando Sistema Integrado...")
            subprocess.run([sys.executable, 'melhorias_avancadas/sistema_integrado_melhorias.py'])
        
        elif opcao == '8':
            print("📈 Iniciando Dashboard Financeiro...")
            subprocess.run([sys.executable, 'sistema_principal/dashboard_financeiro.py'])
        
        elif opcao == '9':
            print("🚨 Iniciando Sistema de Alertas...")
            subprocess.run([sys.executable, 'sistema_principal/sistema_alertas.py'])
        
        elif opcao == '10':
            print("📋 Iniciando Relatórios...")
            subprocess.run([sys.executable, 'sistema_principal/relatorios_gerenciais.py'])
        
        elif opcao == '11':
            print("📁 Organizando arquivos...")
            organizar_arquivos()
        
        elif opcao == '12':
            print("📖 Abrindo documentação...")
            mostrar_documentacao()
        
        elif opcao == '13':
            print("👋 Encerrando sistema...")
            return False
        
        else:
            print("❌ Opção inválida!")
        
        return True
        
    except FileNotFoundError as e:
        print(f"❌ Arquivo não encontrado: {e}")
        print("💡 Verifique se todos os arquivos estão nas pastas corretas")
        return True
    except Exception as e:
        print(f"❌ Erro ao executar: {e}")
        return True

def organizar_arquivos():
    """Mostra status da organização dos arquivos"""
    print("\n📁 STATUS DA ORGANIZAÇÃO:")
    print("=" * 40)
    
    pastas = {
        'sistema_principal': 'Sistema Base',
        'melhorias_avancadas': 'Melhorias Avançadas',
        'dados': 'Dados e Configurações',
        'documentacao': 'Documentação'
    }
    
    for pasta, descricao in pastas.items():
        if os.path.exists(pasta):
            arquivos = len([f for f in os.listdir(pasta) if os.path.isfile(os.path.join(pasta, f))])
            print(f"✅ {descricao}: {arquivos} arquivos")
        else:
            print(f"❌ {descricao}: Pasta não encontrada")

def mostrar_documentacao():
    """Mostra informações sobre a documentação"""
    print("\n📖 DOCUMENTAÇÃO DISPONÍVEL:")
    print("=" * 40)
    
    docs = [
        'MANUAL_3_MELHORIAS_IMPLEMENTADAS.md',
        'MANUAL_SISTEMA_INTELIGENCIA_FINANCEIRA.md',
        'GUIA_INSTALACAO.md',
        'README.md'
    ]
    
    for doc in docs:
        caminho = f"documentacao/{doc}"
        if os.path.exists(caminho):
            print(f"✅ {doc}")
        else:
            print(f"❌ {doc} - Não encontrado")
    
    print("\n💡 Para ler a documentação, abra os arquivos .md na pasta 'documentacao'")

def verificar_estrutura():
    """Verifica se a estrutura de pastas está correta"""
    pastas_necessarias = [
        'sistema_principal',
        'melhorias_avancadas', 
        'dados',
        'documentacao'
    ]
    
    estrutura_ok = True
    
    for pasta in pastas_necessarias:
        if not os.path.exists(pasta):
            print(f"⚠️ Pasta '{pasta}' não encontrada")
            estrutura_ok = False
    
    return estrutura_ok

def main():
    """Função principal"""
    print("🔍 Verificando estrutura do sistema...")
    
    if not verificar_estrutura():
        print("\n❌ ESTRUTURA DE PASTAS INCOMPLETA!")
        print("💡 Execute a organização dos arquivos primeiro")
        return
    
    print("✅ Estrutura verificada com sucesso!")
    
    while True:
        mostrar_menu_principal()
        
        opcao = input("\nEscolha uma opção (1-13): ").strip()
        
        if not executar_opcao(opcao):
            break
        
        if opcao not in ['11', '12', '13']:
            input("\nPressione ENTER para continuar...")

if __name__ == '__main__':
    main()
