# 🐍 GUIA DE INSTALAÇÃO E EXECUÇÃO

## ⚠️ PROBLEMA IDENTIFICADO
O Python não está instalado corretamente no sistema Windows.

## 🔧 SOLUÇÕES PARA INSTALAR PYTHON

### **Opção 1: Microsoft Store (Recomendado)**
1. Abrir Microsoft Store
2. Buscar por "Python 3.11" ou "Python 3.12"
3. <PERSON><PERSON><PERSON> em "Instalar"
4. Aguardar instalação completa

### **Opção 2: Site Oficial Python**
1. Ir para: https://www.python.org/downloads/
2. Baixar Python 3.11+ para Windows
3. Executar instalador
4. ⚠️ **IMPORTANTE**: Marcar "Add Python to PATH"
5. Escolher "Install Now"

### **Opção 3: Anaconda (Para usuários avançados)**
1. Ir para: https://www.anaconda.com/download
2. Baixar Anaconda para Windows
3. Instalar seguindo instruções

## ✅ VERIFICAR INSTALAÇÃO

Após instalar, abrir PowerShell/CMD e testar:

```bash
# Testar Python
python --version
# ou
python3 --version
# ou
py --version

# Deve retornar algo como: Python 3.11.x
```

## 📦 INSTALAR DEPENDÊNCIAS

```bash
# Navegar para a pasta do projeto
cd "C:\Users\<USER>\OneDrive\Área de Trabalho\GMAIL AUTOMATIZACAO BAIXAR NFs"

# Instalar dependências
pip install -r requirements.txt

# Ou instalar individualmente:
pip install google-api-python-client
pip install google-auth-oauthlib
pip install pandas
pip install pdfplumber
pip install openpyxl
pip install python-dotenv
```

## 🧪 TESTAR INSTALAÇÃO

```bash
# Testar sintaxe e importações
python TESTE_SINTAXE.py

# Se funcionar, executar o sistema:
python automacao_nf.py
```

## 🚀 EXECUTAR O SISTEMA

### **Comando Principal:**
```bash
python automacao_nf.py
```

### **Comandos Alternativos:**
```bash
# Se python não funcionar:
python3 automacao_nf.py

# Ou:
py automacao_nf.py

# Ou com caminho completo:
C:\Python311\python.exe automacao_nf.py
```

## 📋 CHECKLIST PRÉ-EXECUÇÃO

- [ ] Python instalado e funcionando
- [ ] Dependências instaladas (`pip install -r requirements.txt`)
- [ ] Arquivo `credentials.json` presente
- [ ] Arquivo `unidades_cnpjs.csv` presente
- [ ] Conexão com internet ativa
- [ ] Gmail com e-mails não lidos para testar

## 🔍 VERIFICAR SE MELHORIAS ESTÃO FUNCIONANDO

### **Durante a Execução, Observe:**

```
✅ Logs Esperados:
"Buscando e-mails com a query: is:unread has:attachment (subject:..."
"-> Anexo XML encontrado: arquivo.xml"
"-> Processando XML da NFe (método preferencial)"
"-> Extração XML bem-sucedida! Itens encontrados: X"

⚠️ Logs de Fallback:
"-> Anexo PDF encontrado: arquivo.pdf"
"-> Processando PDF"

❌ Problemas:
"Nenhum e-mail novo encontrado"
"Não foi possível determinar a unidade"
```

### **Verificar Planilhas Geradas:**

```
📊 controle_produtos.xlsx
   - Deve ter colunas: ICMS CST, ICMS Aliquota, ICMS Valor
   - Deve ter colunas: PIS CST, PIS Aliquota, PIS Valor
   - Deve ter colunas: COFINS CST, COFINS Aliquota, COFINS Valor
   - Deve ter colunas: NCM, CFOP, CNPJ Emitente

📊 controle_faturamento_geral.xlsx
   - Deve ter dados de parcelas
   - Deve ter CNPJ Emitente
   - Deve ter Valor Total NF, Valor Produtos
```

## 🐛 SOLUÇÃO DE PROBLEMAS COMUNS

### **"Python não foi encontrado"**
```
Solução: Instalar Python conforme instruções acima
```

### **"ModuleNotFoundError: No module named 'X'"**
```
Solução: pip install X
Ou: pip install -r requirements.txt
```

### **"Nenhum e-mail novo encontrado"**
```
Verificar:
1. Há e-mails não lidos no Gmail?
2. E-mails têm anexos PDF/XML?
3. Assuntos contêm palavras-chave: nf, nfe, xml, danfe, etc.?
```

### **"Erro ao autenticar Gmail"**
```
Verificar:
1. Arquivo credentials.json está presente?
2. Conexão com internet ativa?
3. Primeira execução? Vai abrir navegador para autorizar
```

### **"Falha ao extrair dados do XML"**
```
Verificar:
1. Arquivo é realmente XML de NFe?
2. XML não está corrompido?
3. Verificar logs detalhados de erro
```

## 📊 EXEMPLO DE EXECUÇÃO BEM-SUCEDIDA

```
Iniciando automação...
Tabela de unidades carregada com sucesso.
Autenticação com Gmail bem-sucedida!
Buscando e-mails com a query: is:unread has:attachment (subject:("nota fiscal") OR subject:("notas fiscais") OR subject:("nfe") OR subject:("nf-e") OR subject:("nf") OR subject:("danfe") OR subject:("xml") OR ...)
Encontrados 2 e-mails para analisar.

[+] Processando e-mail ID: 123456 | Assunto: 'NFe 12345 - Fornecedor ABC'
  -> Anexo XML encontrado: nfe_12345.xml
  -> Classificado como: Notas Fiscais
  -> Processando XML da NFe (método preferencial)
  -> CNPJ destinatário 37053499000105 encontrado! Unidade: ARAXA
  -> Arquivo salvo em: ARAXA\2025\01\Notas Fiscais\nfe_12345.xml
    -> Extração XML bem-sucedida! Itens encontrados: 5
  -> Planilha 'controle_produtos.xlsx' atualizada com sucesso.
  -> Planilha 'controle_faturamento_geral.xlsx' atualizada com sucesso.
  -> E-mail ID 123456 marcado como processado.

[+] Processando e-mail ID: 789012 | Assunto: 'Boleto vencimento 15/01'
  -> Anexo PDF encontrado: boleto.pdf
  -> Classificado como: Boletos
  -> Processando PDF
  -> CNPJ genérico 28580486000101 encontrado no PDF! Unidade: GOIANIA - BOUGAINVILLE
  -> Arquivo salvo em: GOIANIA - BOUGAINVILLE\2025\01\Boletos\boleto.pdf
  -> Planilha 'controle_boletos.xlsx' atualizada com sucesso.
  -> E-mail ID 789012 marcado como processado.

Fim da execução.
```

## 🎯 PRÓXIMOS PASSOS APÓS INSTALAÇÃO

1. **Executar teste:** `python TESTE_SINTAXE.py`
2. **Executar sistema:** `python automacao_nf.py`
3. **Verificar planilhas geradas**
4. **Configurar execução automática** (agendamento)
5. **Treinar usuários** nas novas funcionalidades

---

**🚀 BOA SORTE COM A INSTALAÇÃO E TESTES!**
