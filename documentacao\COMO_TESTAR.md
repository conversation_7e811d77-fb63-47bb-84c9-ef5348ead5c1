# 🧪 COMO TESTAR AS MELHORIAS IMPLEMENTADAS

## 🎯 OBJETIVO
Verificar se o sistema está funcionando corretamente com as novas funcionalidades de processamento de XML e busca expandida.

## 📋 PRÉ-REQUISITOS

### 1. **Verificar Instalação**
```bash
# Verificar se Python está instalado
python --version
# ou
py --version

# Instalar dependências
pip install -r requirements.txt
```

### 2. **Configurar Credenciais Gmail**
- Ter o arquivo `credentials.json` configurado
- Primeira execução fará autenticação OAuth

### 3. **Verificar Arquivo de Unidades**
- Confirmar que `unidades_cnpjs.csv` está atualizado
- Formato: `UNIDADE;CNPJ`

## 🧪 TESTES RECOMENDADOS

### Teste 1: **Verificação de Sintaxe**
```bash
# Verificar se não há erros de sintaxe
python -m py_compile automacao_nf.py
```

### Teste 2: **Teste das Funções XML**
```bash
# Executar teste específico
python teste_melhorias.py
```

### Teste 3: **Execução Completa**
```bash
# Executar o sistema completo
python automacao_nf.py
```

## 📧 PREPARAR E-MAILS DE TESTE

### **E-mails Ideais para Teste:**

1. **E-mail com XML de NFe**
   - Assunto: "NFe 12345 - Fornecedor XYZ"
   - Anexo: arquivo.xml (XML da NFe)
   - ✅ **Deve ser processado com prioridade**

2. **E-mail com PDF de NFe**
   - Assunto: "Nota Fiscal 67890"
   - Anexo: danfe.pdf
   - ✅ **Deve ser processado como fallback**

3. **E-mail com Boleto**
   - Assunto: "Fatura vencimento 15/01"
   - Anexo: boleto.pdf
   - ✅ **Deve ir para planilha de boletos**

4. **E-mails com novas palavras-chave:**
   - "XML da NF 123"
   - "Danfe em anexo"
   - "NF de compra"

## 🔍 O QUE OBSERVAR DURANTE A EXECUÇÃO

### **Logs do Sistema**
```
✅ Bom: "-> Processando XML da NFe (método preferencial)"
✅ Bom: "-> Extração XML bem-sucedida! Itens encontrados: 5"
✅ Bom: "-> CNPJ destinatário 37053499000105 encontrado! Unidade: ARAXA"

⚠️  Atenção: "-> Processando PDF" (fallback quando não há XML)
❌ Problema: "-> Não foi possível determinar a unidade"
```

### **Estrutura de Pastas Criada**
```
📁 [UNIDADE]/
  └── 📁 2025/
      └── 📁 01/  (mês atual)
          ├── 📁 Notas Fiscais/
          │   ├── arquivo.xml
          │   └── danfe.pdf
          ├── 📁 Boletos/
          └── 📁 Impostos/
```

### **Planilhas Geradas**
```
📊 controle_produtos.xlsx
   - Deve ter colunas de impostos (ICMS, PIS, COFINS)
   - Deve ter NCM, CFOP, CNPJ Emitente

📊 controle_faturamento_geral.xlsx  
   - Deve ter dados de parcelas
   - Deve ter valores totais da NF

📊 controle_boletos.xlsx
📊 controle_impostos.xlsx
```

## ✅ CHECKLIST DE VERIFICAÇÃO

### **Funcionalidades Básicas**
- [ ] Sistema conecta com Gmail
- [ ] Busca e-mails não lidos
- [ ] Baixa anexos XML e PDF
- [ ] Identifica unidades corretamente

### **Processamento XML (NOVO)**
- [ ] Detecta arquivos XML
- [ ] Extrai dados básicos da NFe
- [ ] Extrai itens com impostos
- [ ] Processa parcelas/duplicatas
- [ ] Salva dados completos nas planilhas

### **Busca Expandida (NOVO)**
- [ ] Encontra e-mails com "NF"
- [ ] Encontra e-mails com "XML"
- [ ] Encontra e-mails com "Notas Fiscais"
- [ ] Processa anexos XML e PDF

### **Dados nas Planilhas (MELHORADO)**
- [ ] Planilha produtos tem colunas de impostos
- [ ] Planilha faturamento tem CNPJ emitente
- [ ] Dados de parcelas estão corretos
- [ ] NCM e CFOP estão preenchidos

## 🐛 PROBLEMAS COMUNS E SOLUÇÕES

### **Problema: "Nenhum e-mail novo encontrado"**
```
Solução:
1. Verificar se há e-mails não lidos no Gmail
2. Confirmar palavras-chave nos assuntos
3. Verificar se e-mails têm anexos
```

### **Problema: "Não foi possível determinar a unidade"**
```
Solução:
1. Verificar CNPJ no arquivo unidades_cnpjs.csv
2. Confirmar formato do CNPJ (apenas números)
3. Verificar se nome da unidade está no assunto
```

### **Problema: "Falha ao extrair dados do XML"**
```
Solução:
1. Verificar se XML é válido
2. Confirmar se é XML de NFe (não CTe, MDFe, etc.)
3. Verificar logs detalhados de erro
```

### **Problema: Planilhas não são criadas**
```
Solução:
1. Verificar permissões de escrita na pasta
2. Confirmar se openpyxl está instalado
3. Verificar se dados foram extraídos corretamente
```

## 📊 EXEMPLO DE RESULTADO ESPERADO

### **Log de Sucesso:**
```
Iniciando automação...
Autenticação com Gmail bem-sucedida!
Buscando e-mails com a query: is:unread has:attachment (subject:("nota fiscal") OR ...)
Encontrados 3 e-mails para analisar.

[+] Processando e-mail ID: 123456 | Assunto: 'NFe 12345 - Fornecedor ABC'
  -> Anexo XML encontrado: nfe_12345.xml
  -> Classificado como: Notas Fiscais
  -> Processando XML da NFe (método preferencial)
  -> CNPJ destinatário 37053499000105 encontrado! Unidade: ARAXA
  -> Arquivo salvo em: ARAXA\2025\01\Notas Fiscais\nfe_12345.xml
    -> Extração XML bem-sucedida! Itens encontrados: 8
  -> Planilha 'controle_produtos.xlsx' atualizada com sucesso.
  -> Planilha 'controle_faturamento_geral.xlsx' atualizada com sucesso.
  -> E-mail ID 123456 marcado como processado.
```

## 🎯 PRÓXIMOS PASSOS APÓS TESTE

1. **Se tudo funcionou:**
   - Configurar execução automática
   - Treinar usuários nas novas funcionalidades
   - Monitorar planilhas geradas

2. **Se houve problemas:**
   - Verificar logs de erro
   - Ajustar configurações
   - Testar com e-mails específicos

---

**🚀 BOA SORTE COM OS TESTES!**
