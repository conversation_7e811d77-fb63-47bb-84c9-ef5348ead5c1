#!/usr/bin/env python3
"""
🚨 TESTE DO DASHBOARD DE VENCIMENTOS SANTACLARA
Testa análises avançadas de vencimentos com cruzamento de dados
"""

import requests
import json

def testar_dashboard_vencimentos():
    """Testa o dashboard específico de vencimentos"""
    
    print("🚨 TESTANDO DASHBOARD DE VENCIMENTOS SANTACLARA")
    print("=" * 60)
    
    base_url = "http://localhost:5000"
    
    try:
        # 1. Testar se dashboard de vencimentos está acessível
        print("🔍 1. Verificando dashboard de vencimentos...")
        response = requests.get(f"{base_url}/vencimentos", timeout=5)
        if response.status_code == 200:
            print("✅ Dashboard de vencimentos está acessível!")
            print(f"   📄 Página carregada com {len(response.text)} caracteres")
        else:
            print("❌ Dashboard de vencimentos não está acessível!")
            return
            
    except Exception as e:
        print(f"❌ Erro ao acessar dashboard de vencimentos: {e}")
        return
    
    try:
        # 2. Testar API de análise detalhada de vencimentos
        print("\n🔍 2. Testando análise detalhada de vencimentos...")
        response = requests.get(f"{base_url}/api/analise-vencimentos-detalhada", timeout=10)
        if response.status_code == 200:
            vencimentos = response.json()
            print("✅ API de vencimentos detalhada funcionando!")
            
            if 'erro' not in vencimentos:
                print(f"\n🚨 ANÁLISE DE VENCIMENTOS:")
                print(f"   📅 Vencidos: {vencimentos.get('vencidos', 0)} boletos")
                print(f"   ⏰ Vencem hoje: {vencimentos.get('hoje', 0)} boletos")
                print(f"   📆 Próximos 7 dias: {vencimentos.get('semana', 0)} boletos")
                print(f"   📅 Próximo mês: {vencimentos.get('mes', 0)} boletos")
                print(f"   📊 Total de boletos: {vencimentos.get('total', 0)}")
                
                if 'valor_vencidos' in vencimentos:
                    print(f"   💰 Valor vencido: R$ {vencimentos['valor_vencidos']:,.2f}")
                    print(f"   💰 Valor próxima semana: R$ {vencimentos['valor_semana']:,.2f}")
                    print(f"   💰 Valor próximo mês: R$ {vencimentos['valor_mes']:,.2f}")
                
                if 'fornecedores_criticos' in vencimentos and vencimentos['fornecedores_criticos']:
                    print(f"\n🚚 TOP FORNECEDORES COM VENCIMENTOS CRÍTICOS:")
                    count = 0
                    for fornecedor, qtd in vencimentos['fornecedores_criticos'].items():
                        if count >= 5:
                            break
                        print(f"   {count+1}. {fornecedor}: {qtd} boletos")
                        count += 1
                
                if 'unidades_criticas' in vencimentos and vencimentos['unidades_criticas']:
                    print(f"\n🏢 TOP UNIDADES COM VENCIMENTOS CRÍTICOS:")
                    count = 0
                    for unidade, qtd in vencimentos['unidades_criticas'].items():
                        if count >= 5:
                            break
                        print(f"   {count+1}. {unidade}: {qtd} boletos")
                        count += 1
            else:
                print(f"⚠️ {vencimentos['erro']}")
        else:
            print(f"❌ API de vencimentos retornou erro {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erro ao testar vencimentos detalhados: {e}")
    
    try:
        # 3. Testar API de vencimentos por período
        print("\n🔍 3. Testando vencimentos por período...")
        response = requests.get(f"{base_url}/api/vencimentos-por-periodo", timeout=10)
        if response.status_code == 200:
            periodos = response.json()
            print("✅ API de vencimentos por período funcionando!")
            
            print(f"\n📅 ANÁLISE POR PERÍODOS:")
            for periodo, dados in periodos.items():
                if isinstance(dados, dict) and 'quantidade' in dados:
                    print(f"   📆 {periodo}: {dados['quantidade']} boletos - R$ {dados['valor']:,.2f}")
        else:
            print(f"❌ API de períodos retornou erro {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erro ao testar períodos: {e}")
    
    try:
        # 4. Testar API de cruzamento compras vs vencimentos
        print("\n🔍 4. Testando cruzamento compras vs vencimentos...")
        response = requests.get(f"{base_url}/api/cruzamento-compras-vencimentos", timeout=10)
        if response.status_code == 200:
            cruzamento = response.json()
            print("✅ API de cruzamento funcionando!")
            
            print(f"\n📊 CRUZAMENTO COMPRAS × VENCIMENTOS:")
            if 'compras' in cruzamento:
                print(f"   📦 Compras: {cruzamento['compras']['total']} registros - R$ {cruzamento['compras']['valor']:,.2f}")
            if 'vencimentos' in cruzamento:
                print(f"   📄 Vencimentos: {cruzamento['vencimentos']['total']} registros - R$ {cruzamento['vencimentos']['valor']:,.2f}")
            
            if 'insights' in cruzamento and cruzamento['insights']:
                print(f"\n💡 INSIGHTS INTELIGENTES:")
                for insight in cruzamento['insights']:
                    print(f"   • {insight}")
        else:
            print(f"❌ API de cruzamento retornou erro {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erro ao testar cruzamento: {e}")
    
    # 5. Instruções para o usuário
    print(f"\n🌐 COMO USAR O DASHBOARD DE VENCIMENTOS:")
    print(f"1. Acesse: {base_url}/vencimentos")
    print(f"2. Use os filtros por unidade, período e fornecedor")
    print(f"3. Veja alertas críticos em tempo real")
    print(f"4. Analise gráficos de evolução temporal")
    print(f"5. Monitore fornecedores com maior risco")
    
    print(f"\n🔗 APIs ESPECÍFICAS PARA VENCIMENTOS:")
    print(f"• {base_url}/api/analise-vencimentos-detalhada")
    print(f"• {base_url}/api/vencimentos-por-periodo")
    print(f"• {base_url}/api/cruzamento-compras-vencimentos")
    
    print(f"\n✅ TESTE DE VENCIMENTOS CONCLUÍDO!")
    print(f"🎯 Dashboard especializado pronto para análise de vencimentos!")

if __name__ == "__main__":
    testar_dashboard_vencimentos()
