import os
from dotenv import load_dotenv

load_dotenv()

# Carrega as configurações do .env ou usa valores padrão
GMAIL_CREDENTIALS_PATH = os.getenv('GMAIL_CREDENTIALS_PATH', 'credentials.json')
GMAIL_TOKEN_PATH = os.getenv('GMAIL_TOKEN_PATH', 'token.pickle')
SCOPES = ['https://www.googleapis.com/auth/gmail.modify']

PASTA_BASE_SAIDA = os.getenv('PASTA_BASE_SAIDA') or os.getcwd()
PASTA_INCONSISTENCIAS = os.path.join(PASTA_BASE_SAIDA, os.getenv('PASTA_INCONSISTENCIAS', 'INCONSISTENCIAS'))
PASTA_TEMPLATES = os.path.join(PASTA_BASE_SAIDA, os.getenv('PASTA_TEMPLATES', 'templates'))

PLANILHA_FATURAMENTO_NF = os.path.join(PASTA_BASE_SAIDA, os.getenv('PLANILHA_FATURAMENTO_NF', 'controle_faturamento.xlsx'))
PLANILHA_PRODUTOS = os.path.join(PASTA_BASE_SAIDA, os.getenv('PLANILHA_PRODUTOS', 'controle_produtos.xlsx'))
PLANILHA_BOLETOS = os.path.join(PASTA_BASE_SAIDA, os.getenv('PLANILHA_BOLETOS', 'controle_boletos.xlsx'))
PLANILHA_IMPOSTOS = os.path.join(PASTA_BASE_SAIDA, os.getenv('PLANILHA_IMPOSTOS', 'controle_impostos.xlsx'))

ARQUIVO_UNIDADES_CNPJ = os.getenv('ARQUIVO_UNIDADES_CNPJ', 'unidades_cnpjs.csv')

KW_NOTAS_FISCAIS = ['nota fiscal', 'nfe', 'nf-e', 'danfe']
KW_BOLETOS = ['boleto', 'fatura', 'cobrança', 'duplicata']
KW_IMPOSTOS = ['pis', 'cofins', 'irpj', 'csll', 'iss', 'das', 'darf', 'guia', 'recolhimento']