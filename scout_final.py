#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os
from datetime import datetime

def formatar_numero(valor):
    """Formata número com separador de milhares brasileiro"""
    try:
        return f"{int(valor):,}".replace(',', '.')
    except:
        return str(valor)

def formatar_moeda(valor):
    """Formata valor para moeda brasileira"""
    try:
        return f"R$ {valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
    except:
        return str(valor)

def limpar_valor(serie):
    """Limpa valores monetários e numéricos formatados"""
    try:
        return pd.to_numeric(serie.astype(str).str.replace('R$ ', '').str.replace('.', '').str.replace(',', '.'), errors='coerce')
    except:
        return pd.Series([0] * len(serie))

print('🎯 SCOUT COMPLETO - ANÁLISE PÓS 10.000 EMAILS')
print('=' * 80)
print(f'⏰ Análise realizada em: {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}')
print()

# Verificar arquivos
print('📁 ARQUIVOS ENCONTRADOS:')
arquivos_excel = ['controle_produtos.xlsx', 'controle_faturamento_geral.xlsx', 'controle_boletos.xlsx', 'controle_impostos.xlsx']
for arquivo in arquivos_excel:
    if os.path.exists(arquivo):
        try:
            size = os.path.getsize(arquivo) / 1024 / 1024
            print(f'   ✅ {arquivo} ({size:.1f} MB)')
        except:
            print(f'   ✅ {arquivo} (erro ao ler tamanho)')
    else:
        print(f'   ❌ {arquivo} - NÃO ENCONTRADO')
print()

# 1. CONTROLE DE PRODUTOS
print('📊 1. CONTROLE DE PRODUTOS')
print('-' * 60)
try:
    df_produtos = pd.read_excel('controle_produtos.xlsx')
    print(f'📋 Total de registros: {formatar_numero(len(df_produtos))}')
    print(f'📋 Total de colunas: {len(df_produtos.columns)}')
    print()
    
    # Verificar colunas principais
    colunas_principais = ['Unidade', 'Numero NF', 'Descricao Produto', 'Valor Total Item', 'Quantidade']
    print('🔍 COLUNAS PRINCIPAIS:')
    for col in colunas_principais:
        if col in df_produtos.columns:
            print(f'   ✅ {col}')
        else:
            print(f'   ❌ {col} - NÃO ENCONTRADA')
    print()
    
    # Verificar novas colunas (Fabricante, Lote, Data Validade)
    novas_colunas = ['Fabricante', 'Lote', 'Data Validade']
    print('🆕 STATUS DAS NOVAS COLUNAS:')
    for col in novas_colunas:
        if col in df_produtos.columns:
            preenchidos = df_produtos[col].notna().sum()
            total = len(df_produtos)
            percentual = (preenchidos / total) * 100 if total > 0 else 0
            print(f'   ✅ {col}: {formatar_numero(preenchidos)} de {formatar_numero(total)} preenchidos ({percentual:.1f}%)')
        else:
            print(f'   ❌ {col}: COLUNA NÃO ENCONTRADA')
    print()
    
    # Estatísticas por unidade
    if 'Unidade' in df_produtos.columns:
        print('🏢 UNIDADES ENCONTRADAS:')
        unidades = df_produtos['Unidade'].value_counts().sort_index()
        for unidade, count in unidades.items():
            print(f'   📍 {unidade}: {formatar_numero(count)} registros')
        print()
        print(f'🏢 TOTAL DE UNIDADES: {len(unidades)}')
        print()
    
    # NFes únicas
    if 'Numero NF' in df_produtos.columns:
        nfes_unicas = df_produtos['Numero NF'].nunique()
        print(f'📄 NFes ÚNICAS: {formatar_numero(nfes_unicas)}')
        print()
    
    # Produtos únicos
    if 'Descricao Produto' in df_produtos.columns:
        produtos_unicos = df_produtos['Descricao Produto'].nunique()
        print(f'🏷️ PRODUTOS ÚNICOS: {formatar_numero(produtos_unicos)}')
        print()
    
    # Valor total
    if 'Valor Total Item' in df_produtos.columns:
        valor_total = limpar_valor(df_produtos['Valor Total Item']).sum()
        print(f'💰 VALOR TOTAL DOS PRODUTOS: {formatar_moeda(valor_total)}')
        print()
    
    # Quantidade total
    if 'Quantidade' in df_produtos.columns:
        quantidade_total = limpar_valor(df_produtos['Quantidade']).sum()
        print(f'📦 QUANTIDADE TOTAL: {formatar_numero(quantidade_total)}')
        print()
        
except Exception as e:
    print(f'❌ ERRO ao analisar controle_produtos.xlsx: {e}')
    print()

# 2. CONTROLE DE FATURAMENTO
print('📊 2. CONTROLE DE FATURAMENTO')
print('-' * 60)
try:
    df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
    print(f'📋 Total de registros: {formatar_numero(len(df_faturamento))}')
    print(f'📋 Total de colunas: {len(df_faturamento.columns)}')
    print()
    
    # Unidades
    if 'Unidade' in df_faturamento.columns:
        unidades_fat = df_faturamento['Unidade'].value_counts().sort_index()
        print(f'🏢 Unidades no faturamento: {len(unidades_fat)}')
        for unidade, count in unidades_fat.items():
            print(f'   📍 {unidade}: {formatar_numero(count)} registros')
        print()
    
    # NFes únicas
    if 'Numero NF' in df_faturamento.columns:
        nfes_fat = df_faturamento['Numero NF'].nunique()
        print(f'📄 NFes únicas no faturamento: {formatar_numero(nfes_fat)}')
        print()
    
    # Valor total
    if 'Valor Total NF' in df_faturamento.columns:
        valor_fat = limpar_valor(df_faturamento['Valor Total NF']).sum()
        print(f'💰 VALOR TOTAL DAS NFes: {formatar_moeda(valor_fat)}')
        print()
        
except Exception as e:
    print(f'❌ ERRO ao analisar controle_faturamento_geral.xlsx: {e}')
    print()

# 3. CONTROLE DE BOLETOS
print('📊 3. CONTROLE DE BOLETOS')
print('-' * 60)
try:
    df_boletos = pd.read_excel('controle_boletos.xlsx')
    print(f'📋 Total de registros: {formatar_numero(len(df_boletos))}')
    print()
    
    # Unidades
    if 'Unidade' in df_boletos.columns:
        unidades_bol = df_boletos['Unidade'].value_counts().sort_index()
        print(f'🏢 Unidades com boletos: {len(unidades_bol)}')
        for unidade, count in unidades_bol.items():
            print(f'   📍 {unidade}: {formatar_numero(count)} boletos')
        print()
        
except Exception as e:
    print(f'❌ ERRO ao analisar controle_boletos.xlsx: {e}')
    print()

# 4. CONTROLE DE IMPOSTOS
print('📊 4. CONTROLE DE IMPOSTOS')
print('-' * 60)
try:
    df_impostos = pd.read_excel('controle_impostos.xlsx')
    print(f'📋 Total de registros: {formatar_numero(len(df_impostos))}')
    print()
    
    # Unidades
    if 'Unidade' in df_impostos.columns:
        unidades_imp = df_impostos['Unidade'].value_counts().sort_index()
        print(f'🏢 Unidades com impostos: {len(unidades_imp)}')
        for unidade, count in unidades_imp.items():
            print(f'   📍 {unidade}: {formatar_numero(count)} registros')
        print()
        
except Exception as e:
    print(f'❌ ERRO ao analisar controle_impostos.xlsx: {e}')
    print()

print('🎯 SCOUT CONCLUÍDO!')
print('=' * 80)
