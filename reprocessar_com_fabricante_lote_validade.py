#!/usr/bin/env python3
"""
🔄 REPROCESSAR EMAILS COM FABRICANTE, LOTE E VALIDADE
Reprocessa os emails para extrair informações de fabricante, lote e validade
que são essenciais para o dashboard de entrada de produtos
"""

import os
import sys

def reprocessar_emails():
    print("🔄 REPROCESSAMENTO COM FABRICANTE, LOTE E VALIDADE")
    print("=" * 60)
    
    print("📧 Este script irá:")
    print("   1. Fazer backup das planilhas atuais")
    print("   2. Reprocessar os emails para extrair:")
    print("      - Fabricante dos produtos")
    print("      - Lote dos produtos")
    print("      - Data de validade")
    print("   3. Gerar novas planilhas com essas informações")
    print()
    
    confirmacao = input("🎯 Deseja continuar com o reprocessamento? (S/N): ").strip().upper()
    
    if confirmacao != 'S':
        print("❌ Operação cancelada pelo usuário.")
        return
    
    print()
    print("🔄 INICIANDO REPROCESSAMENTO...")
    print("=" * 60)
    
    # Fazer backup das planilhas atuais
    print("📋 1. FAZENDO BACKUP DAS PLANILHAS ATUAIS...")
    
    import shutil
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    pasta_backup = f"backup_{timestamp}"
    
    try:
        os.makedirs(pasta_backup, exist_ok=True)
        
        arquivos_backup = [
            'controle_produtos.xlsx',
            'controle_faturamento_geral.xlsx'
        ]
        
        for arquivo in arquivos_backup:
            if os.path.exists(arquivo):
                shutil.copy2(arquivo, os.path.join(pasta_backup, arquivo))
                print(f"   ✅ Backup: {arquivo} -> {pasta_backup}/{arquivo}")
            else:
                print(f"   ⚠️ Arquivo não encontrado: {arquivo}")
        
        print(f"   📁 Backup salvo em: {pasta_backup}")
        
    except Exception as e:
        print(f"   ❌ Erro ao fazer backup: {e}")
        return
    
    print()
    print("🗑️ 2. REMOVENDO PLANILHAS ANTIGAS...")
    
    # Remover planilhas antigas para reprocessamento completo
    arquivos_remover = [
        'controle_produtos.xlsx',
        'controle_faturamento_geral.xlsx',
        'arquivos_processados.json'  # Para reprocessar tudo
    ]
    
    for arquivo in arquivos_remover:
        try:
            if os.path.exists(arquivo):
                os.remove(arquivo)
                print(f"   ✅ Removido: {arquivo}")
        except Exception as e:
            print(f"   ❌ Erro ao remover {arquivo}: {e}")
    
    print()
    print("📧 3. EXECUTANDO REPROCESSAMENTO DOS EMAILS...")
    print("   (Isso pode levar alguns minutos...)")
    print()
    
    # Executar o script principal de automação
    try:
        # Definir variáveis de ambiente para reprocessamento automático
        os.environ['MODO_REPROCESSAMENTO'] = 'true'
        os.environ['MAX_EMAILS'] = '5000'

        print("🚀 Iniciando processamento automático...")

        # Executar o script diretamente
        import subprocess
        result = subprocess.run([
            sys.executable,
            'sistema_principal/automacao_nf.py'
        ], capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ Processamento executado com sucesso!")
        else:
            print(f"❌ Erro no processamento: {result.stderr}")
            raise Exception(f"Erro no subprocess: {result.stderr}")
        
        print()
        print("✅ REPROCESSAMENTO CONCLUÍDO COM SUCESSO!")
        print("=" * 60)
        
        # Verificar se as novas colunas foram adicionadas
        print("🔍 VERIFICANDO NOVAS COLUNAS...")
        
        import pandas as pd
        
        if os.path.exists('controle_produtos.xlsx'):
            df = pd.read_excel('controle_produtos.xlsx')
            
            colunas_novas = ['Fabricante', 'Lote', 'Data Validade']
            colunas_encontradas = []
            
            for col in colunas_novas:
                if col in df.columns:
                    colunas_encontradas.append(col)
                    # Verificar quantos registros têm dados nessa coluna
                    preenchidos = df[col].notna().sum()
                    total = len(df)
                    percentual = (preenchidos / total) * 100 if total > 0 else 0
                    print(f"   ✅ {col}: {preenchidos}/{total} registros ({percentual:.1f}%)")
                else:
                    print(f"   ❌ {col}: Coluna não encontrada")
            
            print()
            print(f"📊 RESUMO DOS DADOS REPROCESSADOS:")
            print(f"   📦 Total de produtos: {len(df):,}")
            print(f"   🏭 Produtos com fabricante: {df['Fabricante'].notna().sum():,}")
            print(f"   🏷️ Produtos com lote: {df['Lote'].notna().sum():,}")
            print(f"   📅 Produtos com validade: {df['Data Validade'].notna().sum():,}")
            
        else:
            print("   ❌ Arquivo controle_produtos.xlsx não foi gerado")
        
        print()
        print("🎯 PRÓXIMOS PASSOS:")
        print("   1. ✅ Dados reprocessados com fabricante, lote e validade")
        print("   2. 🎨 Atualizar dashboard de entrada de produtos")
        print("   3. 🏢 Adicionar logomarca e paleta SantaClara")
        print("   4. 📊 Consolidar nomes de produtos")
        
    except Exception as e:
        print(f"❌ ERRO durante o reprocessamento: {e}")
        print()
        print("🔄 RESTAURANDO BACKUP...")
        
        # Restaurar backup em caso de erro
        for arquivo in arquivos_backup:
            backup_path = os.path.join(pasta_backup, arquivo)
            if os.path.exists(backup_path):
                try:
                    shutil.copy2(backup_path, arquivo)
                    print(f"   ✅ Restaurado: {arquivo}")
                except Exception as restore_error:
                    print(f"   ❌ Erro ao restaurar {arquivo}: {restore_error}")

if __name__ == "__main__":
    reprocessar_emails()
