r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Messaging
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.messaging.v1.brand_registration import BrandRegistrationList
from twilio.rest.messaging.v1.deactivations import DeactivationsList
from twilio.rest.messaging.v1.domain_certs import DomainCertsList
from twilio.rest.messaging.v1.domain_config import DomainConfigList
from twilio.rest.messaging.v1.domain_config_messaging_service import (
    DomainConfigMessagingServiceList,
)
from twilio.rest.messaging.v1.external_campaign import ExternalCampaignList
from twilio.rest.messaging.v1.linkshortening_messaging_service import (
    LinkshorteningMessagingServiceList,
)
from twilio.rest.messaging.v1.linkshortening_messaging_service_domain_association import (
    LinkshorteningMessagingServiceDomainAssociationList,
)
from twilio.rest.messaging.v1.request_managed_cert import RequestManagedCertList
from twilio.rest.messaging.v1.service import ServiceList
from twilio.rest.messaging.v1.tollfree_verification import TollfreeVerificationList
from twilio.rest.messaging.v1.usecase import UsecaseList


class V1(Version):

    def __init__(self, domain: Domain):
        """
        Initialize the V1 version of Messaging

        :param domain: The Twilio.messaging domain
        """
        super().__init__(domain, "v1")
        self._brand_registrations: Optional[BrandRegistrationList] = None
        self._deactivations: Optional[DeactivationsList] = None
        self._domain_certs: Optional[DomainCertsList] = None
        self._domain_config: Optional[DomainConfigList] = None
        self._domain_config_messaging_service: Optional[
            DomainConfigMessagingServiceList
        ] = None
        self._external_campaign: Optional[ExternalCampaignList] = None
        self._linkshortening_messaging_service: Optional[
            LinkshorteningMessagingServiceList
        ] = None
        self._linkshortening_messaging_service_domain_association: Optional[
            LinkshorteningMessagingServiceDomainAssociationList
        ] = None
        self._request_managed_cert: Optional[RequestManagedCertList] = None
        self._services: Optional[ServiceList] = None
        self._tollfree_verifications: Optional[TollfreeVerificationList] = None
        self._usecases: Optional[UsecaseList] = None

    @property
    def brand_registrations(self) -> BrandRegistrationList:
        if self._brand_registrations is None:
            self._brand_registrations = BrandRegistrationList(self)
        return self._brand_registrations

    @property
    def deactivations(self) -> DeactivationsList:
        if self._deactivations is None:
            self._deactivations = DeactivationsList(self)
        return self._deactivations

    @property
    def domain_certs(self) -> DomainCertsList:
        if self._domain_certs is None:
            self._domain_certs = DomainCertsList(self)
        return self._domain_certs

    @property
    def domain_config(self) -> DomainConfigList:
        if self._domain_config is None:
            self._domain_config = DomainConfigList(self)
        return self._domain_config

    @property
    def domain_config_messaging_service(self) -> DomainConfigMessagingServiceList:
        if self._domain_config_messaging_service is None:
            self._domain_config_messaging_service = DomainConfigMessagingServiceList(
                self
            )
        return self._domain_config_messaging_service

    @property
    def external_campaign(self) -> ExternalCampaignList:
        if self._external_campaign is None:
            self._external_campaign = ExternalCampaignList(self)
        return self._external_campaign

    @property
    def linkshortening_messaging_service(self) -> LinkshorteningMessagingServiceList:
        if self._linkshortening_messaging_service is None:
            self._linkshortening_messaging_service = LinkshorteningMessagingServiceList(
                self
            )
        return self._linkshortening_messaging_service

    @property
    def linkshortening_messaging_service_domain_association(
        self,
    ) -> LinkshorteningMessagingServiceDomainAssociationList:
        if self._linkshortening_messaging_service_domain_association is None:
            self._linkshortening_messaging_service_domain_association = (
                LinkshorteningMessagingServiceDomainAssociationList(self)
            )
        return self._linkshortening_messaging_service_domain_association

    @property
    def request_managed_cert(self) -> RequestManagedCertList:
        if self._request_managed_cert is None:
            self._request_managed_cert = RequestManagedCertList(self)
        return self._request_managed_cert

    @property
    def services(self) -> ServiceList:
        if self._services is None:
            self._services = ServiceList(self)
        return self._services

    @property
    def tollfree_verifications(self) -> TollfreeVerificationList:
        if self._tollfree_verifications is None:
            self._tollfree_verifications = TollfreeVerificationList(self)
        return self._tollfree_verifications

    @property
    def usecases(self) -> UsecaseList:
        if self._usecases is None:
            self._usecases = UsecaseList(self)
        return self._usecases

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.Messaging.V1>"
