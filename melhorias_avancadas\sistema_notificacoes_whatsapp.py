#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SISTEMA DE NOTIFICAÇÕES WHATSAPP
Envia alertas e relatórios via WhatsApp usando Twilio API
"""

import os
import json
import pandas as pd
from datetime import datetime, timedelta
from twilio.rest import Client
import requests
from urllib.parse import quote

class WhatsAppNotificacoes:
    def __init__(self):
        self.configurar_twilio()
        self.configurar_numeros()
        self.df_faturamento = None
        self.carregar_dados()
    
    def configurar_twilio(self):
        """Configura cliente Twilio (método profissional)"""
        # Configurações Twilio - substitua pelos seus dados
        self.account_sid = os.getenv('TWILIO_ACCOUNT_SID', 'SEU_ACCOUNT_SID_AQUI')
        self.auth_token = os.getenv('TWILIO_AUTH_TOKEN', 'SEU_AUTH_TOKEN_AQUI')
        self.whatsapp_from = os.getenv('TWILIO_WHATSAPP_FROM', 'whatsapp:+***********')  # Número Twilio
        
        try:
            self.client = Client(self.account_sid, self.auth_token)
            self.twilio_ativo = True
            print("✅ Twilio configurado com sucesso")
        except Exception as e:
            print(f"⚠️ Twilio não configurado: {e}")
            self.twilio_ativo = False
    
    def configurar_numeros(self):
        """Configura números de destino"""
        # Números para receber notificações (formato: whatsapp:+*************)
        self.numeros_gestores = [
            'whatsapp:+*************',  # Substitua pelo número real
            'whatsapp:+*************'   # Adicione mais números conforme necessário
        ]
        
        # Carregar de arquivo se existir
        if os.path.exists('config_whatsapp.json'):
            try:
                with open('config_whatsapp.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.numeros_gestores = config.get('numeros_gestores', self.numeros_gestores)
                    self.account_sid = config.get('account_sid', self.account_sid)
                    self.auth_token = config.get('auth_token', self.auth_token)
                    self.whatsapp_from = config.get('whatsapp_from', self.whatsapp_from)
            except Exception as e:
                print(f"Erro ao carregar config WhatsApp: {e}")
    
    def carregar_dados(self):
        """Carrega dados das planilhas"""
        try:
            if os.path.exists('controle_faturamento_geral.xlsx'):
                self.df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
                self.processar_dados()
        except Exception as e:
            print(f"Erro ao carregar dados: {e}")
    
    def processar_dados(self):
        """Processa dados para análise"""
        if self.df_faturamento is not None:
            self.df_faturamento['Valor_Numerico'] = self.df_faturamento['Valor Total NF'].apply(self.converter_valor_brasileiro)
            self.df_faturamento['Data_Vencimento_Dt'] = pd.to_datetime(self.df_faturamento['Data Vencimento'], format='%d/%m/%Y', errors='coerce')
    
    def converter_valor_brasileiro(self, valor):
        """Converte valor brasileiro para float"""
        try:
            if pd.isna(valor) or valor == '':
                return 0.0
            valor_str = str(valor).replace('R$', '').replace(' ', '').replace('.', '').replace(',', '.')
            return float(valor_str)
        except:
            return 0.0
    
    def enviar_whatsapp_twilio(self, numero, mensagem):
        """Envia mensagem via Twilio WhatsApp API"""
        if not self.twilio_ativo:
            print(f"⚠️ Twilio não configurado - simulando envio para {numero}")
            print(f"📱 Mensagem: {mensagem[:100]}...")
            return True
        
        try:
            message = self.client.messages.create(
                body=mensagem,
                from_=self.whatsapp_from,
                to=numero
            )
            print(f"✅ WhatsApp enviado para {numero} - SID: {message.sid}")
            return True
        except Exception as e:
            print(f"❌ Erro ao enviar WhatsApp para {numero}: {e}")
            return False
    
    def enviar_whatsapp_web(self, numero, mensagem):
        """Método alternativo usando WhatsApp Web (para desenvolvimento)"""
        # Remove prefixo whatsapp: e formata número
        numero_limpo = numero.replace('whatsapp:', '').replace('+', '')
        mensagem_encoded = quote(mensagem)
        
        # URL para WhatsApp Web
        url = f"https://wa.me/{numero_limpo}?text={mensagem_encoded}"
        
        print(f"🌐 WhatsApp Web URL gerada para {numero}:")
        print(f"   {url}")
        print(f"📱 Mensagem: {mensagem[:100]}...")
        
        return url
    
    def gerar_alerta_vencimentos(self):
        """Gera alerta de vencimentos próximos"""
        if self.df_faturamento is None:
            return None
        
        hoje = datetime.now()
        df_venc = self.df_faturamento[self.df_faturamento['Data_Vencimento_Dt'].notna()].copy()
        
        # Vencimentos em 7 dias
        data_limite_7 = hoje + timedelta(days=7)
        venc_7_dias = df_venc[
            (df_venc['Data_Vencimento_Dt'] >= hoje) & 
            (df_venc['Data_Vencimento_Dt'] <= data_limite_7)
        ]
        
        if len(venc_7_dias) == 0:
            return None
        
        valor_total = venc_7_dias['Valor_Numerico'].sum()
        
        mensagem = f"""🚨 *ALERTA DE VENCIMENTOS*
        
📅 *{len(venc_7_dias)} parcelas vencem em 7 dias*
💰 *Valor total: R$ {valor_total:,.2f}*

📋 *Detalhes:*""".replace('        ', '')
        
        # Adicionar top 5 maiores valores
        top_vencimentos = venc_7_dias.nlargest(5, 'Valor_Numerico')
        for _, row in top_vencimentos.iterrows():
            data_venc = row['Data_Vencimento_Dt'].strftime('%d/%m/%Y')
            valor = f"R$ {row['Valor_Numerico']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
            unidade = row['Unidade'][:15] + "..." if len(row['Unidade']) > 15 else row['Unidade']
            mensagem += f"\n• {unidade} - {data_venc} - {valor}"
        
        mensagem += f"\n\n⏰ *Enviado em: {datetime.now().strftime('%d/%m/%Y %H:%M')}*"
        
        return mensagem
    
    def gerar_resumo_diario(self):
        """Gera resumo diário das operações"""
        if self.df_faturamento is None:
            return None
        
        # Dados de hoje
        hoje = datetime.now()
        df_hoje = self.df_faturamento[
            self.df_faturamento['Data Emissao'] == hoje.strftime('%d/%m/%Y')
        ]
        
        # Estatísticas gerais
        total_nfs = len(self.df_faturamento)
        valor_total = self.df_faturamento['Valor_Numerico'].sum()
        unidades_ativas = self.df_faturamento['Unidade'].nunique()
        
        # Top 3 unidades
        top_unidades = self.df_faturamento.groupby('Unidade')['Valor_Numerico'].sum().sort_values(ascending=False).head(3)
        
        mensagem = f"""📊 *RESUMO FINANCEIRO DIÁRIO*
        
📈 *Estatísticas Gerais:*
• Total de NFes: {total_nfs:,}
• Valor Total: R$ {valor_total:,.2f}
• Unidades Ativas: {unidades_ativas}

🏆 *Top 3 Unidades:*""".replace('        ', '')
        
        for i, (unidade, valor) in enumerate(top_unidades.items(), 1):
            valor_formatado = f"R$ {valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
            unidade_nome = unidade[:20] + "..." if len(unidade) > 20 else unidade
            mensagem += f"\n{i}. {unidade_nome}: {valor_formatado}"
        
        # Verificar alertas ativos
        try:
            if os.path.exists('alertas_sistema.json'):
                with open('alertas_sistema.json', 'r', encoding='utf-8') as f:
                    alertas = json.load(f)
                alertas_alta = len([a for a in alertas if a.get('prioridade') == 'ALTA'])
                if alertas_alta > 0:
                    mensagem += f"\n\n⚠️ *{alertas_alta} alertas de alta prioridade ativos*"
        except:
            pass
        
        mensagem += f"\n\n📅 *{hoje.strftime('%d/%m/%Y %H:%M')}*"
        
        return mensagem
    
    def gerar_alerta_anomalia(self, unidade, valor_anomalo, valor_medio):
        """Gera alerta de gasto anômalo"""
        percentual = ((valor_anomalo - valor_medio) / valor_medio) * 100
        
        mensagem = f"""⚠️ *ALERTA DE ANOMALIA*
        
🏪 *Unidade:* {unidade}
💰 *Valor Anômalo:* R$ {valor_anomalo:,.2f}
📊 *Valor Médio:* R$ {valor_medio:,.2f}
📈 *Variação:* +{percentual:.1f}%

🔍 *Recomendação:* Verificar transação

⏰ *{datetime.now().strftime('%d/%m/%Y %H:%M')}*""".replace('        ', '')
        
        return mensagem
    
    def enviar_alerta_vencimentos(self):
        """Envia alerta de vencimentos para todos os gestores"""
        mensagem = self.gerar_alerta_vencimentos()
        
        if not mensagem:
            print("ℹ️ Nenhum vencimento próximo encontrado")
            return
        
        print("📱 Enviando alertas de vencimento...")
        
        for numero in self.numeros_gestores:
            if self.twilio_ativo:
                self.enviar_whatsapp_twilio(numero, mensagem)
            else:
                # Modo desenvolvimento - gerar URL
                url = self.enviar_whatsapp_web(numero, mensagem)
                print(f"   URL: {url}")
    
    def enviar_resumo_diario(self):
        """Envia resumo diário para todos os gestores"""
        mensagem = self.gerar_resumo_diario()
        
        if not mensagem:
            print("ℹ️ Nenhum dado disponível para resumo")
            return
        
        print("📱 Enviando resumo diário...")
        
        for numero in self.numeros_gestores:
            if self.twilio_ativo:
                self.enviar_whatsapp_twilio(numero, mensagem)
            else:
                # Modo desenvolvimento - gerar URL
                url = self.enviar_whatsapp_web(numero, mensagem)
                print(f"   URL: {url}")
    
    def monitoramento_automatico(self):
        """Executa monitoramento automático e envia alertas necessários"""
        print("🤖 INICIANDO MONITORAMENTO AUTOMÁTICO WHATSAPP")
        print("=" * 50)
        
        # Recarregar dados
        self.carregar_dados()
        
        # Verificar vencimentos
        self.enviar_alerta_vencimentos()
        
        # Enviar resumo se for horário apropriado (8h ou 18h)
        hora_atual = datetime.now().hour
        if hora_atual in [8, 18]:
            self.enviar_resumo_diario()
        
        print("✅ Monitoramento automático concluído")

def criar_arquivo_configuracao():
    """Cria arquivo de configuração do WhatsApp"""
    config = {
        "account_sid": "SEU_TWILIO_ACCOUNT_SID",
        "auth_token": "SEU_TWILIO_AUTH_TOKEN", 
        "whatsapp_from": "whatsapp:+***********",
        "numeros_gestores": [
            "whatsapp:+*************",
            "whatsapp:+*************"
        ],
        "instrucoes": {
            "1": "Substitua SEU_TWILIO_ACCOUNT_SID pelo seu Account SID do Twilio",
            "2": "Substitua SEU_TWILIO_AUTH_TOKEN pelo seu Auth Token do Twilio",
            "3": "Substitua os números pelos números reais dos gestores",
            "4": "Formato dos números: whatsapp:+************* (com código do país)",
            "5": "Para obter credenciais Twilio: https://console.twilio.com/"
        }
    }
    
    with open('config_whatsapp.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("📱 Arquivo config_whatsapp.json criado!")
    print("✏️ Edite o arquivo com suas credenciais Twilio e números")

def main():
    """Função principal para testes"""
    print("📱 SISTEMA DE NOTIFICAÇÕES WHATSAPP")
    print("=" * 50)
    
    # Verificar se arquivo de config existe
    if not os.path.exists('config_whatsapp.json'):
        print("⚠️ Arquivo de configuração não encontrado")
        resposta = input("Criar arquivo de configuração? (S/N): ").strip().upper()
        if resposta == 'S':
            criar_arquivo_configuracao()
            print("\n💡 Configure o arquivo e execute novamente")
            return
    
    # Inicializar sistema
    whatsapp = WhatsAppNotificacoes()
    
    # Menu de opções
    while True:
        print("\n📱 OPÇÕES DISPONÍVEIS:")
        print("1. 🚨 Enviar alerta de vencimentos")
        print("2. 📊 Enviar resumo diário")
        print("3. 🤖 Monitoramento automático")
        print("4. ⚙️ Criar arquivo de configuração")
        print("5. ❌ Sair")
        
        opcao = input("\nEscolha uma opção (1-5): ").strip()
        
        if opcao == '1':
            whatsapp.enviar_alerta_vencimentos()
        elif opcao == '2':
            whatsapp.enviar_resumo_diario()
        elif opcao == '3':
            whatsapp.monitoramento_automatico()
        elif opcao == '4':
            criar_arquivo_configuracao()
        elif opcao == '5':
            print("👋 Encerrando sistema de notificações...")
            break
        else:
            print("❌ Opção inválida!")

if __name__ == '__main__':
    main()
