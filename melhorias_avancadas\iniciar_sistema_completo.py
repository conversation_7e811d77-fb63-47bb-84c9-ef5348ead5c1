#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INICIALIZADOR DO SISTEMA COMPLETO
Script para iniciar rapidamente todas as melhorias
"""

import os
import sys
import subprocess
import time
import webbrowser
from datetime import datetime

def verificar_dependencias():
    """Verifica se todas as dependências estão instaladas"""
    print("🔍 VERIFICANDO DEPENDÊNCIAS...")
    
    dependencias = [
        'flask', 'pandas', 'openpyxl', 'scikit-learn', 'twilio'
    ]
    
    faltando = []
    
    for dep in dependencias:
        try:
            __import__(dep)
            print(f"   ✅ {dep}")
        except ImportError:
            print(f"   ❌ {dep}")
            faltando.append(dep)
    
    if faltando:
        print(f"\n⚠️ Dependências faltando: {', '.join(faltando)}")
        resposta = input("Instalar automaticamente? (S/N): ").strip().upper()
        
        if resposta == 'S':
            print("📦 Instalando dependências...")
            subprocess.run([sys.executable, '-m', 'pip', 'install'] + faltando)
            print("✅ Dependências instaladas!")
        else:
            print("❌ Instale as dependências manualmente:")
            print(f"   pip install {' '.join(faltando)}")
            return False
    
    return True

def verificar_arquivos():
    """Verifica se todos os arquivos necessários existem"""
    print("\n📁 VERIFICANDO ARQUIVOS DO SISTEMA...")
    
    arquivos_necessarios = [
        'dashboard_web.py',
        'templates/dashboard.html',
        'sistema_notificacoes_whatsapp.py',
        'ia_previsoes_financeiras.py',
        'sistema_integrado_melhorias.py'
    ]
    
    todos_presentes = True
    
    for arquivo in arquivos_necessarios:
        if os.path.exists(arquivo):
            print(f"   ✅ {arquivo}")
        else:
            print(f"   ❌ {arquivo}")
            todos_presentes = False
    
    return todos_presentes

def verificar_dados():
    """Verifica se há dados para processar"""
    print("\n📊 VERIFICANDO DADOS...")
    
    planilhas = [
        'controle_produtos.xlsx',
        'controle_faturamento_geral.xlsx',
        'controle_boletos.xlsx'
    ]
    
    dados_encontrados = False
    
    for planilha in planilhas:
        if os.path.exists(planilha):
            try:
                import pandas as pd
                df = pd.read_excel(planilha)
                print(f"   ✅ {planilha}: {len(df)} registros")
                dados_encontrados = True
            except Exception as e:
                print(f"   ⚠️ {planilha}: Erro ao ler ({e})")
        else:
            print(f"   ❌ {planilha}: Não encontrado")
    
    if not dados_encontrados:
        print("\n⚠️ NENHUM DADO ENCONTRADO!")
        print("💡 Execute primeiro o processamento de e-mails:")
        print("   python automacao_nf.py")
        print("   ou")
        print("   python reprocessar_5000_emails.py")
        return False
    
    return True

def iniciar_dashboard():
    """Inicia o dashboard web"""
    print("\n🌐 INICIANDO DASHBOARD WEB...")
    
    try:
        # Iniciar Flask em background
        processo = subprocess.Popen([
            sys.executable, 'dashboard_web.py'
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        
        print("   ⏳ Aguardando servidor iniciar...")
        time.sleep(3)
        
        # Verificar se processo ainda está rodando
        if processo.poll() is None:
            print("   ✅ Dashboard iniciado com sucesso!")
            
            # Abrir navegador
            print("   🌐 Abrindo navegador...")
            webbrowser.open('http://localhost:5000')
            
            return processo
        else:
            print("   ❌ Falha ao iniciar dashboard")
            return None
            
    except Exception as e:
        print(f"   ❌ Erro ao iniciar dashboard: {e}")
        return None

def executar_ia():
    """Executa análise de IA"""
    print("\n🤖 EXECUTANDO ANÁLISE DE IA...")
    
    try:
        # Executar IA em modo silencioso
        resultado = subprocess.run([
            sys.executable, '-c', '''
import sys
sys.path.append(".")
from ia_previsoes_financeiras import IAPrevisoes

ia = IAPrevisoes()
if ia.df_faturamento is not None:
    ia.gerar_relatorio_ia()
    print("✅ Análise de IA concluída")
else:
    print("❌ Dados insuficientes para IA")
'''
        ], capture_output=True, text=True, timeout=60)
        
        if resultado.returncode == 0:
            print("   ✅ IA executada com sucesso!")
            if os.path.exists('previsoes_ia.json'):
                print("   📊 Arquivo de previsões gerado")
            return True
        else:
            print(f"   ❌ Erro na IA: {resultado.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ⏰ Timeout na análise de IA")
        return False
    except Exception as e:
        print(f"   ❌ Erro ao executar IA: {e}")
        return False

def configurar_whatsapp():
    """Configura WhatsApp se necessário"""
    print("\n📱 VERIFICANDO WHATSAPP...")
    
    if not os.path.exists('config_whatsapp.json'):
        print("   ⚠️ Configuração WhatsApp não encontrada")
        resposta = input("   Criar arquivo de configuração? (S/N): ").strip().upper()
        
        if resposta == 'S':
            try:
                subprocess.run([
                    sys.executable, 'sistema_notificacoes_whatsapp.py'
                ], input='S\n', text=True, timeout=10)
                print("   ✅ Arquivo de configuração criado")
                print("   💡 Edite config_whatsapp.json com suas credenciais Twilio")
            except:
                print("   ❌ Erro ao criar configuração")
    else:
        print("   ✅ Configuração WhatsApp encontrada")

def mostrar_resumo():
    """Mostra resumo do sistema iniciado"""
    print("\n" + "="*60)
    print("🎉 SISTEMA COMPLETO INICIADO COM SUCESSO!")
    print("="*60)
    
    print("\n🌐 DASHBOARD WEB:")
    print("   URL: http://localhost:5000")
    print("   📊 Gráficos interativos em tempo real")
    print("   📱 Interface responsiva (desktop + mobile)")
    print("   🔄 Atualização automática a cada 30 segundos")
    
    print("\n🤖 INTELIGÊNCIA ARTIFICIAL:")
    print("   📈 Previsões financeiras geradas")
    print("   🧠 Insights automáticos disponíveis")
    print("   📊 Análise de tendências por unidade")
    print("   📅 Detecção de padrões sazonais")
    
    print("\n📱 NOTIFICAÇÕES WHATSAPP:")
    if os.path.exists('config_whatsapp.json'):
        print("   ✅ Sistema configurado e pronto")
        print("   🚨 Alertas de vencimentos automáticos")
        print("   📊 Resumos diários programados")
    else:
        print("   ⚙️ Necessita configuração (config_whatsapp.json)")
        print("   💡 Edite o arquivo com credenciais Twilio")
    
    print("\n🎯 PRÓXIMOS PASSOS:")
    print("   1. 🌐 Acesse o dashboard no navegador")
    print("   2. 📱 Configure WhatsApp se necessário")
    print("   3. 🔄 Sistema funcionará automaticamente")
    
    print("\n⚙️ COMANDOS ÚTEIS:")
    print("   python sistema_integrado_melhorias.py --completo")
    print("   python sistema_integrado_melhorias.py --monitoramento")
    
    print("\n" + "="*60)

def main():
    """Função principal"""
    print("🚀 INICIALIZADOR DO SISTEMA COMPLETO")
    print("=" * 60)
    print("🌐 Dashboard Web + 📱 WhatsApp + 🤖 IA")
    print("=" * 60)
    
    # Verificações preliminares
    if not verificar_dependencias():
        return
    
    if not verificar_arquivos():
        print("\n❌ Arquivos do sistema não encontrados!")
        print("💡 Certifique-se de que todos os arquivos estão no diretório")
        return
    
    if not verificar_dados():
        return
    
    # Configurar WhatsApp
    configurar_whatsapp()
    
    # Iniciar componentes
    print("\n🚀 INICIANDO COMPONENTES...")
    
    # 1. Dashboard Web
    processo_dashboard = iniciar_dashboard()
    
    # 2. IA
    ia_sucesso = executar_ia()
    
    # Mostrar resumo
    mostrar_resumo()
    
    # Manter dashboard rodando
    if processo_dashboard:
        try:
            print("\n💡 Pressione Ctrl+C para encerrar o sistema")
            processo_dashboard.wait()
        except KeyboardInterrupt:
            print("\n🛑 Encerrando sistema...")
            processo_dashboard.terminate()
            print("✅ Sistema encerrado")

if __name__ == '__main__':
    main()
