#!/usr/bin/env python3
"""
🔍 VERIFICAÇÃO DE PRODUTOS DESCONHECIDOS
Identifica produtos que não foram padronizados corretamente
e podem precisar de classificação manual
"""

import pandas as pd
import re
from collections import Counter

def verificar_produtos_desconhecidos():
    print("🔍 VERIFICAÇÃO DE PRODUTOS DESCONHECIDOS")
    print("=" * 60)
    
    # Carregar dados
    df_produtos = pd.read_excel('controle_produtos.xlsx')
    
    # Mapeamento de produtos SantaClara (mesmo do dashboard)
    mapeamento = {
        'TRIAXIM': ['TRIAXIM', 'TRIAXIM 1G', 'TRIAXIM 1 GRAMA'],
        'ABRYSVO': ['ABRYSVO'],
        'ADAC<PERSON> TRIPLICE': ['ADACEL', 'TRIPLICE ACELULAR'],
        'AJOVY': ['AJOVY'],
        'ANTIPNEUMOCOCICA - PREVENAR': ['PREVENAR', 'PNEUMOCOCICA PREVENAR'],
        'ANTIPNEUMOCOCICA 15 VALENTE': ['PNEUMO 15', 'PNEUMOCOCICA 15V'],
        'ANTIPNEUMOCOCICA 20': ['PNEUMO 20', 'PNEUMOCOCICA 20V'],
        'ANTIPNEUMOCOCICA 23': ['PNEUMO 23', 'PNEUMOCOCICA 23V'],
        'AREXVY': ['AREXVY'],
        'BEXSERO': ['BEXSERO'],
        'BOOSTRIX': ['BOOSTRIX'],
        'CERVARIX': ['CERVARIX'],
        'COMIRNATY': ['COMIRNATY', 'PFIZER'],
        'DENGVAXIA': ['DENGVAXIA'],
        'DUPLA ADULTO': ['DUPLA ADULTO', 'DT ADULTO'],
        'FEBRE AMARELA': ['FEBRE AMARELA', 'FA'],
        'FLUARIX TETRA': ['FLUARIX', 'INFLUENZA TETRA'],
        'GARDASIL 9': ['GARDASIL', 'HPV'],
        'HAVRIX': ['HAVRIX', 'HEPATITE A'],
        'HBVAXPRO': ['HBVAXPRO', 'HEPATITE B'],
        'HEXAXIM': ['HEXAXIM'],
        'IMOVAX': ['IMOVAX', 'RAIVA'],
        'INFLUVAC': ['INFLUVAC', 'INFLUENZA'],
        'IXIARO': ['IXIARO', 'ENCEFALITE JAPONESA'],
        'MENACTRA': ['MENACTRA', 'MENINGITE ACWY'],
        'MENVEO': ['MENVEO', 'MENINGITE'],
        'NIMENRIX': ['NIMENRIX'],
        'PENTAVALENTE': ['PENTAVALENTE', 'DTP'],
        'PNEUMOVAX': ['PNEUMOVAX'],
        'QDENGA': ['QDENGA', 'DENGUE'],
        'ROTARIX': ['ROTARIX', 'ROTAVIRUS'],
        'ROTATEQ': ['ROTATEQ'],
        'SHINGRIX': ['SHINGRIX', 'HERPES ZOSTER'],
        'SYNFLORIX': ['SYNFLORIX'],
        'TRESIVAC': ['TRESIVAC', 'TRIPLICE VIRAL'],
        'TWINRIX': ['TWINRIX', 'HEPATITE A+B'],
        'TYPHIM': ['TYPHIM', 'FEBRE TIFOIDE'],
        'VARICELA': ['VARICELA', 'CATAPORA'],
        'VARILRIX': ['VARILRIX'],
        'VIVOTIF': ['VIVOTIF'],
        'ZOSTAVAX': ['ZOSTAVAX']
    }
    
    def padronizar_produto(descricao):
        """Função de padronização (mesma do dashboard)"""
        if pd.isna(descricao):
            return 'PRODUTO NÃO IDENTIFICADO'
        
        descricao = str(descricao).upper().strip()
        
        # Buscar correspondência
        for produto_padrao, variacoes in mapeamento.items():
            for variacao in variacoes:
                if variacao in descricao:
                    return produto_padrao
        
        # Se não encontrou, retornar descrição limpa
        descricao_limpa = re.sub(r'[^\w\s]', ' ', descricao)
        descricao_limpa = re.sub(r'\s+', ' ', descricao_limpa).strip()
        
        # Se muito longa, pegar primeiras palavras importantes
        palavras = descricao_limpa.split()
        if len(palavras) > 3:
            return ' '.join(palavras[:3])
        
        return descricao_limpa if descricao_limpa else 'PRODUTO NÃO IDENTIFICADO'
    
    # Aplicar padronização
    df_produtos['Produto_Padronizado'] = df_produtos['Descricao Produto'].apply(padronizar_produto)
    
    print(f"📊 Total de produtos: {len(df_produtos):,}")
    print(f"📊 Produtos únicos originais: {df_produtos['Descricao Produto'].nunique():,}")
    print(f"📊 Produtos únicos padronizados: {df_produtos['Produto_Padronizado'].nunique():,}")
    
    # Identificar produtos que podem precisar de classificação
    print(f"\n🔍 PRODUTOS QUE PODEM PRECISAR DE CLASSIFICAÇÃO:")
    print("-" * 60)
    
    # Produtos não identificados
    nao_identificados = df_produtos[df_produtos['Produto_Padronizado'] == 'PRODUTO NÃO IDENTIFICADO']
    if len(nao_identificados) > 0:
        print(f"❌ PRODUTOS NÃO IDENTIFICADOS: {len(nao_identificados)} registros")
        print("   Descrições originais:")
        for desc in nao_identificados['Descricao Produto'].unique()[:10]:
            print(f"   - {desc}")
        if len(nao_identificados['Descricao Produto'].unique()) > 10:
            print(f"   ... e mais {len(nao_identificados['Descricao Produto'].unique()) - 10} produtos")
    
    # Produtos com nomes muito genéricos (podem ser vacinas não mapeadas)
    genericos = df_produtos[
        (~df_produtos['Produto_Padronizado'].isin(mapeamento.keys())) &
        (df_produtos['Produto_Padronizado'] != 'PRODUTO NÃO IDENTIFICADO')
    ]
    
    if len(genericos) > 0:
        print(f"\n⚠️ PRODUTOS COM NOMES GENÉRICOS: {len(genericos)} registros")
        print("   Podem ser vacinas não mapeadas:")
        
        # Agrupar por produto padronizado e mostrar os mais frequentes
        freq_genericos = genericos.groupby('Produto_Padronizado').agg({
            'Descricao Produto': 'first',
            'Valor Total Item': lambda x: x.str.replace('R$ ', '').str.replace('.', '').str.replace(',', '.').astype(float).sum()
        }).sort_values('Valor Total Item', ascending=False)
        
        for produto, row in freq_genericos.head(20).iterrows():
            valor_total = row['Valor Total Item']
            desc_original = row['Descricao Produto']
            print(f"   - {produto} (R$ {valor_total:,.2f}) <- {desc_original}")
    
    # Análise de palavras-chave que podem indicar vacinas
    print(f"\n💉 ANÁLISE DE POSSÍVEIS VACINAS NÃO MAPEADAS:")
    print("-" * 60)
    
    palavras_vacina = [
        'VACINA', 'IMUNIZANTE', 'DOSE', 'HEPATITE', 'INFLUENZA', 'GRIPE',
        'MENINGITE', 'PNEUMO', 'ROTAVIRUS', 'VARICELA', 'HPV', 'DENGUE',
        'FEBRE', 'RAIVA', 'TETANO', 'DIFTERIA', 'COQUELUCHE', 'POLIO'
    ]
    
    possiveis_vacinas = df_produtos[
        df_produtos['Descricao Produto'].str.contains('|'.join(palavras_vacina), case=False, na=False) &
        (~df_produtos['Produto_Padronizado'].isin(mapeamento.keys()))
    ]
    
    if len(possiveis_vacinas) > 0:
        print(f"🔍 Encontradas {len(possiveis_vacinas)} possíveis vacinas não mapeadas:")
        
        vacinas_freq = possiveis_vacinas.groupby('Descricao Produto').agg({
            'Produto_Padronizado': 'first',
            'Valor Total Item': lambda x: x.str.replace('R$ ', '').str.replace('.', '').str.replace(',', '.').astype(float).sum()
        }).sort_values('Valor Total Item', ascending=False)
        
        for desc_original, row in vacinas_freq.head(15).iterrows():
            produto_pad = row['Produto_Padronizado']
            valor_total = row['Valor Total Item']
            print(f"   - {desc_original}")
            print(f"     Padronizado como: {produto_pad}")
            print(f"     Valor total: R$ {valor_total:,.2f}")
            print()
    
    # Resumo final
    print(f"\n📋 RESUMO PARA CLASSIFICAÇÃO:")
    print("-" * 60)
    print(f"✅ Produtos bem mapeados: {len(df_produtos[df_produtos['Produto_Padronizado'].isin(mapeamento.keys())])} registros")
    print(f"⚠️ Produtos genéricos: {len(genericos)} registros")
    print(f"❌ Produtos não identificados: {len(nao_identificados)} registros")
    print(f"💉 Possíveis vacinas não mapeadas: {len(possiveis_vacinas)} registros")
    
    print(f"\n🎯 RECOMENDAÇÕES:")
    print("1. Revisar produtos genéricos com maior valor")
    print("2. Classificar possíveis vacinas não mapeadas")
    print("3. Investigar produtos não identificados")
    print("4. Atualizar mapeamento no dashboard conforme necessário")

if __name__ == "__main__":
    verificar_produtos_desconhecidos()
