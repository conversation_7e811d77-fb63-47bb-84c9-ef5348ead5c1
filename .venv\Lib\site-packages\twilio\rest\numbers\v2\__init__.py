r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Numbers
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional
from twilio.base.version import Version
from twilio.base.domain import Domain
from twilio.rest.numbers.v2.authorization_document import AuthorizationDocumentList
from twilio.rest.numbers.v2.bulk_hosted_number_order import BulkHostedNumberOrderList
from twilio.rest.numbers.v2.bundle_clone import BundleCloneList
from twilio.rest.numbers.v2.hosted_number_order import HostedNumberOrderList
from twilio.rest.numbers.v2.regulatory_compliance import RegulatoryComplianceList


class V2(Version):

    def __init__(self, domain: Domain):
        """
        Initialize the V2 version of Numbers

        :param domain: The Twilio.numbers domain
        """
        super().__init__(domain, "v2")
        self._authorization_documents: Optional[AuthorizationDocumentList] = None
        self._bulk_hosted_number_orders: Optional[BulkHostedNumberOrderList] = None
        self._bundle_clone: Optional[BundleCloneList] = None
        self._hosted_number_orders: Optional[HostedNumberOrderList] = None
        self._regulatory_compliance: Optional[RegulatoryComplianceList] = None

    @property
    def authorization_documents(self) -> AuthorizationDocumentList:
        if self._authorization_documents is None:
            self._authorization_documents = AuthorizationDocumentList(self)
        return self._authorization_documents

    @property
    def bulk_hosted_number_orders(self) -> BulkHostedNumberOrderList:
        if self._bulk_hosted_number_orders is None:
            self._bulk_hosted_number_orders = BulkHostedNumberOrderList(self)
        return self._bulk_hosted_number_orders

    @property
    def bundle_clone(self) -> BundleCloneList:
        if self._bundle_clone is None:
            self._bundle_clone = BundleCloneList(self)
        return self._bundle_clone

    @property
    def hosted_number_orders(self) -> HostedNumberOrderList:
        if self._hosted_number_orders is None:
            self._hosted_number_orders = HostedNumberOrderList(self)
        return self._hosted_number_orders

    @property
    def regulatory_compliance(self) -> RegulatoryComplianceList:
        if self._regulatory_compliance is None:
            self._regulatory_compliance = RegulatoryComplianceList(self)
        return self._regulatory_compliance

    def __repr__(self) -> str:
        """
        Provide a friendly representation
        :returns: Machine friendly representation
        """
        return "<Twilio.Numbers.V2>"
