#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SISTEMA DE ALERTAS INTELIGENTES
Monitora vencimentos, anomalias e oportunidades
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

class SistemaAlertas:
    def __init__(self):
        self.df_faturamento = None
        self.df_boletos = None
        self.alertas = []
        self.carregar_dados()
    
    def carregar_dados(self):
        """Carrega dados das planilhas"""
        try:
            if os.path.exists('controle_faturamento_geral.xlsx'):
                self.df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
                self.processar_dados_faturamento()
            
            if os.path.exists('controle_boletos.xlsx'):
                self.df_boletos = pd.read_excel('controle_boletos.xlsx')
                self.processar_dados_boletos()
                
        except Exception as e:
            print(f"❌ Erro ao carregar dados: {e}")
    
    def processar_dados_faturamento(self):
        """Processa dados de faturamento"""
        if self.df_faturamento is not None:
            # Converter valores
            self.df_faturamento['Valor_Numerico'] = self.df_faturamento['Valor Total NF'].apply(self.converter_valor_brasileiro)
            self.df_faturamento['Valor_Parcela_Numerico'] = self.df_faturamento['Valor Parcela'].apply(self.converter_valor_brasileiro)
            
            # Converter datas
            self.df_faturamento['Data_Vencimento_Dt'] = pd.to_datetime(self.df_faturamento['Data Vencimento'], format='%d/%m/%Y', errors='coerce')
            self.df_faturamento['Data_Emissao_Dt'] = pd.to_datetime(self.df_faturamento['Data Emissao'], format='%d/%m/%Y', errors='coerce')
    
    def processar_dados_boletos(self):
        """Processa dados de boletos"""
        if self.df_boletos is not None:
            # Converter valores
            self.df_boletos['Valor_Numerico'] = self.df_boletos['Valor Documento'].apply(self.converter_valor_brasileiro)
            
            # Converter datas
            self.df_boletos['Data_Vencimento_Dt'] = pd.to_datetime(self.df_boletos['Data Vencimento'], format='%d/%m/%Y', errors='coerce')
    
    def converter_valor_brasileiro(self, valor):
        """Converte valor brasileiro para float"""
        try:
            if pd.isna(valor) or valor == '':
                return 0.0
            valor_str = str(valor).replace('R$', '').replace(' ', '').replace('.', '').replace(',', '.')
            return float(valor_str)
        except:
            return 0.0
    
    def adicionar_alerta(self, tipo, prioridade, titulo, descricao, dados=None):
        """Adiciona um alerta ao sistema"""
        alerta = {
            'timestamp': datetime.now().strftime('%d/%m/%Y %H:%M:%S'),
            'tipo': tipo,
            'prioridade': prioridade,  # ALTA, MEDIA, BAIXA
            'titulo': titulo,
            'descricao': descricao,
            'dados': dados or {}
        }
        self.alertas.append(alerta)
    
    def verificar_vencimentos_proximos(self):
        """Verifica vencimentos próximos"""
        hoje = datetime.now()
        
        # Verificar faturamento
        if self.df_faturamento is not None:
            df_vencimentos = self.df_faturamento[self.df_faturamento['Data_Vencimento_Dt'].notna()].copy()
            
            for dias in [7, 15, 30]:
                data_limite = hoje + timedelta(days=dias)
                vencimentos = df_vencimentos[
                    (df_vencimentos['Data_Vencimento_Dt'] >= hoje) & 
                    (df_vencimentos['Data_Vencimento_Dt'] <= data_limite)
                ]
                
                if len(vencimentos) > 0:
                    valor_total = vencimentos['Valor_Parcela_Numerico'].sum()
                    prioridade = 'ALTA' if dias <= 7 else 'MEDIA' if dias <= 15 else 'BAIXA'
                    
                    self.adicionar_alerta(
                        tipo='VENCIMENTO',
                        prioridade=prioridade,
                        titulo=f'Vencimentos em {dias} dias',
                        descricao=f'{len(vencimentos)} parcelas vencendo em {dias} dias. Valor total: R$ {valor_total:,.2f}'.replace(',', 'X').replace('.', ',').replace('X', '.'),
                        dados={'quantidade': len(vencimentos), 'valor_total': valor_total, 'dias': dias}
                    )
        
        # Verificar boletos
        if self.df_boletos is not None:
            df_boletos_venc = self.df_boletos[self.df_boletos['Data_Vencimento_Dt'].notna()].copy()
            
            for dias in [7, 15, 30]:
                data_limite = hoje + timedelta(days=dias)
                vencimentos_boletos = df_boletos_venc[
                    (df_boletos_venc['Data_Vencimento_Dt'] >= hoje) & 
                    (df_boletos_venc['Data_Vencimento_Dt'] <= data_limite)
                ]
                
                if len(vencimentos_boletos) > 0:
                    valor_total = vencimentos_boletos['Valor_Numerico'].sum()
                    prioridade = 'ALTA' if dias <= 7 else 'MEDIA'
                    
                    self.adicionar_alerta(
                        tipo='VENCIMENTO_BOLETO',
                        prioridade=prioridade,
                        titulo=f'Boletos vencendo em {dias} dias',
                        descricao=f'{len(vencimentos_boletos)} boletos vencendo em {dias} dias. Valor total: R$ {valor_total:,.2f}'.replace(',', 'X').replace('.', ',').replace('X', '.'),
                        dados={'quantidade': len(vencimentos_boletos), 'valor_total': valor_total, 'dias': dias}
                    )
    
    def verificar_gastos_anomalos(self):
        """Verifica gastos anômalos por unidade"""
        if self.df_faturamento is None:
            return
        
        # Análise por unidade
        gastos_por_unidade = self.df_faturamento.groupby('Unidade')['Valor_Numerico'].agg(['mean', 'std', 'sum', 'count']).reset_index()
        
        for _, row in gastos_por_unidade.iterrows():
            if row['count'] >= 5:  # Só analisa unidades com pelo menos 5 registros
                # Verifica se há gastos muito acima da média
                gastos_unidade = self.df_faturamento[self.df_faturamento['Unidade'] == row['Unidade']]['Valor_Numerico']
                limite_anomalia = row['mean'] + (2 * row['std'])
                
                anomalias = gastos_unidade[gastos_unidade > limite_anomalia]
                
                if len(anomalias) > 0:
                    self.adicionar_alerta(
                        tipo='GASTO_ANOMALO',
                        prioridade='MEDIA',
                        titulo=f'Gastos anômalos - {row["Unidade"]}',
                        descricao=f'{len(anomalias)} transações com valores acima do padrão. Maior valor: R$ {anomalias.max():,.2f}'.replace(',', 'X').replace('.', ',').replace('X', '.'),
                        dados={'unidade': row['Unidade'], 'quantidade_anomalias': len(anomalias), 'valor_maximo': anomalias.max()}
                    )
    
    def verificar_performance_unidades(self):
        """Verifica performance das unidades"""
        if self.df_faturamento is None:
            return
        
        # Análise dos últimos 30 dias vs 30 dias anteriores
        hoje = datetime.now()
        data_30_dias = hoje - timedelta(days=30)
        data_60_dias = hoje - timedelta(days=60)
        
        df_recente = self.df_faturamento[self.df_faturamento['Data_Emissao_Dt'] >= data_30_dias]
        df_anterior = self.df_faturamento[
            (self.df_faturamento['Data_Emissao_Dt'] >= data_60_dias) & 
            (self.df_faturamento['Data_Emissao_Dt'] < data_30_dias)
        ]
        
        if len(df_recente) > 0 and len(df_anterior) > 0:
            gastos_recentes = df_recente.groupby('Unidade')['Valor_Numerico'].sum()
            gastos_anteriores = df_anterior.groupby('Unidade')['Valor_Numerico'].sum()
            
            for unidade in gastos_recentes.index:
                if unidade in gastos_anteriores.index:
                    variacao = ((gastos_recentes[unidade] - gastos_anteriores[unidade]) / gastos_anteriores[unidade]) * 100
                    
                    if variacao > 50:  # Aumento de mais de 50%
                        self.adicionar_alerta(
                            tipo='AUMENTO_GASTOS',
                            prioridade='MEDIA',
                            titulo=f'Aumento significativo - {unidade}',
                            descricao=f'Gastos aumentaram {variacao:.1f}% nos últimos 30 dias',
                            dados={'unidade': unidade, 'variacao': variacao}
                        )
                    elif variacao < -30:  # Redução de mais de 30%
                        self.adicionar_alerta(
                            tipo='REDUCAO_GASTOS',
                            prioridade='BAIXA',
                            titulo=f'Redução significativa - {unidade}',
                            descricao=f'Gastos reduziram {abs(variacao):.1f}% nos últimos 30 dias',
                            dados={'unidade': unidade, 'variacao': variacao}
                        )
    
    def gerar_relatorio_alertas(self):
        """Gera relatório de todos os alertas"""
        print("\n" + "="*60)
        print("🚨 SISTEMA DE ALERTAS INTELIGENTES")
        print("="*60)
        
        if not self.alertas:
            print("✅ Nenhum alerta ativo no momento")
            return
        
        # Agrupar por prioridade
        alertas_alta = [a for a in self.alertas if a['prioridade'] == 'ALTA']
        alertas_media = [a for a in self.alertas if a['prioridade'] == 'MEDIA']
        alertas_baixa = [a for a in self.alertas if a['prioridade'] == 'BAIXA']
        
        if alertas_alta:
            print(f"\n🔴 ALERTAS DE ALTA PRIORIDADE ({len(alertas_alta)}):")
            for alerta in alertas_alta:
                print(f"   ⚠️ {alerta['titulo']}")
                print(f"      {alerta['descricao']}")
        
        if alertas_media:
            print(f"\n🟡 ALERTAS DE MÉDIA PRIORIDADE ({len(alertas_media)}):")
            for alerta in alertas_media:
                print(f"   ⚠️ {alerta['titulo']}")
                print(f"      {alerta['descricao']}")
        
        if alertas_baixa:
            print(f"\n🟢 ALERTAS DE BAIXA PRIORIDADE ({len(alertas_baixa)}):")
            for alerta in alertas_baixa:
                print(f"   ℹ️ {alerta['titulo']}")
                print(f"      {alerta['descricao']}")
    
    def salvar_alertas(self):
        """Salva alertas em arquivo JSON"""
        try:
            with open('alertas_sistema.json', 'w', encoding='utf-8') as f:
                json.dump(self.alertas, f, ensure_ascii=False, indent=2)
            print(f"\n💾 Alertas salvos em 'alertas_sistema.json'")
        except Exception as e:
            print(f"❌ Erro ao salvar alertas: {e}")
    
    def executar_monitoramento_completo(self):
        """Executa monitoramento completo"""
        print("🔍 Executando monitoramento inteligente...")
        
        self.verificar_vencimentos_proximos()
        self.verificar_gastos_anomalos()
        self.verificar_performance_unidades()
        
        self.gerar_relatorio_alertas()
        self.salvar_alertas()

def main():
    sistema = SistemaAlertas()
    sistema.executar_monitoramento_completo()

if __name__ == '__main__':
    main()
