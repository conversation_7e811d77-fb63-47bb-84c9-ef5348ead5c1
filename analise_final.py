import pandas as pd
import os
from datetime import datetime

print("🎯 SCOUT COMPLETO - ANÁLISE PÓS 10.000 EMAILS")
print("=" * 80)
print(f"⏰ Análise realizada em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
print()

# Verificar arquivos
arquivos = ['controle_produtos.xlsx', 'controle_faturamento_geral.xlsx', 'controle_boletos.xlsx']
for arquivo in arquivos:
    if os.path.exists(arquivo):
        size = os.path.getsize(arquivo) / 1024 / 1024
        print(f"✅ {arquivo} ({size:.1f} MB)")
    else:
        print(f"❌ {arquivo} - NÃO ENCONTRADO")
print()

# 1. CONTROLE DE PRODUTOS
print("📊 1. CONTROLE DE PRODUTOS")
print("-" * 60)
try:
    df = pd.read_excel('controle_produtos.xlsx')
    print(f"📋 Total de registros: {len(df):,}")
    print(f"📋 Total de colunas: {len(df.columns)}")
    print()
    
    # Verificar colunas
    print("🔍 COLUNAS DISPONÍVEIS:")
    for i, col in enumerate(df.columns, 1):
        print(f"   {i:2d}. {col}")
    print()
    
    # Verificar novas colunas
    novas_colunas = ['Fabricante', 'Lote', 'Data Validade']
    print("🆕 STATUS DAS NOVAS COLUNAS:")
    for col in novas_colunas:
        if col in df.columns:
            preenchidos = df[col].notna().sum()
            total = len(df)
            percentual = (preenchidos / total) * 100 if total > 0 else 0
            print(f"   ✅ {col}: {preenchidos:,} de {total:,} preenchidos ({percentual:.1f}%)")
        else:
            print(f"   ❌ {col}: COLUNA NÃO ENCONTRADA")
    print()
    
    # Unidades
    if 'Unidade' in df.columns:
        unidades = df['Unidade'].value_counts()
        print(f"🏢 TOTAL DE UNIDADES: {len(unidades)}")
        print("🏢 UNIDADES ENCONTRADAS:")
        for unidade, count in unidades.items():
            print(f"   📍 {unidade}: {count:,} registros")
        print()
    
    # NFes únicas
    if 'Numero NF' in df.columns:
        nfes = df['Numero NF'].nunique()
        print(f"📄 NFes ÚNICAS: {nfes:,}")
        print()
    
    # Produtos únicos
    if 'Descricao Produto' in df.columns:
        produtos = df['Descricao Produto'].nunique()
        print(f"🏷️ PRODUTOS ÚNICOS: {produtos:,}")
        print()
        
except Exception as e:
    print(f"❌ ERRO: {e}")
print()

# 2. CONTROLE DE FATURAMENTO
print("📊 2. CONTROLE DE FATURAMENTO")
print("-" * 60)
try:
    df = pd.read_excel('controle_faturamento_geral.xlsx')
    print(f"📋 Total de registros: {len(df):,}")
    
    if 'Unidade' in df.columns:
        unidades = df['Unidade'].value_counts()
        print(f"🏢 Unidades: {len(unidades)}")
    
    if 'Numero NF' in df.columns:
        nfes = df['Numero NF'].nunique()
        print(f"📄 NFes únicas: {nfes:,}")
        
except Exception as e:
    print(f"❌ ERRO: {e}")
print()

# 3. CONTROLE DE BOLETOS
print("📊 3. CONTROLE DE BOLETOS")
print("-" * 60)
try:
    df = pd.read_excel('controle_boletos.xlsx')
    print(f"📋 Total de registros: {len(df):,}")
    
    if 'Unidade' in df.columns:
        unidades = df['Unidade'].value_counts()
        print(f"🏢 Unidades: {len(unidades)}")
        
except Exception as e:
    print(f"❌ ERRO: {e}")
print()

print("🎯 SCOUT CONCLUÍDO!")
print("=" * 80)
