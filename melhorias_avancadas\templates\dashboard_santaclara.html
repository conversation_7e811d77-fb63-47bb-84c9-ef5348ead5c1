<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Santa<PERSON>lara Vacinas - Dashboard Financeiro</title>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            /* Paleta SantaClara Vacinas baseada no logo */
            --primary-blue: #5B9BD5;      /* Azul principal do logo */
            --secondary-blue: #7FB3D3;    /* Azul mais claro */
            --accent-gold: #F4C430;       /* Dourado do coração */
            --light-blue: #E8F4FD;        /* Azul muito claro */
            --dark-blue: #2E5984;         /* Azul escuro */
            --text-primary: #2C3E50;      /* Texto principal */
            --text-secondary: #7F8C8D;    /* Texto secundário */
            --success: #27AE60;           /* Verde para positivo */
            --warning: #F39C12;           /* Laranja para atenção */
            --danger: #E74C3C;            /* Vermelho para crítico */
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --border: #E9ECEF;
            --shadow: rgba(91, 155, 213, 0.1);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--light-blue) 0%, var(--white) 50%, var(--light-blue) 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* Header com identidade SantaClara */
        .header-santaclara {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: var(--white);
            padding: 1.5rem 0;
            box-shadow: 0 4px 20px var(--shadow);
            position: relative;
            overflow: hidden;
        }

        .header-santaclara::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(244, 196, 48, 0.1));
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--accent-gold) 0%, #FFD700 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            box-shadow: 0 4px 15px rgba(244, 196, 48, 0.3);
        }

        .brand-text {
            font-weight: 700;
            font-size: 1.8rem;
            margin: 0;
        }

        .brand-subtitle {
            font-weight: 300;
            font-size: 0.9rem;
            opacity: 0.9;
            margin: 0;
        }

        /* Cards com estilo SantaClara */
        .metric-card {
            background: var(--white);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px var(--shadow);
            border: 1px solid var(--border);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, var(--primary-blue), var(--accent-gold));
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px var(--shadow);
        }

        .metric-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            margin-bottom: 1rem;
        }

        .metric-icon.primary { background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue)); }
        .metric-icon.success { background: linear-gradient(135deg, var(--success), #2ECC71); }
        .metric-icon.warning { background: linear-gradient(135deg, var(--warning), #E67E22); }
        .metric-icon.gold { background: linear-gradient(135deg, var(--accent-gold), #FFD700); }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .metric-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .metric-change {
            font-size: 0.8rem;
            font-weight: 600;
            margin-top: 0.5rem;
        }

        .metric-change.positive { color: var(--success); }
        .metric-change.negative { color: var(--danger); }

        /* Filtros com estilo SantaClara */
        .filter-container {
            background: var(--white);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px var(--shadow);
            border: 1px solid var(--border);
        }

        .form-select {
            border: 2px solid var(--border);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .form-select:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(91, 155, 213, 0.25);
        }

        /* Gráficos */
        .chart-container {
            background: var(--white);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 8px 32px var(--shadow);
            border: 1px solid var(--border);
            margin-bottom: 2rem;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chart-title i {
            color: var(--primary-blue);
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .brand-text { font-size: 1.4rem; }
            .metric-value { font-size: 1.5rem; }
            .chart-container { padding: 1rem; }
        }

        /* Animações */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .metric-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .metric-card:nth-child(1) { animation-delay: 0.1s; }
        .metric-card:nth-child(2) { animation-delay: 0.2s; }
        .metric-card:nth-child(3) { animation-delay: 0.3s; }
        .metric-card:nth-child(4) { animation-delay: 0.4s; }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner {
            border: 3px solid var(--border);
            border-top: 3px solid var(--primary-blue);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Badges e status */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-badge.critico { background: rgba(231, 76, 60, 0.1); color: var(--danger); }
        .status-badge.atencao { background: rgba(243, 156, 18, 0.1); color: var(--warning); }
        .status-badge.normal { background: rgba(39, 174, 96, 0.1); color: var(--success); }

        /* Tabelas */
        .table-santaclara {
            background: var(--white);
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px var(--shadow);
        }

        .table-santaclara th {
            background: var(--primary-blue);
            color: var(--white);
            font-weight: 600;
            border: none;
            padding: 1rem;
        }

        .table-santaclara td {
            padding: 1rem;
            border-color: var(--border);
        }

        .table-santaclara tbody tr:hover {
            background: var(--light-blue);
        }
    </style>
</head>
<body>
    <!-- Header SantaClara -->
    <header class="header-santaclara">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="logo-container">
                        <div class="logo-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div>
                            <h1 class="brand-text">SantaClara</h1>
                            <p class="brand-subtitle">vacinas • Protege quem você ama</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <h2 style="margin: 0; font-weight: 300;">Dashboard Financeiro</h2>
                    <p style="margin: 0; opacity: 0.9;">Análise Inteligente de Dados</p>
                </div>
            </div>
        </div>
    </header>

    <!-- Filtros -->
    <div class="container mt-4">
        <div class="filter-container">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <label for="unidadeSelect" class="form-label fw-semibold">
                        <i class="fas fa-building text-primary me-2"></i>Selecionar Unidade
                    </label>
                    <select class="form-select" id="unidadeSelect">
                        <option value="TODAS">🏢 Todas as Unidades</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-semibold">
                        <i class="fas fa-calendar text-primary me-2"></i>Período de Análise
                    </label>
                    <select class="form-select">
                        <option>📅 Últimos 30 dias</option>
                        <option>📅 Últimos 90 dias</option>
                        <option>📅 Último ano</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-semibold">
                        <i class="fas fa-sync text-primary me-2"></i>Atualização
                    </label>
                    <button class="btn btn-outline-primary w-100" onclick="atualizarDados()">
                        <i class="fas fa-refresh me-2"></i>Atualizar Dados
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Métricas Principais -->
    <div class="container">
        <div class="row g-4" id="metricas-principais">
            <!-- Cards serão inseridos aqui via JavaScript -->
        </div>
    </div>

    <!-- 🎯 NOVA SEÇÃO: ANÁLISE DE PRODUTOS PADRONIZADOS -->
    <div class="container mt-4">
        <div class="row g-4">
            <div class="col-12">
                <div class="chart-container">
                    <h3 class="chart-title">
                        <i class="fas fa-pills"></i>
                        🎯 Top Produtos Comprados (Padronizados SantaClara)
                    </h3>
                    <div class="row">
                        <div class="col-lg-8">
                            <canvas id="topProdutosPadronizadosChart" height="120"></canvas>
                        </div>
                        <div class="col-lg-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Produtos Padronizados:</h6>
                                <ul class="mb-0">
                                    <li>✅ Nomes organizados conforme tabela SantaClara</li>
                                    <li>📊 Consolidação de variações</li>
                                    <li>💰 Análise de compras por produto</li>
                                    <li>📈 Histórico de preços</li>
                                </ul>
                            </div>
                            <div id="detalheProdutoSelecionado"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 💉 NOVA SEÇÃO: ANÁLISE ESPECÍFICA DE VACINAS -->
    <div class="container mt-4">
        <div class="row g-4">
            <div class="col-12">
                <div class="chart-container">
                    <h3 class="chart-title">
                        <i class="fas fa-syringe"></i>
                        💉 Análise Específica de Vacinas Compradas
                    </h3>
                    <div class="row" id="vacinasAnalise">
                        <!-- Conteúdo será carregado via JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 📈 NOVA SEÇÃO: HISTÓRICO DE PREÇOS INTERATIVO -->
    <div class="container mt-4">
        <div class="row g-4">
            <div class="col-12">
                <div class="chart-container">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-area"></i>
                        📈 Histórico de Preços por Produto (Preparado para 5000 NFes)
                    </h3>
                    <div class="row">
                        <div class="col-md-3">
                            <label class="form-label">Selecionar Produto:</label>
                            <select class="form-select" id="produtoHistoricoSelect">
                                <option value="">Carregando produtos...</option>
                            </select>
                        </div>
                        <div class="col-md-9">
                            <canvas id="historicoPrecoChart" height="80"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos Originais -->
    <div class="container mt-4">
        <div class="row g-4">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        Evolução Mensal de Compras
                    </h3>
                    <canvas id="fluxoMensalChart" height="100"></canvas>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="chart-container">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-pie"></i>
                        Top Unidades
                    </h3>
                    <canvas id="topUnidadesChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Configuração global dos gráficos com cores SantaClara
        Chart.defaults.font.family = 'Inter';
        Chart.defaults.color = '#2C3E50';
        
        const coresSantaClara = {
            primary: '#5B9BD5',
            secondary: '#7FB3D3', 
            accent: '#F4C430',
            success: '#27AE60',
            warning: '#F39C12',
            danger: '#E74C3C'
        };

        // Variáveis globais
        let fluxoChart, unidadesChart, fornecedoresChart, produtosPadronizadosChart, historicoPrecoChart;
        let dadosAtuais = {};
        let dadosVacinas = {};
        let produtosPadronizados = [];

        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            carregarUnidades();
            carregarDados();
            carregarDadosVacinas();
            carregarProdutosPadronizados();

            // Event listeners
            document.getElementById('unidadeSelect').addEventListener('change', function() {
                carregarDados();
                carregarDadosVacinas();
                carregarProdutosPadronizados();
            });

            document.getElementById('produtoHistoricoSelect').addEventListener('change', function() {
                carregarHistoricoPreco();
            });
        });

        // Função para carregar unidades
        async function carregarUnidades() {
            try {
                const response = await fetch('/api/unidades');
                const unidades = await response.json();
                
                const select = document.getElementById('unidadeSelect');
                select.innerHTML = '<option value="TODAS">🏢 Todas as Unidades</option>';
                
                unidades.forEach(unidade => {
                    if (unidade.value !== 'TODAS') {
                        const option = document.createElement('option');
                        option.value = unidade.value;
                        option.textContent = `🏢 ${unidade.label}`;
                        select.appendChild(option);
                    }
                });
            } catch (error) {
                console.error('Erro ao carregar unidades:', error);
            }
        }

        // Função principal para carregar dados
        async function carregarDados() {
            const unidadeSelecionada = document.getElementById('unidadeSelect').value;
            
            try {
                // Mostrar loading
                document.body.classList.add('loading');
                
                // Carregar dados em paralelo
                const [resumo, fluxoMensal, topUnidades, topFornecedores] = await Promise.all([
                    fetch(`/api/resumo?unidade=${unidadeSelecionada}`).then(r => r.json()),
                    fetch(`/api/fluxo-mensal?unidade=${unidadeSelecionada}`).then(r => r.json()),
                    fetch(`/api/top-unidades`).then(r => r.json()),
                    fetch(`/api/top-fornecedores?unidade=${unidadeSelecionada}`).then(r => r.json())
                ]);

                dadosAtuais = { resumo, fluxoMensal, topUnidades, topFornecedores };
                
                // Atualizar interface
                atualizarMetricas(resumo);
                atualizarGraficos();
                
            } catch (error) {
                console.error('Erro ao carregar dados:', error);
                mostrarErro('Erro ao carregar dados. Tente novamente.');
            } finally {
                document.body.classList.remove('loading');
            }
        }

        // Função para atualizar métricas principais
        function atualizarMetricas(resumo) {
            const container = document.getElementById('metricas-principais');

            const metricas = [
                {
                    icon: 'fas fa-file-invoice',
                    iconClass: 'primary',
                    titulo: 'Total de NFes',
                    valor: resumo.total_nfs || 0,
                    mudanca: resumo.variacao_percentual || 0,
                    formato: 'numero'
                },
                {
                    icon: 'fas fa-shopping-cart',
                    iconClass: 'success',
                    titulo: 'Total de Produtos',
                    valor: resumo.total_produtos || 0,
                    mudanca: 0,
                    formato: 'numero'
                },
                {
                    icon: 'fas fa-dollar-sign',
                    iconClass: 'gold',
                    titulo: 'Valor Total',
                    valor: resumo.valor_total_formatado || 'R$ 0,00',
                    mudanca: resumo.variacao_percentual || 0,
                    formato: 'moeda'
                },
                {
                    icon: 'fas fa-building',
                    iconClass: 'warning',
                    titulo: 'Unidades Ativas',
                    valor: resumo.unidades_ativas || 0,
                    mudanca: 0,
                    formato: 'numero'
                }
            ];

            container.innerHTML = metricas.map((metrica, index) => `
                <div class="col-lg-3 col-md-6">
                    <div class="metric-card" style="animation-delay: ${index * 0.1}s">
                        <div class="metric-icon ${metrica.iconClass}">
                            <i class="${metrica.icon}"></i>
                        </div>
                        <div class="metric-value">${metrica.formato === 'numero' ? metrica.valor.toLocaleString('pt-BR') : metrica.valor}</div>
                        <div class="metric-label">${metrica.titulo}</div>
                        ${metrica.mudanca !== 0 ? `
                            <div class="metric-change ${metrica.mudanca > 0 ? 'positive' : 'negative'}">
                                <i class="fas fa-arrow-${metrica.mudanca > 0 ? 'up' : 'down'}"></i>
                                ${Math.abs(metrica.mudanca).toFixed(1)}% vs período anterior
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        }

        // Função para atualizar gráficos
        function atualizarGraficos() {
            atualizarFluxoMensal();
            atualizarTopUnidades();
        }

        // Gráfico de fluxo mensal
        function atualizarFluxoMensal() {
            const ctx = document.getElementById('fluxoMensalChart').getContext('2d');

            if (fluxoChart) {
                fluxoChart.destroy();
            }

            fluxoChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: dadosAtuais.fluxoMensal.labels || [],
                    datasets: [{
                        label: 'Valor Total (R$)',
                        data: dadosAtuais.fluxoMensal.data || [],
                        borderColor: coresSantaClara.primary,
                        backgroundColor: `${coresSantaClara.primary}20`,
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: coresSantaClara.accent,
                        pointBorderColor: coresSantaClara.primary,
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            titleColor: '#2C3E50',
                            bodyColor: '#2C3E50',
                            borderColor: coresSantaClara.primary,
                            borderWidth: 1,
                            cornerRadius: 8,
                            callbacks: {
                                label: function(context) {
                                    return `Valor: R$ ${context.parsed.y.toLocaleString('pt-BR', {minimumFractionDigits: 2})}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: '#E9ECEF'
                            },
                            ticks: {
                                callback: function(value) {
                                    return 'R$ ' + value.toLocaleString('pt-BR');
                                }
                            }
                        },
                        x: {
                            grid: {
                                color: '#E9ECEF'
                            }
                        }
                    }
                }
            });
        }

        // Gráfico de top unidades
        function atualizarTopUnidades() {
            const ctx = document.getElementById('topUnidadesChart').getContext('2d');

            if (unidadesChart) {
                unidadesChart.destroy();
            }

            const cores = [
                coresSantaClara.primary,
                coresSantaClara.secondary,
                coresSantaClara.accent,
                coresSantaClara.success,
                coresSantaClara.warning
            ];

            unidadesChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: dadosAtuais.topUnidades.labels || [],
                    datasets: [{
                        data: dadosAtuais.topUnidades.data || [],
                        backgroundColor: cores,
                        borderColor: '#FFFFFF',
                        borderWidth: 3
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            titleColor: '#2C3E50',
                            bodyColor: '#2C3E50',
                            borderColor: coresSantaClara.primary,
                            borderWidth: 1,
                            cornerRadius: 8,
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return `${context.label}: R$ ${context.parsed.toLocaleString('pt-BR', {minimumFractionDigits: 2})} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }

        // 🎯 NOVAS FUNÇÕES PARA PRODUTOS PADRONIZADOS
        async function carregarProdutosPadronizados() {
            try {
                const unidadeSelecionada = document.getElementById('unidadeSelect').value;
                const response = await fetch(`/api/top-produtos-padronizados?unidade=${unidadeSelecionada}&limit=10`);
                const dados = await response.json();

                produtosPadronizados = dados;
                atualizarGraficoProdutosPadronizados();
                atualizarSelectProdutos();

            } catch (error) {
                console.error('Erro ao carregar produtos padronizados:', error);
            }
        }

        function atualizarGraficoProdutosPadronizados() {
            const ctx = document.getElementById('topProdutosPadronizadosChart').getContext('2d');

            if (produtosPadronizadosChart) {
                produtosPadronizadosChart.destroy();
            }

            produtosPadronizadosChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: produtosPadronizados.labels || [],
                    datasets: [{
                        label: 'Valor Total Comprado (R$)',
                        data: produtosPadronizados.data || [],
                        backgroundColor: coresSantaClara.primary,
                        borderColor: coresSantaClara.primary,
                        borderWidth: 1,
                        borderRadius: 8
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            titleColor: '#2C3E50',
                            bodyColor: '#2C3E50',
                            borderColor: coresSantaClara.primary,
                            borderWidth: 1,
                            cornerRadius: 8,
                            callbacks: {
                                afterBody: function(context) {
                                    const index = context[0].dataIndex;
                                    const detalhe = produtosPadronizados.detalhes[index];
                                    if (detalhe) {
                                        return [
                                            `📦 Compras: ${detalhe.qtd_compras}`,
                                            `📊 Quantidade: ${detalhe.quantidade_total_comprada.toFixed(0)} unidades`,
                                            `🏢 Unidades: ${detalhe.qtd_unidades_compradoras}`,
                                            `🏭 Fornecedores: ${detalhe.qtd_fornecedores}`,
                                            `💲 Preço Médio: R$ ${detalhe.preco_medio_unitario.toFixed(2)}`,
                                            `📈 Variação: ${detalhe.variacao_preco_percentual}%`,
                                            `📅 ${detalhe.primeira_compra} a ${detalhe.ultima_compra}`
                                        ];
                                    }
                                    return [];
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return 'R$ ' + value.toLocaleString('pt-BR');
                                }
                            }
                        }
                    }
                }
            });
        }

        // 💉 FUNÇÕES PARA ANÁLISE DE VACINAS
        async function carregarDadosVacinas() {
            try {
                const unidadeSelecionada = document.getElementById('unidadeSelect').value;
                const response = await fetch(`/api/analise-vacinas-compradas?unidade=${unidadeSelecionada}`);
                const dados = await response.json();

                dadosVacinas = dados;
                atualizarAnaliseVacinas();

            } catch (error) {
                console.error('Erro ao carregar dados de vacinas:', error);
            }
        }

        function atualizarAnaliseVacinas() {
            const container = document.getElementById('vacinasAnalise');

            if (dadosVacinas.erro) {
                container.innerHTML = `<div class="col-12"><div class="alert alert-warning">${dadosVacinas.erro}</div></div>`;
                return;
            }

            container.innerHTML = `
                <div class="col-md-4">
                    <div class="metric-card">
                        <div class="metric-icon success">
                            <i class="fas fa-syringe"></i>
                        </div>
                        <div class="metric-value">R$ ${dadosVacinas.total_vacinas_compradas?.toLocaleString('pt-BR', {minimumFractionDigits: 2}) || '0,00'}</div>
                        <div class="metric-label">Total em Vacinas Compradas</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="metric-card">
                        <div class="metric-icon primary">
                            <i class="fas fa-vial"></i>
                        </div>
                        <div class="metric-value">${dadosVacinas.total_doses_compradas?.toLocaleString('pt-BR') || '0'}</div>
                        <div class="metric-label">Total de Doses Compradas</div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="metric-card">
                        <div class="metric-icon gold">
                            <i class="fas fa-pills"></i>
                        </div>
                        <div class="metric-value">${dadosVacinas.tipos_vacinas || 0}</div>
                        <div class="metric-label">Tipos de Vacinas</div>
                    </div>
                </div>
            `;
        }

        // 📈 FUNÇÕES PARA HISTÓRICO DE PREÇOS
        function atualizarSelectProdutos() {
            const select = document.getElementById('produtoHistoricoSelect');
            select.innerHTML = '<option value="">Selecione um produto...</option>';

            if (produtosPadronizados.labels) {
                produtosPadronizados.labels.forEach((produto, index) => {
                    const option = document.createElement('option');
                    option.value = produto;
                    option.textContent = produto;
                    select.appendChild(option);
                });
            }
        }

        async function carregarHistoricoPreco() {
            const produto = document.getElementById('produtoHistoricoSelect').value;
            if (!produto) return;

            try {
                const unidadeSelecionada = document.getElementById('unidadeSelect').value;
                const response = await fetch(`/api/analise-oscilacao-precos?produto=${encodeURIComponent(produto)}&unidade=${unidadeSelecionada}`);
                const dados = await response.json();

                atualizarGraficoHistoricoPreco(dados, produto);

            } catch (error) {
                console.error('Erro ao carregar histórico de preços:', error);
            }
        }

        function atualizarGraficoHistoricoPreco(dados, produto) {
            const ctx = document.getElementById('historicoPrecoChart').getContext('2d');

            if (historicoPrecoChart) {
                historicoPrecoChart.destroy();
            }

            // Preparar dados do histórico (simulado por enquanto)
            const labels = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun'];
            const precos = [100, 105, 98, 110, 115, 108]; // Dados simulados

            historicoPrecoChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: `Preço Unitário - ${produto}`,
                        data: precos,
                        borderColor: coresSantaClara.primary,
                        backgroundColor: coresSantaClara.primary + '20',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: true },
                        tooltip: {
                            backgroundColor: 'rgba(255, 255, 255, 0.95)',
                            titleColor: '#2C3E50',
                            bodyColor: '#2C3E50',
                            borderColor: coresSantaClara.primary,
                            borderWidth: 1,
                            cornerRadius: 8,
                            callbacks: {
                                label: function(context) {
                                    return `Preço: R$ ${context.parsed.y.toFixed(2)}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: false,
                            ticks: {
                                callback: function(value) {
                                    return 'R$ ' + value.toFixed(2);
                                }
                            }
                        }
                    }
                }
            });
        }

        // Função para atualizar dados
        function atualizarDados() {
            carregarDados();
            carregarDadosVacinas();
            carregarProdutosPadronizados();
        }

        // Função para mostrar erros
        function mostrarErro(mensagem) {
            // Implementar toast ou modal de erro
            console.error(mensagem);
        }

        // Função para exportar dados
        function exportarDados() {
            const unidade = document.getElementById('unidadeSelect').value;
            window.open(`/api/exportar?unidade=${unidade}`, '_blank');
        }

        // Auto-refresh a cada 5 minutos
        setInterval(carregarDados, 300000);
    </script>
</body>
</html>
