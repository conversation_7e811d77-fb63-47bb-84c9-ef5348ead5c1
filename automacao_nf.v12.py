import os
import pickle
import base64
import re
import io
from datetime import datetime
import pdfplumber
import pandas as pd
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import traceback

# --- CONFIGURAÇÕES GLOBAIS ---
SCOPES = ['https://www.googleapis.com/auth/gmail.modify']
CAMINHO_CREDENCIAIS = 'credentials.json'
ARQUIVO_TOKEN = 'token.pickle'
PASTA_BASE = os.getcwd()
PASTA_INCONSISTENCIAS = os.path.join(PASTA_BASE, 'INCONSISTENCIAS')
ARQUIVO_UNIDADES_CNPJ = 'unidades_cnpjs.csv'

# Planilhas de controle
PLANILHA_FATURAMENTO_NF = 'controle_faturamento.xlsx' # Nome mais claro
PLANILHA_PRODUTOS = 'controle_produtos.xlsx'
PLANILHA_BOLETOS = 'controle_boletos.xlsx'
PLANILHA_IMPOSTOS = 'controle_impostos.xlsx'

# Palavras-chave para classificação e busca
KW_NOTAS_FISCAIS = ['nota fiscal', 'nfe', 'nf-e', 'danfe']
KW_BOLETOS = ['boleto', 'fatura', 'cobrança', 'duplicata']
KW_IMPOSTOS = ['pis', 'cofins', 'irpj', 'csll', 'iss', 'das', 'darf', 'guia', 'recolhimento']

# --- FUNÇÕES GMAIL ---
def autenticar_gmail():
    creds = None
    if os.path.exists(ARQUIVO_TOKEN):
        with open(ARQUIVO_TOKEN, 'rb') as token:
            creds = pickle.load(token)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            if os.path.exists(ARQUIVO_TOKEN): os.remove(ARQUIVO_TOKEN)
            try:
                flow = InstalledAppFlow.from_client_secrets_file(CAMINHO_CREDENCIAIS, SCOPES)
                creds = flow.run_local_server(port=0)
            except FileNotFoundError:
                print(f"ERRO: Arquivo '{CAMINHO_CREDENCIAIS}' não encontrado.")
                return None
        with open(ARQUIVO_TOKEN, 'wb') as token:
            pickle.dump(creds, token)
    try:
        service = build('gmail', 'v1', credentials=creds)
        print("Autenticação com Gmail bem-sucedida!")
        return service
    except HttpError as error:
        print(f'Erro ao construir o serviço do Gmail: {error}')
        return None

def buscar_emails(service):
    query_parts = [f'subject:({kw})' for kw in KW_NOTAS_FISCAIS + KW_BOLETOS + KW_IMPOSTOS]
    query = f"is:unread ({' OR '.join(query_parts)})"
    print(f"Buscando e-mails com a query: {query}")
    try:
        result = service.users().messages().list(userId='me', q=query).execute()
        return result.get('messages', [])
    except HttpError as error:
        print(f'Erro ao buscar e-mails: {error}')
        return []

def baixar_anexo_temporario(service, msg_id, part):
    try:
        attachment = service.users().messages().attachments().get(userId='me', messageId=msg_id, id=part['body']['attachmentId']).execute()
        return base64.urlsafe_b64decode(attachment['data'].encode('UTF-8'))
    except HttpError as error:
        print(f"  -> Erro ao baixar anexo: {error}")
        return None

def marcar_email_como_processado(service, msg_id):
    try:
        service.users().messages().modify(userId='me', id=msg_id, body={'removeLabelIds': ['UNREAD']}).execute()
        print(f"  -> E-mail ID {msg_id} marcado como processado.")
    except HttpError as error:
        print(f"  -> Erro ao marcar e-mail como lido: {error}")

# --- FUNÇÕES DE LÓGICA E PROCESSAMENTO ---

def carregar_unidades(arquivo_csv):
    try:
        df = pd.read_csv(arquivo_csv, sep=';', dtype=str)
        df['CNPJ_LIMPO'] = df['CNPJ'].str.replace(r'\D', '', regex=True)
        print("Tabela de unidades carregada com sucesso.")
        return df
    except FileNotFoundError:
        print(f"ERRO CRÍTICO: Arquivo de unidades '{arquivo_csv}' não encontrado.")
        return None

def classificar_documento(assunto, nome_arquivo):
    texto_busca = f"{assunto.lower()} {nome_arquivo.lower()}"
    if any(kw in texto_busca for kw in KW_BOLETOS): return 'Boletos'
    if any(kw in texto_busca for kw in KW_NOTAS_FISCAIS): return 'Notas Fiscais'
    if any(kw in texto_busca for kw in KW_IMPOSTOS): return 'Impostos'
    return 'Outros'

def extrair_cnpj_destinatario(texto_pdf):
    try:
        bloco_destinatario = re.search(r'DESTINAT[ÁA]RIO(?:/REMETENTE)?\s*(.*?)(?:FATURA|CÁLCULO DO IMPOSTO)', texto_pdf, re.DOTALL | re.IGNORECASE)
        if bloco_destinatario:
            texto_bloco = bloco_destinatario.group(1)
            cnpj_match = re.search(r'(\d{2}\.\d{3}\.\d{3}/\d{4}-\d{2})', texto_bloco) or re.search(r'(\d{14})', texto_bloco)
            if cnpj_match:
                return re.sub(r'\D', '', cnpj_match.group(1))
    except Exception: pass
    return None

def extrair_todos_cnpjs(texto_pdf):
    try:
        cnpjs_encontrados = re.findall(r'(\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}|\d{14})', texto_pdf)
        return list(set([re.sub(r'\D', '', cnpj) for cnpj in cnpjs_encontrados])) # Retorna lista sem duplicados
    except Exception: return []

def extrair_dados_nfe(texto_pdf):
    dados = {}
    try:
        dados['Numero Nota'] = (re.search(r'N[Ff]-e\s*N[º°.]\s*(\d[\d. ]*)', texto_pdf, re.S) or re.search(r'N[º°.]\s*(\d[\d. ]{5,})', texto_pdf)).group(1).strip().replace(" ", "").replace(".", "")
        dados['Data Emissao'] = (re.search(r'EMISSÃO\s*([\d/]+)', texto_pdf, re.I) or re.search(r'Data de Emissão\n\s*([\d/]+)', texto_pdf, re.I)).group(1)
        emitente_bloco = re.search(r'(?:Identificação do emitente|remetente)\s*(.*?)(?:\n\s*Endereço|\n\s*AER|\n\s*AV|\n\s*RUA|\n\s*ESTRADA)', texto_pdf, re.DOTALL | re.I)
        dados['Fornecedor'] = emitente_bloco.group(1).replace('\n', ' ').strip() if emitente_bloco else 'NÃO ENCONTRADO'
        return dados
    except (AttributeError, IndexError): return None

def extrair_dados_boleto(texto_pdf):
    try:
        dados = {}
        dados['Vencimento'] = (re.search(r'Vencimento\s*[:\s]*([\d/]+)', texto_pdf, re.I) or re.search(r'\n([\d/]{10})\s*[\d,.-]+', texto_pdf)).group(1)
        dados['Valor'] = (re.search(r'Valor do Documento\s*[:\s]*R?\$\s*([\d,.-]+)', texto_pdf, re.I) or re.search(r'VALOR\s*COBRADO\s*[:\s]*R?\$\s*([\d,.-]+)', texto_pdf, re.I)).group(1)
        return dados
    except(AttributeError, IndexError): return None

def extrair_itens_nfe(pdf_obj):
    itens_encontrados = []
    try:
        for page in pdf_obj.pages:
            tables = page.extract_tables({"vertical_strategy": "text", "horizontal_strategy": "lines", "snap_tolerance": 3, "text_tolerance": 3})
            for table in tables:
                if not (table and table[0]): continue
                header = str(table[0]).upper()
                if 'DESCRIÇÃO' in header or 'PROD' in header:
                    for row in table[1:]:
                        if len(row) > 8 and row[1]: # Garante que a linha e a descrição não são vazias
                            item = {'Cod. Produto': row[0] or '','Descricao': (row[1] or '').replace('\n', ' '),'Quantidade': row[6] or '0','Valor Unitario': row[7] or '0','Valor Total': row[8] or '0'}
                            if 'DADOS DO ISSQN' not in item['Descricao'].upper():
                                itens_encontrados.append(item)
    except Exception as e:
        print(f"    -> Erro ao extrair tabela de itens: {e}")
    return itens_encontrados

def atualizar_planilha(nome_planilha, novos_dados_lista):
    try:
        df_novo = pd.DataFrame(novos_dados_lista)
        if os.path.exists(nome_planilha):
            df_antigo = pd.read_excel(nome_planilha, engine='openpyxl', dtype=str)
            df_final = pd.concat([df_antigo, df_novo], ignore_index=True)
        else:
            df_final = df_novo
        
        df_final.to_excel(nome_planilha, index=False, engine='openpyxl')
        print(f"  -> Planilha '{nome_planilha}' atualizada com sucesso.")
    except Exception as e:
        print(f"    -> ERRO ao atualizar a planilha '{nome_planilha}': {e}")

def salvar_arquivo(dados_pdf, unidade, tipo_doc, nome_original):
    agora = datetime.now()
    ano, mes = str(agora.year), f"{agora.month:02d}"
    nome_pasta_unidade = "".join(c for c in unidade if c.isalnum() or c in (' ', '-')).rstrip()
    if unidade == "INCONSISTENCIAS": caminho_final = PASTA_INCONSISTENCIAS
    else: caminho_final = os.path.join(PASTA_BASE, nome_pasta_unidade, ano, mes, tipo_doc)
    os.makedirs(caminho_final, exist_ok=True)
    caminho_arquivo = os.path.join(caminho_final, nome_original)
    with open(caminho_arquivo, 'wb') as f: f.write(dados_pdf)
    print(f"  -> Arquivo salvo em: {caminho_arquivo}")
    return caminho_arquivo

def processar_anexo(dados_pdf, nome_arquivo, assunto, df_unidades):
    tipo_doc = classificar_documento(assunto, nome_arquivo)
    if tipo_doc == 'Outros':
        print(f"  -> Documento '{nome_arquivo}' classificado como 'Outros'. Ignorando.")
        return

    print(f"  -> Classificado como: {tipo_doc}")
    pdf_obj = None
    try:
        pdf_obj = pdfplumber.open(io.BytesIO(dados_pdf))
        texto_pdf = "\n".join(page.extract_text(x_tolerance=1, y_tolerance=1) or "" for page in pdf_obj.pages)
    except Exception as e:
        print(f"    -> Erro grave ao ler PDF: {e}. Movendo para inconsistências.")
        salvar_arquivo(dados_pdf, "INCONSISTENCIAS", "", nome_arquivo)
        if pdf_obj: pdf_obj.close()
        return

    unidade, cnpj_identificado = "INCONSISTENCIAS", None
    
    cnpj_dest = extrair_cnpj_destinatario(texto_pdf)
    if cnpj_dest and any(df_unidades['CNPJ_LIMPO'] == cnpj_dest):
        unidade = df_unidades.loc[df_unidades['CNPJ_LIMPO'] == cnpj_dest, 'UNIDADE'].iloc[0]
        cnpj_identificado = cnpj_dest
        print(f"  -> CNPJ do destinatário {cnpj_dest} encontrado! Unidade: {unidade}")
    else:
        todos_cnpjs_no_pdf = extrair_todos_cnpjs(texto_pdf)
        for cnpj in todos_cnpjs_no_pdf:
            if any(df_unidades['CNPJ_LIMPO'] == cnpj):
                unidade = df_unidades.loc[df_unidades['CNPJ_LIMPO'] == cnpj, 'UNIDADE'].iloc[0]
                cnpj_identificado = cnpj
                print(f"  -> CNPJ genérico {cnpj} encontrado no PDF! Unidade: {unidade}")
                break 
        if unidade == "INCONSISTENCIAS":
            for _, row in df_unidades.iterrows():
                if row['UNIDADE'].lower().replace(" ", "") in assunto.lower().replace(" ", ""):
                    unidade, cnpj_identificado = row['UNIDADE'], row['CNPJ_LIMPO']
                    print(f"  -> Nome da unidade '{unidade}' encontrado no assunto do e-mail!")
                    break
    
    if unidade == "INCONSISTENCIAS": print("  -> Não foi possível determinar a unidade.")

    caminho_salvo = salvar_arquivo(dados_pdf, unidade, tipo_doc, nome_arquivo)
    
    if unidade != "INCONSISTENCIAS":
        if tipo_doc == 'Notas Fiscais':
            dados_gerais = extrair_dados_nfe(texto_pdf)
            itens_nfe = extrair_itens_nfe(pdf_obj)
            if dados_gerais and itens_nfe:
                print("    -> Extração de dados e itens da NFe bem-sucedida!")
                dados_produtos = [{'Unidade': unidade, 'CNPJ Destinatario': cnpj_identificado, 'Data Emissao': dados_gerais.get('Data Emissao'), 'Numero Nota': dados_gerais.get('Numero Nota'),'Fornecedor': dados_gerais.get('Fornecedor'), 'Cod. Produto': item.get('Cod. Produto'),'Descricao Produto': item.get('Descricao'), 'Quantidade': item.get('Quantidade'),'Valor Unitario': item.get('Valor Unitario'), 'Valor Total Item': item.get('Valor Total'),'Arquivo': os.path.basename(caminho_salvo)} for item in itens_nfe]
                atualizar_planilha(PLANILHA_PRODUTOS, dados_produtos)
                # Também atualiza a planilha de faturamento
                atualizar_planilha(PLANILHA_FATURAMENTO_NF, [dados_produtos[0]])
            else:
                 print("   -> ATENÇÃO: Falha na extração de dados gerais ou itens da NFe. O arquivo foi salvo, mas verifique-o.")
        else:
            dados_planilha = [{'Unidade': unidade, 'CNPJ Encontrado': cnpj_identificado, 'Tipo Documento': tipo_doc, 'Data Processamento': datetime.now().strftime("%d/%m/%Y %H:%M:%S"), 'Arquivo': os.path.basename(caminho_salvo)}]
            if tipo_doc == 'Boletos':
                dados_boleto = extrair_dados_boleto(texto_pdf)
                if dados_boleto: dados_planilha[0].update(dados_boleto)
                atualizar_planilha(PLANILHA_BOLETOS, dados_planilha)
            elif tipo_doc == 'Impostos':
                atualizar_planilha(PLANILHA_IMPOSTOS, dados_planilha)
    
    if pdf_obj: pdf_obj.close()

# --- BLOCO PRINCIPAL ---
if __name__ == '__main__':
    print("Iniciando automação...")
    os.makedirs(PASTA_INCONSISTENCIAS, exist_ok=True)
    df_unidades = carregar_unidades(ARQUIVO_UNIDADES_CNPJ)
    if df_unidades is None: exit()
    
    servico_gmail = autenticar_gmail()
    if servico_gmail:
        emails = buscar_emails(servico_gmail)
        if not emails: print("Nenhum e-mail novo encontrado.")
        else:
            print(f"Encontrados {len(emails)} e-mails para analisar.")
            for email_info in emails:
                msg_id = email_info['id']
                try:
                    message = servico_gmail.users().messages().get(userId='me', id=msg_id, format='full').execute()
                    payload = message.get('payload', {})
                    headers = payload.get('headers', [])
                    assunto = next((h['value'] for h in headers if h['name'].lower() == 'subject'), '')
                    print(f"\n[+] Processando e-mail ID: {msg_id} | Assunto: '{assunto}'")
                    parts_a_verificar = [payload]
                    anexos_encontrados_neste_email = False
                    while parts_a_verificar:
                        part = parts_a_verificar.pop(0)
                        if 'parts' in part: parts_a_verificar.extend(part.get('parts', []))
                        if part.get('filename') and part.get('filename').lower().endswith('.pdf'):
                            anexos_encontrados_neste_email = True
                            print(f"  -> Anexo PDF encontrado: {part.get('filename')}")
                            dados_anexo = baixar_anexo_temporario(servico_gmail, msg_id, part)
                            if dados_anexo:
                                processar_anexo(dados_anexo, part.get('filename'), assunto, df_unidades)
                    if not anexos_encontrados_neste_email: print("  -> Nenhum anexo PDF encontrado neste e-mail.")
                    marcar_email_como_processado(servico_gmail, msg_id)
                except Exception as e:
                    print(f"\nERRO GERAL ao processar o e-mail ID {msg_id}: {e}")
                    traceback.print_exc()
    print("\nFim da execução.")