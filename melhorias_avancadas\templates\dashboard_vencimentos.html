<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SantaClara - Dashboard de Vencimentos Inteligente</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --primary-blue: #5B9BD5;
            --secondary-blue: #7FB3D3;
            --accent-gold: #F4C430;
            --light-blue: #E8F4FD;
            --dark-blue: #2E5984;
            --text-primary: #2C3E50;
            --text-secondary: #7F8C8D;
            --success: #27AE60;
            --warning: #F39C12;
            --danger: #E74C3C;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --border: #E9ECEF;
            --shadow: rgba(91, 155, 213, 0.1);

            /* Cores específicas para vencimentos */
            --vencido: #E74C3C;
            --critico: #F39C12;
            --atencao: #F1C40F;
            --normal: #27AE60;
            --futuro: #3498DB;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, var(--light-blue) 0%, var(--white) 50%, var(--light-blue) 100%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* Header específico para vencimentos */
        .header-vencimentos {
            background: linear-gradient(135deg, var(--danger) 0%, var(--warning) 50%, var(--primary-blue) 100%);
            color: var(--white);
            padding: 2rem 0;
            box-shadow: 0 4px 20px var(--shadow);
            position: relative;
            overflow: hidden;
        }

        .header-vencimentos::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(244, 196, 48, 0.1));
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--accent-gold) 0%, #FFD700 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: var(--white);
            box-shadow: 0 4px 15px rgba(244, 196, 48, 0.3);
        }

        .brand-text {
            font-weight: 700;
            font-size: 2rem;
            margin: 0;
        }

        .brand-subtitle {
            font-weight: 300;
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }

        /* Cards de vencimento com cores específicas */
        .vencimento-card {
            background: var(--white);
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px var(--shadow);
            border: 1px solid var(--border);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .vencimento-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
        }

        .vencimento-card.vencido::before { background: var(--vencido); }
        .vencimento-card.critico::before { background: var(--critico); }
        .vencimento-card.atencao::before { background: var(--atencao); }
        .vencimento-card.normal::before { background: var(--normal); }
        .vencimento-card.futuro::before { background: var(--futuro); }

        .vencimento-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px var(--shadow);
        }

        .vencimento-icon {
            width: 60px;
            height: 60px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--white);
            margin-bottom: 1rem;
        }

        .vencimento-icon.vencido { background: linear-gradient(135deg, var(--vencido), #C0392B); }
        .vencimento-icon.critico { background: linear-gradient(135deg, var(--critico), #E67E22); }
        .vencimento-icon.atencao { background: linear-gradient(135deg, var(--atencao), #F4D03F); }
        .vencimento-icon.normal { background: linear-gradient(135deg, var(--normal), #2ECC71); }
        .vencimento-icon.futuro { background: linear-gradient(135deg, var(--futuro), #5DADE2); }

        .vencimento-valor {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .vencimento-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .vencimento-detalhes {
            font-size: 0.8rem;
            margin-top: 0.5rem;
            padding-top: 0.5rem;
            border-top: 1px solid var(--border);
        }

        /* Timeline de vencimentos */
        .timeline-container {
            background: var(--white);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 8px 32px var(--shadow);
            border: 1px solid var(--border);
            margin-bottom: 2rem;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border);
        }

        .timeline-item:last-child {
            border-bottom: none;
        }

        .timeline-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 1rem;
            flex-shrink: 0;
        }

        .timeline-dot.vencido { background: var(--vencido); }
        .timeline-dot.critico { background: var(--critico); }
        .timeline-dot.atencao { background: var(--atencao); }
        .timeline-dot.normal { background: var(--normal); }
        .timeline-dot.futuro { background: var(--futuro); }

        /* Filtros avançados */
        .filtros-avancados {
            background: var(--white);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 20px var(--shadow);
            border: 1px solid var(--border);
        }

        .form-select, .form-control {
            border: 2px solid var(--border);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .form-select:focus, .form-control:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(91, 155, 213, 0.25);
        }

        /* Gráficos */
        .chart-container {
            background: var(--white);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 8px 32px var(--shadow);
            border: 1px solid var(--border);
            margin-bottom: 2rem;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .chart-title i {
            color: var(--primary-blue);
        }

        /* Alertas de vencimento */
        .alerta-vencimento {
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid;
        }

        .alerta-vencimento.critico {
            background: rgba(231, 76, 60, 0.1);
            border-color: var(--vencido);
            color: var(--vencido);
        }

        .alerta-vencimento.atencao {
            background: rgba(243, 156, 18, 0.1);
            border-color: var(--warning);
            color: var(--warning);
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .brand-text { font-size: 1.6rem; }
            .vencimento-valor { font-size: 1.4rem; }
            .chart-container { padding: 1rem; }
        }

        /* Animações */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .vencimento-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .vencimento-card:nth-child(1) { animation-delay: 0.1s; }
        .vencimento-card:nth-child(2) { animation-delay: 0.2s; }
        .vencimento-card:nth-child(3) { animation-delay: 0.3s; }
        .vencimento-card:nth-child(4) { animation-delay: 0.4s; }
        .vencimento-card:nth-child(5) { animation-delay: 0.5s; }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner {
            border: 3px solid var(--border);
            border-top: 3px solid var(--primary-blue);
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Badges de status */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-badge.vencido { background: rgba(231, 76, 60, 0.1); color: var(--vencido); }
        .status-badge.critico { background: rgba(243, 156, 18, 0.1); color: var(--critico); }
        .status-badge.atencao { background: rgba(241, 196, 15, 0.1); color: var(--atencao); }
        .status-badge.normal { background: rgba(39, 174, 96, 0.1); color: var(--normal); }
        .status-badge.futuro { background: rgba(52, 152, 219, 0.1); color: var(--futuro); }
    </style>
</head>
<body>
    <!-- Header Vencimentos -->
    <header class="header-vencimentos">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <div class="logo-container">
                        <div class="logo-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                        <div>
                            <h1 class="brand-text">SantaClara Pagamentos</h1>
                            <p class="brand-subtitle">🚨 Dashboard de Vencimentos de Boletos e Contas a Pagar</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <h2 style="margin: 0; font-weight: 300;">Controle Financeiro</h2>
                    <p style="margin: 0; opacity: 0.9;">Boletos • Pagamentos • Fluxo de Caixa</p>
                </div>
            </div>
        </div>
    </header>

    <!-- Filtros Avançados -->
    <div class="container mt-4">
        <div class="filtros-avancados">
            <div class="row align-items-center">
                <div class="col-md-3">
                    <label for="unidadeSelect" class="form-label fw-semibold">
                        <i class="fas fa-building text-primary me-2"></i>Unidade
                    </label>
                    <select class="form-select" id="unidadeSelect">
                        <option value="TODAS">🏢 Todas as Unidades</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="periodoSelect" class="form-label fw-semibold">
                        <i class="fas fa-calendar text-primary me-2"></i>Período de Análise
                    </label>
                    <select class="form-select" id="periodoSelect">
                        <option value="30">📅 Próximos 30 dias</option>
                        <option value="60">📅 Próximos 60 dias</option>
                        <option value="90">📅 Próximos 90 dias</option>
                        <option value="120">📅 Próximos 120 dias</option>
                        <option value="150">📅 Próximos 150 dias</option>
                        <option value="365">📅 Próximo ano</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="fornecedorSelect" class="form-label fw-semibold">
                        <i class="fas fa-truck text-primary me-2"></i>Fornecedor
                    </label>
                    <select class="form-select" id="fornecedorSelect">
                        <option value="TODOS">🚚 Todos os Fornecedores</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-semibold">
                        <i class="fas fa-sync text-primary me-2"></i>Ações
                    </label>
                    <button class="btn btn-outline-primary w-100" onclick="atualizarDados()">
                        <i class="fas fa-refresh me-2"></i>Atualizar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Alertas Críticos -->
    <div class="container">
        <div id="alertasCriticos">
            <!-- Alertas serão inseridos aqui via JavaScript -->
        </div>
    </div>

    <!-- Métricas de Vencimentos -->
    <div class="container">
        <div class="row g-4" id="metricas-vencimentos">
            <!-- Cards serão inseridos aqui via JavaScript -->
        </div>
    </div>

    <!-- 📊 ANÁLISE TEMPORAL DE VENCIMENTOS -->
    <div class="container mt-4">
        <div class="row g-4">
            <div class="col-lg-8">
                <div class="chart-container">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        📊 Evolução de Vencimentos de Boletos por Período
                    </h3>
                    <canvas id="evolucaoVencimentosChart" height="100"></canvas>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="chart-container">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-pie"></i>
                        🎯 Status dos Boletos (Pago/Pendente/Vencido)
                    </h3>
                    <canvas id="distribuicaoStatusChart" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 🏢 ANÁLISE POR UNIDADE -->
    <div class="container mt-4">
        <div class="row g-4">
            <div class="col-12">
                <div class="chart-container">
                    <h3 class="chart-title">
                        <i class="fas fa-building"></i>
                        🏢 Boletos a Vencer por Unidade (30/60/90 dias)
                    </h3>
                    <canvas id="vencimentosPorUnidadeChart" height="120"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 🚚 ANÁLISE POR FORNECEDOR -->
    <div class="container mt-4">
        <div class="row g-4">
            <div class="col-lg-6">
                <div class="chart-container">
                    <h3 class="chart-title">
                        <i class="fas fa-truck"></i>
                        🚚 Fornecedores com Boletos Vencendo
                    </h3>
                    <canvas id="fornecedoresCriticosChart" height="150"></canvas>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="chart-container">
                    <h3 class="chart-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        ⚠️ Alertas de Pagamentos em Atraso
                    </h3>
                    <div id="riscoInadimplencia">
                        <!-- Conteúdo será carregado via JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 📈 CRUZAMENTO COMPRAS × VENCIMENTOS -->
    <div class="container mt-4">
        <div class="row g-4">
            <div class="col-12">
                <div class="chart-container">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-area"></i>
                        📈 Fluxo de Caixa: Compras vs Pagamentos de Boletos
                    </h3>
                    <div class="row">
                        <div class="col-md-8">
                            <canvas id="comprasVsVencimentosChart" height="100"></canvas>
                        </div>
                        <div class="col-md-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-lightbulb"></i> Insights Inteligentes:</h6>
                                <div id="insightsPreditivos">
                                    <!-- Insights serão carregados via JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 📋 TIMELINE DE VENCIMENTOS -->
    <div class="container mt-4">
        <div class="row g-4">
            <div class="col-12">
                <div class="timeline-container">
                    <h3 class="chart-title">
                        <i class="fas fa-timeline"></i>
                        📋 Cronograma de Vencimentos de Boletos
                    </h3>
                    <div id="timelineVencimentos">
                        <!-- Timeline será carregada via JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Configuração global dos gráficos
        Chart.defaults.font.family = 'Inter';
        Chart.defaults.color = '#2C3E50';

        const coresVencimentos = {
            vencido: '#E74C3C',
            critico: '#F39C12',
            atencao: '#F1C40F',
            normal: '#27AE60',
            futuro: '#3498DB',
            primary: '#5B9BD5'
        };

        // Variáveis globais
        let evolucaoChart, distribuicaoChart, unidadesChart, fornecedoresChart, comprasVsChart;
        let dadosVencimentos = {};

        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            carregarUnidades();
            carregarFornecedores();
            carregarDadosVencimentos();

            // Event listeners
            document.getElementById('unidadeSelect').addEventListener('change', carregarDadosVencimentos);
            document.getElementById('periodoSelect').addEventListener('change', carregarDadosVencimentos);
            document.getElementById('fornecedorSelect').addEventListener('change', carregarDadosVencimentos);
        });

        // Carregar unidades
        async function carregarUnidades() {
            try {
                const response = await fetch('/api/unidades');
                const unidades = await response.json();

                const select = document.getElementById('unidadeSelect');
                select.innerHTML = '<option value="TODAS">🏢 Todas as Unidades</option>';

                unidades.forEach(unidade => {
                    if (unidade.value !== 'TODAS') {
                        const option = document.createElement('option');
                        option.value = unidade.value;
                        option.textContent = `🏢 ${unidade.label}`;
                        select.appendChild(option);
                    }
                });
            } catch (error) {
                console.error('Erro ao carregar unidades:', error);
            }
        }

        // Carregar fornecedores
        async function carregarFornecedores() {
            try {
                const response = await fetch('/api/top-fornecedores?limit=50');
                const fornecedores = await response.json();

                const select = document.getElementById('fornecedorSelect');
                select.innerHTML = '<option value="TODOS">🚚 Todos os Fornecedores</option>';

                if (fornecedores.labels) {
                    fornecedores.labels.forEach(fornecedor => {
                        const option = document.createElement('option');
                        option.value = fornecedor;
                        option.textContent = `🚚 ${fornecedor}`;
                        select.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Erro ao carregar fornecedores:', error);
            }
        }

        // Carregar dados de vencimentos
        async function carregarDadosVencimentos() {
            const unidade = document.getElementById('unidadeSelect').value;
            const periodo = document.getElementById('periodoSelect').value;
            const fornecedor = document.getElementById('fornecedorSelect').value;

            try {
                document.body.classList.add('loading');

                // Carregar dados em paralelo
                const [analiseVencimentos] = await Promise.all([
                    fetch(`/api/analise-vencimentos-detalhada?unidade=${unidade}&periodo=${periodo}&fornecedor=${fornecedor}`).then(r => r.json())
                ]);

                dadosVencimentos = { analiseVencimentos };

                // Atualizar interface
                atualizarAlertas(analiseVencimentos);
                atualizarMetricas(analiseVencimentos);
                atualizarGraficos();

            } catch (error) {
                console.error('Erro ao carregar dados de vencimentos:', error);
                mostrarErro('Erro ao carregar dados. Tente novamente.');
            } finally {
                document.body.classList.remove('loading');
            }
        }

        // Atualizar alertas críticos
        function atualizarAlertas(dados) {
            const container = document.getElementById('alertasCriticos');

            let alertas = '';

            if (dados.vencidos && dados.vencidos > 0) {
                alertas += `
                    <div class="alerta-vencimento critico">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>🚨 BOLETOS VENCIDOS - AÇÃO NECESSÁRIA</h6>
                        <p><strong>${dados.vencidos}</strong> boletos em atraso precisam ser pagos urgentemente!</p>
                    </div>
                `;
            }

            if (dados.hoje && dados.hoje > 0) {
                alertas += `
                    <div class="alerta-vencimento atencao">
                        <h6><i class="fas fa-clock me-2"></i>⏰ BOLETOS VENCEM HOJE - PAGAR HOJE</h6>
                        <p><strong>${dados.hoje}</strong> boletos precisam ser pagos hoje para evitar atraso!</p>
                    </div>
                `;
            }

            container.innerHTML = alertas;
        }

        // Atualizar métricas
        function atualizarMetricas(dados) {
            const container = document.getElementById('metricas-vencimentos');

            const metricas = [
                {
                    tipo: 'vencido',
                    icon: 'fas fa-times-circle',
                    titulo: 'Boletos Vencidos',
                    valor: dados.vencidos || 0,
                    valorFormatado: 'CRÍTICO',
                    detalhes: `Pagamentos em atraso`
                },
                {
                    tipo: 'critico',
                    icon: 'fas fa-exclamation-triangle',
                    titulo: 'Vencem Hoje',
                    valor: dados.hoje || 0,
                    valorFormatado: 'URGENTE',
                    detalhes: `Pagar hoje`
                },
                {
                    tipo: 'atencao',
                    icon: 'fas fa-calendar-week',
                    titulo: 'Próximos 7 dias',
                    valor: dados.semana || 0,
                    valorFormatado: 'ATENÇÃO',
                    detalhes: `Boletos a vencer`
                },
                {
                    tipo: 'normal',
                    icon: 'fas fa-calendar-alt',
                    titulo: 'Próximo Mês',
                    valor: dados.mes || 0,
                    valorFormatado: 'NORMAL',
                    detalhes: `Programados`
                },
                {
                    tipo: 'futuro',
                    icon: 'fas fa-calendar-plus',
                    titulo: 'Total Boletos',
                    valor: dados.total || 0,
                    valorFormatado: 'GERAL',
                    detalhes: `Todos os boletos`
                }
            ];

            container.innerHTML = metricas.map((metrica, index) => `
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="vencimento-card ${metrica.tipo}" style="animation-delay: ${index * 0.1}s">
                        <div class="vencimento-icon ${metrica.tipo}">
                            <i class="${metrica.icon}"></i>
                        </div>
                        <div class="vencimento-valor">${metrica.valor}</div>
                        <div class="vencimento-label">${metrica.titulo}</div>
                        <div class="vencimento-detalhes">
                            <div>${metrica.valorFormatado}</div>
                            <div>${metrica.detalhes}</div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // Atualizar gráficos (simulados)
        function atualizarGraficos() {
            // Implementação básica dos gráficos
            console.log('Gráficos atualizados');
        }

        // Função para atualizar dados
        function atualizarDados() {
            carregarDadosVencimentos();
        }

        // Função para mostrar erros
        function mostrarErro(mensagem) {
            console.error(mensagem);
            alert(mensagem);
        }
    </script>
</body>
</html>