#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🏭 CONSOLIDADOR DE FABRICANTES
Consolida e padroniza fabricantes nas planilhas existentes
Aplica melhorias de detecção para aumentar performance para 85-90%
"""

import pandas as pd
import re
import os
from datetime import datetime

def obter_fabricantes_conhecidos():
    """Retorna dicionário expandido de fabricantes conhecidos"""
    return {
        # Farmacêuticas Principais
        'PFIZER': ['PFIZER', 'COMIRNATY', 'WYETH'],
        'GSK': ['GSK', 'GLAXOSMITHKLINE', 'SHINGRIX', 'AREXVY', 'GLAXO'],
        'MERCK': ['MERCK', 'MSD', 'GARDASIL', 'MERCK SHARP', 'MERCK & CO'],
        'SANOFI': ['SANOFI', 'PASTEUR', 'SANOFI PASTEUR', 'AVENTIS'],
        'NOVARTIS': ['NOVARTIS', 'SANDOZ'],
        'ROCHE': ['ROCHE', 'GENENTECH'],
        'BAYER': ['BAYER', '<PERSON>Y<PERSON> AG'],
        'JOHNSON & JOHNSON': ['JANSSEN', 'JOHNSON', 'J&J', 'JOHNSON & JOHNSON'],
        'ASTRAZENECA': ['ASTRAZENECA', 'ZENECA'],
        'MODERNA': ['MODERNA'],
        'ABBOTT': ['ABBOTT', 'ABBOTT LABORATORIES'],
        'BOEHRINGER': ['BOEHRINGER', 'BOEHRINGER INGELHEIM'],
        'TAKEDA': ['TAKEDA', 'TAKEDA PHARMA'],
        'GILEAD': ['GILEAD', 'GILEAD SCIENCES'],
        'AMGEN': ['AMGEN'],
        'BRISTOL': ['BRISTOL', 'BMS', 'BRISTOL MYERS', 'BRISTOL-MYERS'],
        'ELI LILLY': ['LILLY', 'ELI LILLY'],
        
        # Institutos Brasileiros
        'BUTANTAN': ['BUTANTAN', 'INSTITUTO BUTANTAN'],
        'FIOCRUZ': ['FIOCRUZ', 'BIO-MANGUINHOS', 'BIOMANGUINHOS'],
        'FUNDAÇÃO OSWALDO CRUZ': ['OSWALDO CRUZ', 'FUNDACAO OSWALDO'],
        
        # Fabricantes Asiáticos
        'SINOVAC': ['SINOVAC', 'CORONAVAC'],
        'SERUM INSTITUTE': ['SERUM INSTITUTE', 'SERUM', 'SII'],
        'SINOPHARM': ['SINOPHARM'],
        'BHARAT BIOTECH': ['BHARAT', 'COVAXIN'],
        
        # Fabricantes Nacionais
        'EUROFARMA': ['EUROFARMA'],
        'EMS': ['EMS', 'EMS PHARMA'],
        'HYPERA': ['HYPERA', 'HYPERA PHARMA'],
        'CRISTÁLIA': ['CRISTALIA', 'CRISTÁLIA'],
        'UNIÃO QUÍMICA': ['UNIAO QUIMICA', 'UNIÃO QUÍMICA'],
        'MEDLEY': ['MEDLEY'],
        'GERMED': ['GERMED'],
        'BIOSINTÉTICA': ['BIOSINTETICA', 'BIOSINTÉTICA'],
        'BLAU': ['BLAU FARMACÊUTICA', 'BLAU'],
        'APSEN': ['APSEN'],
        
        # Distribuidoras/Importadoras
        'DIMED': ['DIMED'],
        'PANPHARMA': ['PANPHARMA', 'PAN PHARMA'],
        'PROFARMA': ['PROFARMA'],
        'ONCO': ['ONCO', 'ONCOFARMA'],
        
        # Genéricos
        'GENÉRICO': ['GENERICO', 'GENÉRICO', 'MEDICAMENTO GENERICO'],
        'SIMILAR': ['SIMILAR', 'MEDICAMENTO SIMILAR']
    }

def identificar_fabricante(descricao):
    """Identifica fabricante na descrição do produto"""
    if not descricao or pd.isna(descricao):
        return ''
    
    descricao_upper = str(descricao).upper()
    fabricantes_conhecidos = obter_fabricantes_conhecidos()
    
    # Buscar fabricante com prioridade (mais específico primeiro)
    for fab_nome, variacoes in fabricantes_conhecidos.items():
        for variacao in variacoes:
            if variacao in descricao_upper:
                return fab_nome
    
    return ''

def detectar_fabricante_desconhecido(descricao):
    """Detecta possível fabricante desconhecido na descrição"""
    if not descricao or pd.isna(descricao):
        return None
    
    descricao_upper = str(descricao).upper()
    palavras = descricao_upper.split()
    
    # Padrões que podem indicar fabricante
    indicadores_fabricante = ['LABORATORIO', 'LAB', 'PHARMA', 'FARMACEUTICA', 'INDUSTRIA', 'IND']
    
    for i, palavra in enumerate(palavras):
        if any(ind in palavra for ind in indicadores_fabricante):
            # Pegar palavra anterior e posterior
            palavras_fabricante = []
            if i > 0:
                palavras_fabricante.append(palavras[i-1])
            palavras_fabricante.append(palavra)
            if i < len(palavras) - 1:
                palavras_fabricante.append(palavras[i+1])
            
            return ' '.join(palavras_fabricante[:3])  # Máximo 3 palavras
    
    return None

def consolidar_planilha_produtos():
    """Consolida fabricantes na planilha de produtos"""
    arquivo = 'controle_produtos.xlsx'
    
    if not os.path.exists(arquivo):
        print(f"❌ Arquivo {arquivo} não encontrado!")
        return
    
    print(f"📊 Consolidando fabricantes em {arquivo}...")
    
    # Carregar planilha
    df = pd.read_excel(arquivo)
    print(f"   📋 Total de registros: {len(df):,}")
    
    # Verificar se coluna Fabricante existe
    if 'Fabricante' not in df.columns:
        df['Fabricante'] = ''
        print("   🆕 Coluna 'Fabricante' criada")
    
    # Estatísticas antes
    fabricantes_preenchidos_antes = df['Fabricante'].notna().sum()
    fabricantes_vazios_antes = df['Fabricante'].isna().sum() + (df['Fabricante'] == '').sum()
    
    print(f"   📊 ANTES - Preenchidos: {fabricantes_preenchidos_antes:,} | Vazios: {fabricantes_vazios_antes:,}")
    
    # Consolidar fabricantes
    fabricantes_desconhecidos = set()
    melhorias = 0
    
    for index, row in df.iterrows():
        fabricante_atual = row.get('Fabricante', '')
        descricao = row.get('Descricao Produto', '')
        
        # Se fabricante está vazio ou é genérico, tentar identificar
        if not fabricante_atual or fabricante_atual == '' or pd.isna(fabricante_atual):
            novo_fabricante = identificar_fabricante(descricao)
            
            if novo_fabricante:
                df.at[index, 'Fabricante'] = novo_fabricante
                melhorias += 1
            else:
                # Detectar possível fabricante desconhecido
                possivel_fabricante = detectar_fabricante_desconhecido(descricao)
                if possivel_fabricante:
                    fabricantes_desconhecidos.add(f"{possivel_fabricante} | {descricao[:50]}...")
    
    # Estatísticas depois
    fabricantes_preenchidos_depois = df['Fabricante'].notna().sum()
    fabricantes_vazios_depois = df['Fabricante'].isna().sum() + (df['Fabricante'] == '').sum()
    
    print(f"   📊 DEPOIS - Preenchidos: {fabricantes_preenchidos_depois:,} | Vazios: {fabricantes_vazios_depois:,}")
    print(f"   ✅ MELHORIAS: {melhorias:,} fabricantes identificados")
    
    # Salvar planilha atualizada
    df.to_excel(arquivo, index=False)
    print(f"   💾 Planilha salva com sucesso!")
    
    # Relatório de fabricantes desconhecidos
    if fabricantes_desconhecidos:
        print(f"\n🔔 FABRICANTES DESCONHECIDOS DETECTADOS: {len(fabricantes_desconhecidos)}")
        
        # Salvar em arquivo de log
        with open('fabricantes_desconhecidos.log', 'w', encoding='utf-8') as f:
            f.write(f"FABRICANTES DESCONHECIDOS DETECTADOS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")
            
            for i, fabricante in enumerate(sorted(fabricantes_desconhecidos), 1):
                f.write(f"{i:3d}. {fabricante}\n")
                if i <= 10:  # Mostrar apenas os primeiros 10 no console
                    print(f"   {i:2d}. {fabricante}")
        
        if len(fabricantes_desconhecidos) > 10:
            print(f"   ... e mais {len(fabricantes_desconhecidos) - 10} (ver fabricantes_desconhecidos.log)")
        
        print(f"\n💡 SUGESTÃO: Revisar o arquivo 'fabricantes_desconhecidos.log' e adicionar novos fabricantes ao sistema")
    
    # Estatísticas finais
    performance = (fabricantes_preenchidos_depois / len(df)) * 100
    print(f"\n📈 PERFORMANCE FINAL: {performance:.1f}% de preenchimento")
    
    return df

def gerar_relatorio_fabricantes():
    """Gera relatório detalhado dos fabricantes"""
    arquivo = 'controle_produtos.xlsx'
    
    if not os.path.exists(arquivo):
        print(f"❌ Arquivo {arquivo} não encontrado!")
        return
    
    df = pd.read_excel(arquivo)
    
    if 'Fabricante' not in df.columns:
        print("❌ Coluna 'Fabricante' não encontrada!")
        return
    
    print("\n📊 RELATÓRIO DE FABRICANTES")
    print("=" * 60)
    
    # Estatísticas por fabricante
    fabricantes_stats = df.groupby('Fabricante').agg({
        'Numero Nota': 'nunique',
        'Descricao Produto': 'count',
        'Valor Total Item': lambda x: pd.to_numeric(x.astype(str).str.replace('R$ ', '').str.replace('.', '').str.replace(',', '.'), errors='coerce').sum()
    }).round(2)
    
    fabricantes_stats.columns = ['NFes_Únicas', 'Produtos_Total', 'Valor_Total_R$']
    fabricantes_stats = fabricantes_stats.sort_values('Valor_Total_R$', ascending=False)
    
    print(f"🏭 TOP 10 FABRICANTES POR VALOR:")
    for i, (fabricante, row) in enumerate(fabricantes_stats.head(10).iterrows(), 1):
        if fabricante and fabricante != '':
            valor_formatado = f"R$ {row['Valor_Total_R$']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
            print(f"   {i:2d}. {fabricante}: {int(row['NFes_Únicas']):,} NFes | {int(row['Produtos_Total']):,} produtos | {valor_formatado}")
    
    # Produtos sem fabricante
    sem_fabricante = df[(df['Fabricante'].isna()) | (df['Fabricante'] == '')].shape[0]
    total_produtos = len(df)
    percentual_sem = (sem_fabricante / total_produtos) * 100
    
    print(f"\n❌ PRODUTOS SEM FABRICANTE: {sem_fabricante:,} ({percentual_sem:.1f}%)")
    print(f"✅ PRODUTOS COM FABRICANTE: {total_produtos - sem_fabricante:,} ({100 - percentual_sem:.1f}%)")

if __name__ == "__main__":
    print("🏭 CONSOLIDADOR DE FABRICANTES")
    print("=" * 50)
    print(f"⏰ Iniciado em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    # Consolidar fabricantes
    df_consolidado = consolidar_planilha_produtos()
    
    if df_consolidado is not None:
        # Gerar relatório
        gerar_relatorio_fabricantes()
        
        print("\n🎯 CONSOLIDAÇÃO CONCLUÍDA!")
        print("✅ Fabricantes consolidados e padronizados")
        print("✅ Sistema preparado para futuras leituras")
        print("✅ Notificações de fabricantes desconhecidos ativas")
    
    print(f"\n⏰ Finalizado em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
