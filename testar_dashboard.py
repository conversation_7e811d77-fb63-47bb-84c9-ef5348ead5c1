#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def testar_dados():
    """Testa se os dados estão corretos para o dashboard"""
    
    print("🔍 TESTANDO DADOS PARA O DASHBOARD")
    print("=" * 50)
    
    # Verificar arquivo de produtos
    if os.path.exists('controle_produtos.xlsx'):
        print("✅ Arquivo controle_produtos.xlsx encontrado")
        
        try:
            df_produtos = pd.read_excel('controle_produtos.xlsx')
            print(f"📊 Total de registros: {len(df_produtos):,}")
            print(f"📋 Total de colunas: {len(df_produtos.columns)}")
            
            # Verificar colunas importantes
            colunas_importantes = [
                'Unidade', 'Data Emissao', 'Fornecedor', 'Descricao Produto',
                'Quantidade', 'Valor Unitario', 'Valor Total Item',
                'Fabricante', 'Lote', 'Data Validade'
            ]
            
            print("\n🔍 VERIFICAÇÃO DE COLUNAS:")
            for col in colunas_importantes:
                if col in df_produtos.columns:
                    if col in ['Valor Unitario', 'Valor Total Item']:
                        # Verificar se são numéricos
                        try:
                            valores_numericos = pd.to_numeric(df_produtos[col], errors='coerce')
                            nao_nulos = valores_numericos.notna().sum()
                            print(f"   ✅ {col}: {nao_nulos:,} valores numéricos")
                        except:
                            print(f"   ⚠️ {col}: Problemas de conversão numérica")
                    else:
                        nao_nulos = df_produtos[col].notna().sum()
                        print(f"   ✅ {col}: {nao_nulos:,} valores preenchidos")
                else:
                    print(f"   ❌ {col}: NÃO ENCONTRADA")
            
            # Testar conversão de valores
            print("\n💰 TESTE DE VALORES:")
            if 'Valor Total Item' in df_produtos.columns:
                try:
                    valores = pd.to_numeric(df_produtos['Valor Total Item'], errors='coerce')
                    soma_total = valores.sum()
                    print(f"   ✅ Soma total: R$ {soma_total:,.2f}")
                except Exception as e:
                    print(f"   ❌ Erro na conversão: {e}")
            
            # Verificar datas
            print("\n📅 TESTE DE DATAS:")
            if 'Data Emissao' in df_produtos.columns:
                try:
                    datas = pd.to_datetime(df_produtos['Data Emissao'], errors='coerce')
                    datas_validas = datas.notna().sum()
                    print(f"   ✅ Datas válidas: {datas_validas:,}")
                    if datas_validas > 0:
                        print(f"   📅 Período: {datas.min()} a {datas.max()}")
                except Exception as e:
                    print(f"   ❌ Erro na conversão de datas: {e}")
            
        except Exception as e:
            print(f"❌ Erro ao ler arquivo de produtos: {e}")
    else:
        print("❌ Arquivo controle_produtos.xlsx NÃO encontrado")
    
    print("\n" + "=" * 50)
    
    # Verificar arquivo de faturamento
    if os.path.exists('controle_faturamento_geral.xlsx'):
        print("✅ Arquivo controle_faturamento_geral.xlsx encontrado")
        
        try:
            df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
            print(f"📊 Total de registros: {len(df_faturamento):,}")
            print(f"📋 Total de colunas: {len(df_faturamento.columns)}")
        except Exception as e:
            print(f"❌ Erro ao ler arquivo de faturamento: {e}")
    else:
        print("⚠️ Arquivo controle_faturamento_geral.xlsx NÃO encontrado")
    
    print("\n🎯 CONCLUSÃO:")
    print("   O dashboard deve funcionar com os dados disponíveis!")
    print("   Acesse: http://localhost:8501")

if __name__ == "__main__":
    testar_dados()
