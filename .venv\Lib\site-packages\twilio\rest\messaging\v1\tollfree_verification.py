r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Messaging
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page


class TollfreeVerificationInstance(InstanceResource):

    class OptInType(object):
        VERBAL = "VERBAL"
        WEB_FORM = "WEB_FORM"
        PAPER_FORM = "PAPER_FORM"
        VIA_TEXT = "VIA_TEXT"
        MOBILE_QR_CODE = "MOBILE_QR_CODE"
        IMPORT = "IMPORT"
        IMPORT_PLEASE_REPLACE = "IMPORT_PLEASE_REPLACE"

    class Status(object):
        PENDING_REVIEW = "PENDING_REVIEW"
        IN_REVIEW = "IN_REVIEW"
        TWILIO_APPROVED = "TWILIO_APPROVED"
        TWILIO_REJECTED = "TWILIO_REJECTED"

    """
    :ivar sid: The unique string to identify Tollfree Verification.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Tollfree Verification resource.
    :ivar customer_profile_sid: Customer's Profile Bundle BundleSid.
    :ivar trust_product_sid: Tollfree TrustProduct Bundle BundleSid.
    :ivar date_created: The date and time in GMT when the resource was created specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) format.
    :ivar regulated_item_sid: The SID of the Regulated Item.
    :ivar business_name: The name of the business or organization using the Tollfree number.
    :ivar business_street_address: The address of the business or organization using the Tollfree number.
    :ivar business_street_address2: The address of the business or organization using the Tollfree number.
    :ivar business_city: The city of the business or organization using the Tollfree number.
    :ivar business_state_province_region: The state/province/region of the business or organization using the Tollfree number.
    :ivar business_postal_code: The postal code of the business or organization using the Tollfree number.
    :ivar business_country: The country of the business or organization using the Tollfree number.
    :ivar business_website: The website of the business or organization using the Tollfree number.
    :ivar business_contact_first_name: The first name of the contact for the business or organization using the Tollfree number.
    :ivar business_contact_last_name: The last name of the contact for the business or organization using the Tollfree number.
    :ivar business_contact_email: The email address of the contact for the business or organization using the Tollfree number.
    :ivar business_contact_phone: The E.164 formatted phone number of the contact for the business or organization using the Tollfree number.
    :ivar notification_email: The email address to receive the notification about the verification result. .
    :ivar use_case_categories: The category of the use case for the Tollfree Number. List as many are applicable..
    :ivar use_case_summary: Use this to further explain how messaging is used by the business or organization.
    :ivar production_message_sample: An example of message content, i.e. a sample message.
    :ivar opt_in_image_urls: Link to an image that shows the opt-in workflow. Multiple images allowed and must be a publicly hosted URL.
    :ivar opt_in_type: 
    :ivar message_volume: Estimate monthly volume of messages from the Tollfree Number.
    :ivar additional_information: Additional information to be provided for verification.
    :ivar tollfree_phone_number_sid: The SID of the Phone Number associated with the Tollfree Verification.
    :ivar status: 
    :ivar url: The absolute URL of the Tollfree Verification resource.
    :ivar rejection_reason: The rejection reason given when a Tollfree Verification has been rejected.
    :ivar error_code: The error code given when a Tollfree Verification has been rejected.
    :ivar edit_expiration: The date and time when the ability to edit a rejected verification expires.
    :ivar edit_allowed: If a rejected verification is allowed to be edited/resubmitted. Some rejection reasons allow editing and some do not.
    :ivar rejection_reasons: A list of rejection reasons and codes describing why a Tollfree Verification has been rejected.
    :ivar resource_links: The URLs of the documents associated with the Tollfree Verification resource.
    :ivar external_reference_id: An optional external reference ID supplied by customer and echoed back on status retrieval.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.customer_profile_sid: Optional[str] = payload.get("customer_profile_sid")
        self.trust_product_sid: Optional[str] = payload.get("trust_product_sid")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.regulated_item_sid: Optional[str] = payload.get("regulated_item_sid")
        self.business_name: Optional[str] = payload.get("business_name")
        self.business_street_address: Optional[str] = payload.get(
            "business_street_address"
        )
        self.business_street_address2: Optional[str] = payload.get(
            "business_street_address2"
        )
        self.business_city: Optional[str] = payload.get("business_city")
        self.business_state_province_region: Optional[str] = payload.get(
            "business_state_province_region"
        )
        self.business_postal_code: Optional[str] = payload.get("business_postal_code")
        self.business_country: Optional[str] = payload.get("business_country")
        self.business_website: Optional[str] = payload.get("business_website")
        self.business_contact_first_name: Optional[str] = payload.get(
            "business_contact_first_name"
        )
        self.business_contact_last_name: Optional[str] = payload.get(
            "business_contact_last_name"
        )
        self.business_contact_email: Optional[str] = payload.get(
            "business_contact_email"
        )
        self.business_contact_phone: Optional[str] = payload.get(
            "business_contact_phone"
        )
        self.notification_email: Optional[str] = payload.get("notification_email")
        self.use_case_categories: Optional[List[str]] = payload.get(
            "use_case_categories"
        )
        self.use_case_summary: Optional[str] = payload.get("use_case_summary")
        self.production_message_sample: Optional[str] = payload.get(
            "production_message_sample"
        )
        self.opt_in_image_urls: Optional[List[str]] = payload.get("opt_in_image_urls")
        self.opt_in_type: Optional["TollfreeVerificationInstance.OptInType"] = (
            payload.get("opt_in_type")
        )
        self.message_volume: Optional[str] = payload.get("message_volume")
        self.additional_information: Optional[str] = payload.get(
            "additional_information"
        )
        self.tollfree_phone_number_sid: Optional[str] = payload.get(
            "tollfree_phone_number_sid"
        )
        self.status: Optional["TollfreeVerificationInstance.Status"] = payload.get(
            "status"
        )
        self.url: Optional[str] = payload.get("url")
        self.rejection_reason: Optional[str] = payload.get("rejection_reason")
        self.error_code: Optional[int] = deserialize.integer(payload.get("error_code"))
        self.edit_expiration: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("edit_expiration")
        )
        self.edit_allowed: Optional[bool] = payload.get("edit_allowed")
        self.rejection_reasons: Optional[List[Dict[str, object]]] = payload.get(
            "rejection_reasons"
        )
        self.resource_links: Optional[Dict[str, object]] = payload.get("resource_links")
        self.external_reference_id: Optional[str] = payload.get("external_reference_id")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[TollfreeVerificationContext] = None

    @property
    def _proxy(self) -> "TollfreeVerificationContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: TollfreeVerificationContext for this TollfreeVerificationInstance
        """
        if self._context is None:
            self._context = TollfreeVerificationContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the TollfreeVerificationInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the TollfreeVerificationInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "TollfreeVerificationInstance":
        """
        Fetch the TollfreeVerificationInstance


        :returns: The fetched TollfreeVerificationInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "TollfreeVerificationInstance":
        """
        Asynchronous coroutine to fetch the TollfreeVerificationInstance


        :returns: The fetched TollfreeVerificationInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        business_name: Union[str, object] = values.unset,
        business_website: Union[str, object] = values.unset,
        notification_email: Union[str, object] = values.unset,
        use_case_categories: Union[List[str], object] = values.unset,
        use_case_summary: Union[str, object] = values.unset,
        production_message_sample: Union[str, object] = values.unset,
        opt_in_image_urls: Union[List[str], object] = values.unset,
        opt_in_type: Union[
            "TollfreeVerificationInstance.OptInType", object
        ] = values.unset,
        message_volume: Union[str, object] = values.unset,
        business_street_address: Union[str, object] = values.unset,
        business_street_address2: Union[str, object] = values.unset,
        business_city: Union[str, object] = values.unset,
        business_state_province_region: Union[str, object] = values.unset,
        business_postal_code: Union[str, object] = values.unset,
        business_country: Union[str, object] = values.unset,
        additional_information: Union[str, object] = values.unset,
        business_contact_first_name: Union[str, object] = values.unset,
        business_contact_last_name: Union[str, object] = values.unset,
        business_contact_email: Union[str, object] = values.unset,
        business_contact_phone: Union[str, object] = values.unset,
        edit_reason: Union[str, object] = values.unset,
    ) -> "TollfreeVerificationInstance":
        """
        Update the TollfreeVerificationInstance

        :param business_name: The name of the business or organization using the Tollfree number.
        :param business_website: The website of the business or organization using the Tollfree number.
        :param notification_email: The email address to receive the notification about the verification result. .
        :param use_case_categories: The category of the use case for the Tollfree Number. List as many are applicable..
        :param use_case_summary: Use this to further explain how messaging is used by the business or organization.
        :param production_message_sample: An example of message content, i.e. a sample message.
        :param opt_in_image_urls: Link to an image that shows the opt-in workflow. Multiple images allowed and must be a publicly hosted URL.
        :param opt_in_type:
        :param message_volume: Estimate monthly volume of messages from the Tollfree Number.
        :param business_street_address: The address of the business or organization using the Tollfree number.
        :param business_street_address2: The address of the business or organization using the Tollfree number.
        :param business_city: The city of the business or organization using the Tollfree number.
        :param business_state_province_region: The state/province/region of the business or organization using the Tollfree number.
        :param business_postal_code: The postal code of the business or organization using the Tollfree number.
        :param business_country: The country of the business or organization using the Tollfree number.
        :param additional_information: Additional information to be provided for verification.
        :param business_contact_first_name: The first name of the contact for the business or organization using the Tollfree number.
        :param business_contact_last_name: The last name of the contact for the business or organization using the Tollfree number.
        :param business_contact_email: The email address of the contact for the business or organization using the Tollfree number.
        :param business_contact_phone: The E.164 formatted phone number of the contact for the business or organization using the Tollfree number.
        :param edit_reason: Describe why the verification is being edited. If the verification was rejected because of a technical issue, such as the website being down, and the issue has been resolved this parameter should be set to something similar to 'Website fixed'.

        :returns: The updated TollfreeVerificationInstance
        """
        return self._proxy.update(
            business_name=business_name,
            business_website=business_website,
            notification_email=notification_email,
            use_case_categories=use_case_categories,
            use_case_summary=use_case_summary,
            production_message_sample=production_message_sample,
            opt_in_image_urls=opt_in_image_urls,
            opt_in_type=opt_in_type,
            message_volume=message_volume,
            business_street_address=business_street_address,
            business_street_address2=business_street_address2,
            business_city=business_city,
            business_state_province_region=business_state_province_region,
            business_postal_code=business_postal_code,
            business_country=business_country,
            additional_information=additional_information,
            business_contact_first_name=business_contact_first_name,
            business_contact_last_name=business_contact_last_name,
            business_contact_email=business_contact_email,
            business_contact_phone=business_contact_phone,
            edit_reason=edit_reason,
        )

    async def update_async(
        self,
        business_name: Union[str, object] = values.unset,
        business_website: Union[str, object] = values.unset,
        notification_email: Union[str, object] = values.unset,
        use_case_categories: Union[List[str], object] = values.unset,
        use_case_summary: Union[str, object] = values.unset,
        production_message_sample: Union[str, object] = values.unset,
        opt_in_image_urls: Union[List[str], object] = values.unset,
        opt_in_type: Union[
            "TollfreeVerificationInstance.OptInType", object
        ] = values.unset,
        message_volume: Union[str, object] = values.unset,
        business_street_address: Union[str, object] = values.unset,
        business_street_address2: Union[str, object] = values.unset,
        business_city: Union[str, object] = values.unset,
        business_state_province_region: Union[str, object] = values.unset,
        business_postal_code: Union[str, object] = values.unset,
        business_country: Union[str, object] = values.unset,
        additional_information: Union[str, object] = values.unset,
        business_contact_first_name: Union[str, object] = values.unset,
        business_contact_last_name: Union[str, object] = values.unset,
        business_contact_email: Union[str, object] = values.unset,
        business_contact_phone: Union[str, object] = values.unset,
        edit_reason: Union[str, object] = values.unset,
    ) -> "TollfreeVerificationInstance":
        """
        Asynchronous coroutine to update the TollfreeVerificationInstance

        :param business_name: The name of the business or organization using the Tollfree number.
        :param business_website: The website of the business or organization using the Tollfree number.
        :param notification_email: The email address to receive the notification about the verification result. .
        :param use_case_categories: The category of the use case for the Tollfree Number. List as many are applicable..
        :param use_case_summary: Use this to further explain how messaging is used by the business or organization.
        :param production_message_sample: An example of message content, i.e. a sample message.
        :param opt_in_image_urls: Link to an image that shows the opt-in workflow. Multiple images allowed and must be a publicly hosted URL.
        :param opt_in_type:
        :param message_volume: Estimate monthly volume of messages from the Tollfree Number.
        :param business_street_address: The address of the business or organization using the Tollfree number.
        :param business_street_address2: The address of the business or organization using the Tollfree number.
        :param business_city: The city of the business or organization using the Tollfree number.
        :param business_state_province_region: The state/province/region of the business or organization using the Tollfree number.
        :param business_postal_code: The postal code of the business or organization using the Tollfree number.
        :param business_country: The country of the business or organization using the Tollfree number.
        :param additional_information: Additional information to be provided for verification.
        :param business_contact_first_name: The first name of the contact for the business or organization using the Tollfree number.
        :param business_contact_last_name: The last name of the contact for the business or organization using the Tollfree number.
        :param business_contact_email: The email address of the contact for the business or organization using the Tollfree number.
        :param business_contact_phone: The E.164 formatted phone number of the contact for the business or organization using the Tollfree number.
        :param edit_reason: Describe why the verification is being edited. If the verification was rejected because of a technical issue, such as the website being down, and the issue has been resolved this parameter should be set to something similar to 'Website fixed'.

        :returns: The updated TollfreeVerificationInstance
        """
        return await self._proxy.update_async(
            business_name=business_name,
            business_website=business_website,
            notification_email=notification_email,
            use_case_categories=use_case_categories,
            use_case_summary=use_case_summary,
            production_message_sample=production_message_sample,
            opt_in_image_urls=opt_in_image_urls,
            opt_in_type=opt_in_type,
            message_volume=message_volume,
            business_street_address=business_street_address,
            business_street_address2=business_street_address2,
            business_city=business_city,
            business_state_province_region=business_state_province_region,
            business_postal_code=business_postal_code,
            business_country=business_country,
            additional_information=additional_information,
            business_contact_first_name=business_contact_first_name,
            business_contact_last_name=business_contact_last_name,
            business_contact_email=business_contact_email,
            business_contact_phone=business_contact_phone,
            edit_reason=edit_reason,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Messaging.V1.TollfreeVerificationInstance {}>".format(context)


class TollfreeVerificationContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the TollfreeVerificationContext

        :param version: Version that contains the resource
        :param sid: The unique string to identify Tollfree Verification.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Tollfree/Verifications/{sid}".format(**self._solution)

    def delete(self) -> bool:
        """
        Deletes the TollfreeVerificationInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the TollfreeVerificationInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> TollfreeVerificationInstance:
        """
        Fetch the TollfreeVerificationInstance


        :returns: The fetched TollfreeVerificationInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return TollfreeVerificationInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> TollfreeVerificationInstance:
        """
        Asynchronous coroutine to fetch the TollfreeVerificationInstance


        :returns: The fetched TollfreeVerificationInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return TollfreeVerificationInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self,
        business_name: Union[str, object] = values.unset,
        business_website: Union[str, object] = values.unset,
        notification_email: Union[str, object] = values.unset,
        use_case_categories: Union[List[str], object] = values.unset,
        use_case_summary: Union[str, object] = values.unset,
        production_message_sample: Union[str, object] = values.unset,
        opt_in_image_urls: Union[List[str], object] = values.unset,
        opt_in_type: Union[
            "TollfreeVerificationInstance.OptInType", object
        ] = values.unset,
        message_volume: Union[str, object] = values.unset,
        business_street_address: Union[str, object] = values.unset,
        business_street_address2: Union[str, object] = values.unset,
        business_city: Union[str, object] = values.unset,
        business_state_province_region: Union[str, object] = values.unset,
        business_postal_code: Union[str, object] = values.unset,
        business_country: Union[str, object] = values.unset,
        additional_information: Union[str, object] = values.unset,
        business_contact_first_name: Union[str, object] = values.unset,
        business_contact_last_name: Union[str, object] = values.unset,
        business_contact_email: Union[str, object] = values.unset,
        business_contact_phone: Union[str, object] = values.unset,
        edit_reason: Union[str, object] = values.unset,
    ) -> TollfreeVerificationInstance:
        """
        Update the TollfreeVerificationInstance

        :param business_name: The name of the business or organization using the Tollfree number.
        :param business_website: The website of the business or organization using the Tollfree number.
        :param notification_email: The email address to receive the notification about the verification result. .
        :param use_case_categories: The category of the use case for the Tollfree Number. List as many are applicable..
        :param use_case_summary: Use this to further explain how messaging is used by the business or organization.
        :param production_message_sample: An example of message content, i.e. a sample message.
        :param opt_in_image_urls: Link to an image that shows the opt-in workflow. Multiple images allowed and must be a publicly hosted URL.
        :param opt_in_type:
        :param message_volume: Estimate monthly volume of messages from the Tollfree Number.
        :param business_street_address: The address of the business or organization using the Tollfree number.
        :param business_street_address2: The address of the business or organization using the Tollfree number.
        :param business_city: The city of the business or organization using the Tollfree number.
        :param business_state_province_region: The state/province/region of the business or organization using the Tollfree number.
        :param business_postal_code: The postal code of the business or organization using the Tollfree number.
        :param business_country: The country of the business or organization using the Tollfree number.
        :param additional_information: Additional information to be provided for verification.
        :param business_contact_first_name: The first name of the contact for the business or organization using the Tollfree number.
        :param business_contact_last_name: The last name of the contact for the business or organization using the Tollfree number.
        :param business_contact_email: The email address of the contact for the business or organization using the Tollfree number.
        :param business_contact_phone: The E.164 formatted phone number of the contact for the business or organization using the Tollfree number.
        :param edit_reason: Describe why the verification is being edited. If the verification was rejected because of a technical issue, such as the website being down, and the issue has been resolved this parameter should be set to something similar to 'Website fixed'.

        :returns: The updated TollfreeVerificationInstance
        """

        data = values.of(
            {
                "BusinessName": business_name,
                "BusinessWebsite": business_website,
                "NotificationEmail": notification_email,
                "UseCaseCategories": serialize.map(use_case_categories, lambda e: e),
                "UseCaseSummary": use_case_summary,
                "ProductionMessageSample": production_message_sample,
                "OptInImageUrls": serialize.map(opt_in_image_urls, lambda e: e),
                "OptInType": opt_in_type,
                "MessageVolume": message_volume,
                "BusinessStreetAddress": business_street_address,
                "BusinessStreetAddress2": business_street_address2,
                "BusinessCity": business_city,
                "BusinessStateProvinceRegion": business_state_province_region,
                "BusinessPostalCode": business_postal_code,
                "BusinessCountry": business_country,
                "AdditionalInformation": additional_information,
                "BusinessContactFirstName": business_contact_first_name,
                "BusinessContactLastName": business_contact_last_name,
                "BusinessContactEmail": business_contact_email,
                "BusinessContactPhone": business_contact_phone,
                "EditReason": edit_reason,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TollfreeVerificationInstance(
            self._version, payload, sid=self._solution["sid"]
        )

    async def update_async(
        self,
        business_name: Union[str, object] = values.unset,
        business_website: Union[str, object] = values.unset,
        notification_email: Union[str, object] = values.unset,
        use_case_categories: Union[List[str], object] = values.unset,
        use_case_summary: Union[str, object] = values.unset,
        production_message_sample: Union[str, object] = values.unset,
        opt_in_image_urls: Union[List[str], object] = values.unset,
        opt_in_type: Union[
            "TollfreeVerificationInstance.OptInType", object
        ] = values.unset,
        message_volume: Union[str, object] = values.unset,
        business_street_address: Union[str, object] = values.unset,
        business_street_address2: Union[str, object] = values.unset,
        business_city: Union[str, object] = values.unset,
        business_state_province_region: Union[str, object] = values.unset,
        business_postal_code: Union[str, object] = values.unset,
        business_country: Union[str, object] = values.unset,
        additional_information: Union[str, object] = values.unset,
        business_contact_first_name: Union[str, object] = values.unset,
        business_contact_last_name: Union[str, object] = values.unset,
        business_contact_email: Union[str, object] = values.unset,
        business_contact_phone: Union[str, object] = values.unset,
        edit_reason: Union[str, object] = values.unset,
    ) -> TollfreeVerificationInstance:
        """
        Asynchronous coroutine to update the TollfreeVerificationInstance

        :param business_name: The name of the business or organization using the Tollfree number.
        :param business_website: The website of the business or organization using the Tollfree number.
        :param notification_email: The email address to receive the notification about the verification result. .
        :param use_case_categories: The category of the use case for the Tollfree Number. List as many are applicable..
        :param use_case_summary: Use this to further explain how messaging is used by the business or organization.
        :param production_message_sample: An example of message content, i.e. a sample message.
        :param opt_in_image_urls: Link to an image that shows the opt-in workflow. Multiple images allowed and must be a publicly hosted URL.
        :param opt_in_type:
        :param message_volume: Estimate monthly volume of messages from the Tollfree Number.
        :param business_street_address: The address of the business or organization using the Tollfree number.
        :param business_street_address2: The address of the business or organization using the Tollfree number.
        :param business_city: The city of the business or organization using the Tollfree number.
        :param business_state_province_region: The state/province/region of the business or organization using the Tollfree number.
        :param business_postal_code: The postal code of the business or organization using the Tollfree number.
        :param business_country: The country of the business or organization using the Tollfree number.
        :param additional_information: Additional information to be provided for verification.
        :param business_contact_first_name: The first name of the contact for the business or organization using the Tollfree number.
        :param business_contact_last_name: The last name of the contact for the business or organization using the Tollfree number.
        :param business_contact_email: The email address of the contact for the business or organization using the Tollfree number.
        :param business_contact_phone: The E.164 formatted phone number of the contact for the business or organization using the Tollfree number.
        :param edit_reason: Describe why the verification is being edited. If the verification was rejected because of a technical issue, such as the website being down, and the issue has been resolved this parameter should be set to something similar to 'Website fixed'.

        :returns: The updated TollfreeVerificationInstance
        """

        data = values.of(
            {
                "BusinessName": business_name,
                "BusinessWebsite": business_website,
                "NotificationEmail": notification_email,
                "UseCaseCategories": serialize.map(use_case_categories, lambda e: e),
                "UseCaseSummary": use_case_summary,
                "ProductionMessageSample": production_message_sample,
                "OptInImageUrls": serialize.map(opt_in_image_urls, lambda e: e),
                "OptInType": opt_in_type,
                "MessageVolume": message_volume,
                "BusinessStreetAddress": business_street_address,
                "BusinessStreetAddress2": business_street_address2,
                "BusinessCity": business_city,
                "BusinessStateProvinceRegion": business_state_province_region,
                "BusinessPostalCode": business_postal_code,
                "BusinessCountry": business_country,
                "AdditionalInformation": additional_information,
                "BusinessContactFirstName": business_contact_first_name,
                "BusinessContactLastName": business_contact_last_name,
                "BusinessContactEmail": business_contact_email,
                "BusinessContactPhone": business_contact_phone,
                "EditReason": edit_reason,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TollfreeVerificationInstance(
            self._version, payload, sid=self._solution["sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Messaging.V1.TollfreeVerificationContext {}>".format(context)


class TollfreeVerificationPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> TollfreeVerificationInstance:
        """
        Build an instance of TollfreeVerificationInstance

        :param payload: Payload response from the API
        """
        return TollfreeVerificationInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Messaging.V1.TollfreeVerificationPage>"


class TollfreeVerificationList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the TollfreeVerificationList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Tollfree/Verifications"

    def create(
        self,
        business_name: str,
        business_website: str,
        notification_email: str,
        use_case_categories: List[str],
        use_case_summary: str,
        production_message_sample: str,
        opt_in_image_urls: List[str],
        opt_in_type: "TollfreeVerificationInstance.OptInType",
        message_volume: str,
        tollfree_phone_number_sid: str,
        customer_profile_sid: Union[str, object] = values.unset,
        business_street_address: Union[str, object] = values.unset,
        business_street_address2: Union[str, object] = values.unset,
        business_city: Union[str, object] = values.unset,
        business_state_province_region: Union[str, object] = values.unset,
        business_postal_code: Union[str, object] = values.unset,
        business_country: Union[str, object] = values.unset,
        additional_information: Union[str, object] = values.unset,
        business_contact_first_name: Union[str, object] = values.unset,
        business_contact_last_name: Union[str, object] = values.unset,
        business_contact_email: Union[str, object] = values.unset,
        business_contact_phone: Union[str, object] = values.unset,
        external_reference_id: Union[str, object] = values.unset,
    ) -> TollfreeVerificationInstance:
        """
        Create the TollfreeVerificationInstance

        :param business_name: The name of the business or organization using the Tollfree number.
        :param business_website: The website of the business or organization using the Tollfree number.
        :param notification_email: The email address to receive the notification about the verification result. .
        :param use_case_categories: The category of the use case for the Tollfree Number. List as many are applicable..
        :param use_case_summary: Use this to further explain how messaging is used by the business or organization.
        :param production_message_sample: An example of message content, i.e. a sample message.
        :param opt_in_image_urls: Link to an image that shows the opt-in workflow. Multiple images allowed and must be a publicly hosted URL.
        :param opt_in_type:
        :param message_volume: Estimate monthly volume of messages from the Tollfree Number.
        :param tollfree_phone_number_sid: The SID of the Phone Number associated with the Tollfree Verification.
        :param customer_profile_sid: Customer's Profile Bundle BundleSid.
        :param business_street_address: The address of the business or organization using the Tollfree number.
        :param business_street_address2: The address of the business or organization using the Tollfree number.
        :param business_city: The city of the business or organization using the Tollfree number.
        :param business_state_province_region: The state/province/region of the business or organization using the Tollfree number.
        :param business_postal_code: The postal code of the business or organization using the Tollfree number.
        :param business_country: The country of the business or organization using the Tollfree number.
        :param additional_information: Additional information to be provided for verification.
        :param business_contact_first_name: The first name of the contact for the business or organization using the Tollfree number.
        :param business_contact_last_name: The last name of the contact for the business or organization using the Tollfree number.
        :param business_contact_email: The email address of the contact for the business or organization using the Tollfree number.
        :param business_contact_phone: The E.164 formatted phone number of the contact for the business or organization using the Tollfree number.
        :param external_reference_id: An optional external reference ID supplied by customer and echoed back on status retrieval.

        :returns: The created TollfreeVerificationInstance
        """

        data = values.of(
            {
                "BusinessName": business_name,
                "BusinessWebsite": business_website,
                "NotificationEmail": notification_email,
                "UseCaseCategories": serialize.map(use_case_categories, lambda e: e),
                "UseCaseSummary": use_case_summary,
                "ProductionMessageSample": production_message_sample,
                "OptInImageUrls": serialize.map(opt_in_image_urls, lambda e: e),
                "OptInType": opt_in_type,
                "MessageVolume": message_volume,
                "TollfreePhoneNumberSid": tollfree_phone_number_sid,
                "CustomerProfileSid": customer_profile_sid,
                "BusinessStreetAddress": business_street_address,
                "BusinessStreetAddress2": business_street_address2,
                "BusinessCity": business_city,
                "BusinessStateProvinceRegion": business_state_province_region,
                "BusinessPostalCode": business_postal_code,
                "BusinessCountry": business_country,
                "AdditionalInformation": additional_information,
                "BusinessContactFirstName": business_contact_first_name,
                "BusinessContactLastName": business_contact_last_name,
                "BusinessContactEmail": business_contact_email,
                "BusinessContactPhone": business_contact_phone,
                "ExternalReferenceId": external_reference_id,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TollfreeVerificationInstance(self._version, payload)

    async def create_async(
        self,
        business_name: str,
        business_website: str,
        notification_email: str,
        use_case_categories: List[str],
        use_case_summary: str,
        production_message_sample: str,
        opt_in_image_urls: List[str],
        opt_in_type: "TollfreeVerificationInstance.OptInType",
        message_volume: str,
        tollfree_phone_number_sid: str,
        customer_profile_sid: Union[str, object] = values.unset,
        business_street_address: Union[str, object] = values.unset,
        business_street_address2: Union[str, object] = values.unset,
        business_city: Union[str, object] = values.unset,
        business_state_province_region: Union[str, object] = values.unset,
        business_postal_code: Union[str, object] = values.unset,
        business_country: Union[str, object] = values.unset,
        additional_information: Union[str, object] = values.unset,
        business_contact_first_name: Union[str, object] = values.unset,
        business_contact_last_name: Union[str, object] = values.unset,
        business_contact_email: Union[str, object] = values.unset,
        business_contact_phone: Union[str, object] = values.unset,
        external_reference_id: Union[str, object] = values.unset,
    ) -> TollfreeVerificationInstance:
        """
        Asynchronously create the TollfreeVerificationInstance

        :param business_name: The name of the business or organization using the Tollfree number.
        :param business_website: The website of the business or organization using the Tollfree number.
        :param notification_email: The email address to receive the notification about the verification result. .
        :param use_case_categories: The category of the use case for the Tollfree Number. List as many are applicable..
        :param use_case_summary: Use this to further explain how messaging is used by the business or organization.
        :param production_message_sample: An example of message content, i.e. a sample message.
        :param opt_in_image_urls: Link to an image that shows the opt-in workflow. Multiple images allowed and must be a publicly hosted URL.
        :param opt_in_type:
        :param message_volume: Estimate monthly volume of messages from the Tollfree Number.
        :param tollfree_phone_number_sid: The SID of the Phone Number associated with the Tollfree Verification.
        :param customer_profile_sid: Customer's Profile Bundle BundleSid.
        :param business_street_address: The address of the business or organization using the Tollfree number.
        :param business_street_address2: The address of the business or organization using the Tollfree number.
        :param business_city: The city of the business or organization using the Tollfree number.
        :param business_state_province_region: The state/province/region of the business or organization using the Tollfree number.
        :param business_postal_code: The postal code of the business or organization using the Tollfree number.
        :param business_country: The country of the business or organization using the Tollfree number.
        :param additional_information: Additional information to be provided for verification.
        :param business_contact_first_name: The first name of the contact for the business or organization using the Tollfree number.
        :param business_contact_last_name: The last name of the contact for the business or organization using the Tollfree number.
        :param business_contact_email: The email address of the contact for the business or organization using the Tollfree number.
        :param business_contact_phone: The E.164 formatted phone number of the contact for the business or organization using the Tollfree number.
        :param external_reference_id: An optional external reference ID supplied by customer and echoed back on status retrieval.

        :returns: The created TollfreeVerificationInstance
        """

        data = values.of(
            {
                "BusinessName": business_name,
                "BusinessWebsite": business_website,
                "NotificationEmail": notification_email,
                "UseCaseCategories": serialize.map(use_case_categories, lambda e: e),
                "UseCaseSummary": use_case_summary,
                "ProductionMessageSample": production_message_sample,
                "OptInImageUrls": serialize.map(opt_in_image_urls, lambda e: e),
                "OptInType": opt_in_type,
                "MessageVolume": message_volume,
                "TollfreePhoneNumberSid": tollfree_phone_number_sid,
                "CustomerProfileSid": customer_profile_sid,
                "BusinessStreetAddress": business_street_address,
                "BusinessStreetAddress2": business_street_address2,
                "BusinessCity": business_city,
                "BusinessStateProvinceRegion": business_state_province_region,
                "BusinessPostalCode": business_postal_code,
                "BusinessCountry": business_country,
                "AdditionalInformation": additional_information,
                "BusinessContactFirstName": business_contact_first_name,
                "BusinessContactLastName": business_contact_last_name,
                "BusinessContactEmail": business_contact_email,
                "BusinessContactPhone": business_contact_phone,
                "ExternalReferenceId": external_reference_id,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return TollfreeVerificationInstance(self._version, payload)

    def stream(
        self,
        tollfree_phone_number_sid: Union[str, object] = values.unset,
        status: Union["TollfreeVerificationInstance.Status", object] = values.unset,
        external_reference_id: Union[str, object] = values.unset,
        include_sub_accounts: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[TollfreeVerificationInstance]:
        """
        Streams TollfreeVerificationInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str tollfree_phone_number_sid: The SID of the Phone Number associated with the Tollfree Verification.
        :param &quot;TollfreeVerificationInstance.Status&quot; status: The compliance status of the Tollfree Verification record.
        :param str external_reference_id: Customer supplied reference id for the Tollfree Verification record.
        :param bool include_sub_accounts: Whether to include Tollfree Verifications from sub accounts in list response.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(
            tollfree_phone_number_sid=tollfree_phone_number_sid,
            status=status,
            external_reference_id=external_reference_id,
            include_sub_accounts=include_sub_accounts,
            page_size=limits["page_size"],
        )

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        tollfree_phone_number_sid: Union[str, object] = values.unset,
        status: Union["TollfreeVerificationInstance.Status", object] = values.unset,
        external_reference_id: Union[str, object] = values.unset,
        include_sub_accounts: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[TollfreeVerificationInstance]:
        """
        Asynchronously streams TollfreeVerificationInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str tollfree_phone_number_sid: The SID of the Phone Number associated with the Tollfree Verification.
        :param &quot;TollfreeVerificationInstance.Status&quot; status: The compliance status of the Tollfree Verification record.
        :param str external_reference_id: Customer supplied reference id for the Tollfree Verification record.
        :param bool include_sub_accounts: Whether to include Tollfree Verifications from sub accounts in list response.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            tollfree_phone_number_sid=tollfree_phone_number_sid,
            status=status,
            external_reference_id=external_reference_id,
            include_sub_accounts=include_sub_accounts,
            page_size=limits["page_size"],
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        tollfree_phone_number_sid: Union[str, object] = values.unset,
        status: Union["TollfreeVerificationInstance.Status", object] = values.unset,
        external_reference_id: Union[str, object] = values.unset,
        include_sub_accounts: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[TollfreeVerificationInstance]:
        """
        Lists TollfreeVerificationInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str tollfree_phone_number_sid: The SID of the Phone Number associated with the Tollfree Verification.
        :param &quot;TollfreeVerificationInstance.Status&quot; status: The compliance status of the Tollfree Verification record.
        :param str external_reference_id: Customer supplied reference id for the Tollfree Verification record.
        :param bool include_sub_accounts: Whether to include Tollfree Verifications from sub accounts in list response.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                tollfree_phone_number_sid=tollfree_phone_number_sid,
                status=status,
                external_reference_id=external_reference_id,
                include_sub_accounts=include_sub_accounts,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        tollfree_phone_number_sid: Union[str, object] = values.unset,
        status: Union["TollfreeVerificationInstance.Status", object] = values.unset,
        external_reference_id: Union[str, object] = values.unset,
        include_sub_accounts: Union[bool, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[TollfreeVerificationInstance]:
        """
        Asynchronously lists TollfreeVerificationInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str tollfree_phone_number_sid: The SID of the Phone Number associated with the Tollfree Verification.
        :param &quot;TollfreeVerificationInstance.Status&quot; status: The compliance status of the Tollfree Verification record.
        :param str external_reference_id: Customer supplied reference id for the Tollfree Verification record.
        :param bool include_sub_accounts: Whether to include Tollfree Verifications from sub accounts in list response.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                tollfree_phone_number_sid=tollfree_phone_number_sid,
                status=status,
                external_reference_id=external_reference_id,
                include_sub_accounts=include_sub_accounts,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        tollfree_phone_number_sid: Union[str, object] = values.unset,
        status: Union["TollfreeVerificationInstance.Status", object] = values.unset,
        external_reference_id: Union[str, object] = values.unset,
        include_sub_accounts: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> TollfreeVerificationPage:
        """
        Retrieve a single page of TollfreeVerificationInstance records from the API.
        Request is executed immediately

        :param tollfree_phone_number_sid: The SID of the Phone Number associated with the Tollfree Verification.
        :param status: The compliance status of the Tollfree Verification record.
        :param external_reference_id: Customer supplied reference id for the Tollfree Verification record.
        :param include_sub_accounts: Whether to include Tollfree Verifications from sub accounts in list response.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of TollfreeVerificationInstance
        """
        data = values.of(
            {
                "TollfreePhoneNumberSid": tollfree_phone_number_sid,
                "Status": status,
                "ExternalReferenceId": external_reference_id,
                "IncludeSubAccounts": serialize.boolean_to_string(include_sub_accounts),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return TollfreeVerificationPage(self._version, response)

    async def page_async(
        self,
        tollfree_phone_number_sid: Union[str, object] = values.unset,
        status: Union["TollfreeVerificationInstance.Status", object] = values.unset,
        external_reference_id: Union[str, object] = values.unset,
        include_sub_accounts: Union[bool, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> TollfreeVerificationPage:
        """
        Asynchronously retrieve a single page of TollfreeVerificationInstance records from the API.
        Request is executed immediately

        :param tollfree_phone_number_sid: The SID of the Phone Number associated with the Tollfree Verification.
        :param status: The compliance status of the Tollfree Verification record.
        :param external_reference_id: Customer supplied reference id for the Tollfree Verification record.
        :param include_sub_accounts: Whether to include Tollfree Verifications from sub accounts in list response.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of TollfreeVerificationInstance
        """
        data = values.of(
            {
                "TollfreePhoneNumberSid": tollfree_phone_number_sid,
                "Status": status,
                "ExternalReferenceId": external_reference_id,
                "IncludeSubAccounts": serialize.boolean_to_string(include_sub_accounts),
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return TollfreeVerificationPage(self._version, response)

    def get_page(self, target_url: str) -> TollfreeVerificationPage:
        """
        Retrieve a specific page of TollfreeVerificationInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of TollfreeVerificationInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return TollfreeVerificationPage(self._version, response)

    async def get_page_async(self, target_url: str) -> TollfreeVerificationPage:
        """
        Asynchronously retrieve a specific page of TollfreeVerificationInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of TollfreeVerificationInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return TollfreeVerificationPage(self._version, response)

    def get(self, sid: str) -> TollfreeVerificationContext:
        """
        Constructs a TollfreeVerificationContext

        :param sid: The unique string to identify Tollfree Verification.
        """
        return TollfreeVerificationContext(self._version, sid=sid)

    def __call__(self, sid: str) -> TollfreeVerificationContext:
        """
        Constructs a TollfreeVerificationContext

        :param sid: The unique string to identify Tollfree Verification.
        """
        return TollfreeVerificationContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Messaging.V1.TollfreeVerificationList>"
