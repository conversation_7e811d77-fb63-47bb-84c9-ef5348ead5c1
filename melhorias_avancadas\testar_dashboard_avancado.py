#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TESTE DO DASHBOARD FINANCEIRO AVANÇADO
Script para testar todas as funcionalidades do dashboard avançado
"""

import os
import sys
import subprocess
import time
import requests
from datetime import datetime

def verificar_dependencias():
    """Verifica se todas as dependências estão instaladas"""
    print("🔍 Verificando dependências...")
    
    dependencias = ['flask', 'pandas', 'numpy']
    faltando = []
    
    for dep in dependencias:
        try:
            __import__(dep)
            print(f"✅ {dep}")
        except ImportError:
            faltando.append(dep)
            print(f"❌ {dep}")
    
    if faltando:
        print(f"\n⚠️  Dependências faltando: {', '.join(faltando)}")
        print("Instalando dependências...")
        
        for dep in faltando:
            subprocess.run([sys.executable, '-m', 'pip', 'install', dep], check=True)
        
        print("✅ Todas as dependências foram instaladas!")
    
    return True

def verificar_arquivos():
    """Verifica se os arquivos necessários existem"""
    print("\n📁 Verificando arquivos...")
    
    arquivos_necessarios = [
        'dashboard_web.py',
        'templates/dashboard_avancado.html'
    ]
    
    for arquivo in arquivos_necessarios:
        if os.path.exists(arquivo):
            print(f"✅ {arquivo}")
        else:
            print(f"❌ {arquivo} - ARQUIVO FALTANDO!")
            return False
    
    # Verificar se existe pasta templates
    if not os.path.exists('templates'):
        print("📁 Criando pasta templates...")
        os.makedirs('templates')
    
    return True

def verificar_dados():
    """Verifica se existem dados para teste"""
    print("\n📊 Verificando dados de teste...")
    
    # Procurar por arquivos de dados em diferentes locais
    locais_dados = ['../dados/', 'dados/', './']
    arquivos_dados = [
        'controle_faturamento_geral.xlsx',
        'controle_produtos.xlsx',
        'controle_boletos.xlsx'
    ]
    
    dados_encontrados = False
    
    for local in locais_dados:
        print(f"🔍 Verificando em: {local}")
        for arquivo in arquivos_dados:
            caminho = os.path.join(local, arquivo)
            if os.path.exists(caminho):
                print(f"  ✅ {arquivo}")
                dados_encontrados = True
            else:
                print(f"  ❌ {arquivo}")
    
    if not dados_encontrados:
        print("\n⚠️  Nenhum arquivo de dados encontrado!")
        print("O dashboard funcionará, mas sem dados reais.")
        print("Execute o processamento de emails primeiro para gerar dados.")
    
    return True

def iniciar_servidor():
    """Inicia o servidor do dashboard"""
    print("\n🚀 Iniciando servidor do dashboard...")
    
    try:
        # Importar e executar o dashboard
        import dashboard_web
        
        print("✅ Dashboard importado com sucesso!")
        print("🌐 Servidor será iniciado em: http://localhost:5000")
        print("📱 Interface responsiva (desktop + mobile)")
        print("🔄 Atualização automática dos dados")
        print("\n" + "="*60)
        print("DASHBOARD FINANCEIRO AVANÇADO - RECURSOS:")
        print("="*60)
        print("📊 Análise detalhada por unidade")
        print("🔍 Filtros avançados para drill-down")
        print("📈 Métricas financeiras profissionais")
        print("🎯 Relatórios específicos para proprietários")
        print("💰 Projeção de fluxo de caixa")
        print("⚠️  Alertas de vencimento em tempo real")
        print("📋 Tabelas interativas com DataTables")
        print("🎨 Interface moderna e responsiva")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro ao iniciar servidor: {e}")
        return False

def testar_apis():
    """Testa as APIs do dashboard"""
    print("\n🧪 Testando APIs do dashboard...")
    
    # Aguardar um pouco para o servidor iniciar
    time.sleep(3)
    
    apis_teste = [
        '/api/unidades',
        '/api/resumo',
        '/api/fluxo-mensal',
        '/api/top-unidades',
        '/api/top-fornecedores',
        '/api/analise-vencimentos',
        '/api/fluxo-caixa-projetado'
    ]
    
    base_url = 'http://localhost:5000'
    
    for api in apis_teste:
        try:
            response = requests.get(f"{base_url}{api}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {api} - OK")
            else:
                print(f"⚠️  {api} - Status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {api} - Erro: {e}")
    
    return True

def mostrar_instrucoes():
    """Mostra instruções de uso"""
    print("\n" + "="*60)
    print("INSTRUÇÕES DE USO DO DASHBOARD AVANÇADO")
    print("="*60)
    print("\n🎯 COMO USAR COMO ANALISTA FINANCEIRO:")
    print("\n1. FILTROS POR UNIDADE:")
    print("   • Selecione uma unidade específica no dropdown")
    print("   • Veja métricas detalhadas apenas dessa unidade")
    print("   • Compare performance entre unidades")
    
    print("\n2. ANÁLISES DISPONÍVEIS:")
    print("   • Resumo executivo com KPIs principais")
    print("   • Evolução mensal do faturamento")
    print("   • Top fornecedores por unidade")
    print("   • Análise de concentração (Pareto)")
    print("   • Alertas de vencimento críticos")
    print("   • Projeção de fluxo de caixa")
    
    print("\n3. RELATÓRIOS PARA PROPRIETÁRIOS:")
    print("   • Selecione a unidade do proprietário")
    print("   • Veja tabela detalhada de fornecedores")
    print("   • Analise concentração de gastos")
    print("   • Monitore vencimentos importantes")
    
    print("\n4. RECURSOS AVANÇADOS:")
    print("   • Gráficos interativos com Chart.js")
    print("   • Tabelas ordenáveis e pesquisáveis")
    print("   • Atualização automática dos dados")
    print("   • Interface responsiva para mobile")
    
    print("\n5. INSIGHTS AUTOMÁTICOS:")
    print("   • Variação percentual mensal")
    print("   • Alertas de concentração de fornecedores")
    print("   • Recomendações baseadas nos dados")
    
    print("\n" + "="*60)
    print("🌐 Acesse: http://localhost:5000")
    print("📱 Funciona em desktop, tablet e mobile")
    print("🔄 Dados atualizados automaticamente")
    print("="*60)

def main():
    """Função principal"""
    print("🚀 TESTE DO DASHBOARD FINANCEIRO AVANÇADO")
    print("=" * 50)
    print(f"⏰ Iniciado em: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print("=" * 50)
    
    try:
        # Verificações
        if not verificar_dependencias():
            return False
        
        if not verificar_arquivos():
            return False
        
        verificar_dados()
        
        # Mostrar instruções
        mostrar_instrucoes()
        
        # Iniciar servidor
        if iniciar_servidor():
            print("\n✅ DASHBOARD INICIADO COM SUCESSO!")
            print("\n🎉 RECURSOS IMPLEMENTADOS:")
            print("   ✅ Filtros avançados por unidade")
            print("   ✅ Análises financeiras detalhadas")
            print("   ✅ Métricas profissionais para analistas")
            print("   ✅ Relatórios específicos por proprietário")
            print("   ✅ Interface moderna e responsiva")
            print("   ✅ Gráficos interativos avançados")
            print("   ✅ Tabelas com busca e ordenação")
            print("   ✅ Alertas de vencimento em tempo real")
            print("   ✅ Projeção de fluxo de caixa")
            print("   ✅ Insights automáticos")
            
            print(f"\n🌐 Dashboard disponível em: http://localhost:5000")
            print("📊 Pronto para análises financeiras avançadas!")
            
            # Testar APIs em background
            import threading
            threading.Thread(target=testar_apis, daemon=True).start()
            
            return True
        else:
            return False
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Dashboard interrompido pelo usuário")
        return True
    except Exception as e:
        print(f"\n❌ Erro durante execução: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ Teste concluído com sucesso!")
    else:
        print("\n❌ Teste falhou!")
        sys.exit(1)
