r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Taskrouter
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.taskrouter.v1.workspace.workflow.workflow_cumulative_statistics import (
    WorkflowCumulativeStatisticsList,
)
from twilio.rest.taskrouter.v1.workspace.workflow.workflow_real_time_statistics import (
    WorkflowRealTimeStatisticsList,
)
from twilio.rest.taskrouter.v1.workspace.workflow.workflow_statistics import (
    WorkflowStatisticsList,
)


class WorkflowInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Workflow resource.
    :ivar assignment_callback_url: The URL that we call when a task managed by the Workflow is assigned to a Worker. See Assignment Callback URL for more information.
    :ivar configuration: A JSON string that contains the Workflow's configuration. See [Configuring Workflows](https://www.twilio.com/docs/taskrouter/workflow-configuration) for more information.
    :ivar date_created: The date and time in GMT when the resource was created specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar document_content_type: The MIME type of the document.
    :ivar fallback_assignment_callback_url: The URL that we call when a call to the `assignment_callback_url` fails.
    :ivar friendly_name: The string that you assigned to describe the Workflow resource. For example, `Customer Support` or `2014 Election Campaign`.
    :ivar sid: The unique string that we created to identify the Workflow resource.
    :ivar task_reservation_timeout: How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
    :ivar workspace_sid: The SID of the Workspace that contains the Workflow.
    :ivar url: The absolute URL of the Workflow resource.
    :ivar links: The URLs of related resources.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        workspace_sid: str,
        sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.assignment_callback_url: Optional[str] = payload.get(
            "assignment_callback_url"
        )
        self.configuration: Optional[str] = payload.get("configuration")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.document_content_type: Optional[str] = payload.get("document_content_type")
        self.fallback_assignment_callback_url: Optional[str] = payload.get(
            "fallback_assignment_callback_url"
        )
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.sid: Optional[str] = payload.get("sid")
        self.task_reservation_timeout: Optional[int] = deserialize.integer(
            payload.get("task_reservation_timeout")
        )
        self.workspace_sid: Optional[str] = payload.get("workspace_sid")
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "workspace_sid": workspace_sid,
            "sid": sid or self.sid,
        }
        self._context: Optional[WorkflowContext] = None

    @property
    def _proxy(self) -> "WorkflowContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: WorkflowContext for this WorkflowInstance
        """
        if self._context is None:
            self._context = WorkflowContext(
                self._version,
                workspace_sid=self._solution["workspace_sid"],
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the WorkflowInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the WorkflowInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "WorkflowInstance":
        """
        Fetch the WorkflowInstance


        :returns: The fetched WorkflowInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "WorkflowInstance":
        """
        Asynchronous coroutine to fetch the WorkflowInstance


        :returns: The fetched WorkflowInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        assignment_callback_url: Union[str, object] = values.unset,
        fallback_assignment_callback_url: Union[str, object] = values.unset,
        configuration: Union[str, object] = values.unset,
        task_reservation_timeout: Union[int, object] = values.unset,
        re_evaluate_tasks: Union[str, object] = values.unset,
    ) -> "WorkflowInstance":
        """
        Update the WorkflowInstance

        :param friendly_name: A descriptive string that you create to describe the Workflow resource. For example, `Inbound Call Workflow` or `2014 Outbound Campaign`.
        :param assignment_callback_url: The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
        :param fallback_assignment_callback_url: The URL that we should call when a call to the `assignment_callback_url` fails.
        :param configuration: A JSON string that contains the rules to apply to the Workflow. See [Configuring Workflows](https://www.twilio.com/docs/taskrouter/workflow-configuration) for more information.
        :param task_reservation_timeout: How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
        :param re_evaluate_tasks: Whether or not to re-evaluate Tasks. The default is `false`, which means Tasks in the Workflow will not be processed through the assignment loop again.

        :returns: The updated WorkflowInstance
        """
        return self._proxy.update(
            friendly_name=friendly_name,
            assignment_callback_url=assignment_callback_url,
            fallback_assignment_callback_url=fallback_assignment_callback_url,
            configuration=configuration,
            task_reservation_timeout=task_reservation_timeout,
            re_evaluate_tasks=re_evaluate_tasks,
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        assignment_callback_url: Union[str, object] = values.unset,
        fallback_assignment_callback_url: Union[str, object] = values.unset,
        configuration: Union[str, object] = values.unset,
        task_reservation_timeout: Union[int, object] = values.unset,
        re_evaluate_tasks: Union[str, object] = values.unset,
    ) -> "WorkflowInstance":
        """
        Asynchronous coroutine to update the WorkflowInstance

        :param friendly_name: A descriptive string that you create to describe the Workflow resource. For example, `Inbound Call Workflow` or `2014 Outbound Campaign`.
        :param assignment_callback_url: The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
        :param fallback_assignment_callback_url: The URL that we should call when a call to the `assignment_callback_url` fails.
        :param configuration: A JSON string that contains the rules to apply to the Workflow. See [Configuring Workflows](https://www.twilio.com/docs/taskrouter/workflow-configuration) for more information.
        :param task_reservation_timeout: How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
        :param re_evaluate_tasks: Whether or not to re-evaluate Tasks. The default is `false`, which means Tasks in the Workflow will not be processed through the assignment loop again.

        :returns: The updated WorkflowInstance
        """
        return await self._proxy.update_async(
            friendly_name=friendly_name,
            assignment_callback_url=assignment_callback_url,
            fallback_assignment_callback_url=fallback_assignment_callback_url,
            configuration=configuration,
            task_reservation_timeout=task_reservation_timeout,
            re_evaluate_tasks=re_evaluate_tasks,
        )

    @property
    def cumulative_statistics(self) -> WorkflowCumulativeStatisticsList:
        """
        Access the cumulative_statistics
        """
        return self._proxy.cumulative_statistics

    @property
    def real_time_statistics(self) -> WorkflowRealTimeStatisticsList:
        """
        Access the real_time_statistics
        """
        return self._proxy.real_time_statistics

    @property
    def statistics(self) -> WorkflowStatisticsList:
        """
        Access the statistics
        """
        return self._proxy.statistics

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkflowInstance {}>".format(context)


class WorkflowContext(InstanceContext):

    def __init__(self, version: Version, workspace_sid: str, sid: str):
        """
        Initialize the WorkflowContext

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the Workflow to update.
        :param sid: The SID of the Workflow resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
            "sid": sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/Workflows/{sid}".format(
            **self._solution
        )

        self._cumulative_statistics: Optional[WorkflowCumulativeStatisticsList] = None
        self._real_time_statistics: Optional[WorkflowRealTimeStatisticsList] = None
        self._statistics: Optional[WorkflowStatisticsList] = None

    def delete(self) -> bool:
        """
        Deletes the WorkflowInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the WorkflowInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> WorkflowInstance:
        """
        Fetch the WorkflowInstance


        :returns: The fetched WorkflowInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return WorkflowInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> WorkflowInstance:
        """
        Asynchronous coroutine to fetch the WorkflowInstance


        :returns: The fetched WorkflowInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return WorkflowInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        assignment_callback_url: Union[str, object] = values.unset,
        fallback_assignment_callback_url: Union[str, object] = values.unset,
        configuration: Union[str, object] = values.unset,
        task_reservation_timeout: Union[int, object] = values.unset,
        re_evaluate_tasks: Union[str, object] = values.unset,
    ) -> WorkflowInstance:
        """
        Update the WorkflowInstance

        :param friendly_name: A descriptive string that you create to describe the Workflow resource. For example, `Inbound Call Workflow` or `2014 Outbound Campaign`.
        :param assignment_callback_url: The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
        :param fallback_assignment_callback_url: The URL that we should call when a call to the `assignment_callback_url` fails.
        :param configuration: A JSON string that contains the rules to apply to the Workflow. See [Configuring Workflows](https://www.twilio.com/docs/taskrouter/workflow-configuration) for more information.
        :param task_reservation_timeout: How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
        :param re_evaluate_tasks: Whether or not to re-evaluate Tasks. The default is `false`, which means Tasks in the Workflow will not be processed through the assignment loop again.

        :returns: The updated WorkflowInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "AssignmentCallbackUrl": assignment_callback_url,
                "FallbackAssignmentCallbackUrl": fallback_assignment_callback_url,
                "Configuration": configuration,
                "TaskReservationTimeout": task_reservation_timeout,
                "ReEvaluateTasks": re_evaluate_tasks,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return WorkflowInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        assignment_callback_url: Union[str, object] = values.unset,
        fallback_assignment_callback_url: Union[str, object] = values.unset,
        configuration: Union[str, object] = values.unset,
        task_reservation_timeout: Union[int, object] = values.unset,
        re_evaluate_tasks: Union[str, object] = values.unset,
    ) -> WorkflowInstance:
        """
        Asynchronous coroutine to update the WorkflowInstance

        :param friendly_name: A descriptive string that you create to describe the Workflow resource. For example, `Inbound Call Workflow` or `2014 Outbound Campaign`.
        :param assignment_callback_url: The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
        :param fallback_assignment_callback_url: The URL that we should call when a call to the `assignment_callback_url` fails.
        :param configuration: A JSON string that contains the rules to apply to the Workflow. See [Configuring Workflows](https://www.twilio.com/docs/taskrouter/workflow-configuration) for more information.
        :param task_reservation_timeout: How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.
        :param re_evaluate_tasks: Whether or not to re-evaluate Tasks. The default is `false`, which means Tasks in the Workflow will not be processed through the assignment loop again.

        :returns: The updated WorkflowInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "AssignmentCallbackUrl": assignment_callback_url,
                "FallbackAssignmentCallbackUrl": fallback_assignment_callback_url,
                "Configuration": configuration,
                "TaskReservationTimeout": task_reservation_timeout,
                "ReEvaluateTasks": re_evaluate_tasks,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return WorkflowInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            sid=self._solution["sid"],
        )

    @property
    def cumulative_statistics(self) -> WorkflowCumulativeStatisticsList:
        """
        Access the cumulative_statistics
        """
        if self._cumulative_statistics is None:
            self._cumulative_statistics = WorkflowCumulativeStatisticsList(
                self._version,
                self._solution["workspace_sid"],
                self._solution["sid"],
            )
        return self._cumulative_statistics

    @property
    def real_time_statistics(self) -> WorkflowRealTimeStatisticsList:
        """
        Access the real_time_statistics
        """
        if self._real_time_statistics is None:
            self._real_time_statistics = WorkflowRealTimeStatisticsList(
                self._version,
                self._solution["workspace_sid"],
                self._solution["sid"],
            )
        return self._real_time_statistics

    @property
    def statistics(self) -> WorkflowStatisticsList:
        """
        Access the statistics
        """
        if self._statistics is None:
            self._statistics = WorkflowStatisticsList(
                self._version,
                self._solution["workspace_sid"],
                self._solution["sid"],
            )
        return self._statistics

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkflowContext {}>".format(context)


class WorkflowPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> WorkflowInstance:
        """
        Build an instance of WorkflowInstance

        :param payload: Payload response from the API
        """
        return WorkflowInstance(
            self._version, payload, workspace_sid=self._solution["workspace_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.WorkflowPage>"


class WorkflowList(ListResource):

    def __init__(self, version: Version, workspace_sid: str):
        """
        Initialize the WorkflowList

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the Workflow to read.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/Workflows".format(**self._solution)

    def create(
        self,
        friendly_name: str,
        configuration: str,
        assignment_callback_url: Union[str, object] = values.unset,
        fallback_assignment_callback_url: Union[str, object] = values.unset,
        task_reservation_timeout: Union[int, object] = values.unset,
    ) -> WorkflowInstance:
        """
        Create the WorkflowInstance

        :param friendly_name: A descriptive string that you create to describe the Workflow resource. For example, `Inbound Call Workflow` or `2014 Outbound Campaign`.
        :param configuration: A JSON string that contains the rules to apply to the Workflow. See [Configuring Workflows](https://www.twilio.com/docs/taskrouter/workflow-configuration) for more information.
        :param assignment_callback_url: The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
        :param fallback_assignment_callback_url: The URL that we should call when a call to the `assignment_callback_url` fails.
        :param task_reservation_timeout: How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.

        :returns: The created WorkflowInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "Configuration": configuration,
                "AssignmentCallbackUrl": assignment_callback_url,
                "FallbackAssignmentCallbackUrl": fallback_assignment_callback_url,
                "TaskReservationTimeout": task_reservation_timeout,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return WorkflowInstance(
            self._version, payload, workspace_sid=self._solution["workspace_sid"]
        )

    async def create_async(
        self,
        friendly_name: str,
        configuration: str,
        assignment_callback_url: Union[str, object] = values.unset,
        fallback_assignment_callback_url: Union[str, object] = values.unset,
        task_reservation_timeout: Union[int, object] = values.unset,
    ) -> WorkflowInstance:
        """
        Asynchronously create the WorkflowInstance

        :param friendly_name: A descriptive string that you create to describe the Workflow resource. For example, `Inbound Call Workflow` or `2014 Outbound Campaign`.
        :param configuration: A JSON string that contains the rules to apply to the Workflow. See [Configuring Workflows](https://www.twilio.com/docs/taskrouter/workflow-configuration) for more information.
        :param assignment_callback_url: The URL from your application that will process task assignment events. See [Handling Task Assignment Callback](https://www.twilio.com/docs/taskrouter/handle-assignment-callbacks) for more details.
        :param fallback_assignment_callback_url: The URL that we should call when a call to the `assignment_callback_url` fails.
        :param task_reservation_timeout: How long TaskRouter will wait for a confirmation response from your application after it assigns a Task to a Worker. Can be up to `86,400` (24 hours) and the default is `120`.

        :returns: The created WorkflowInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "Configuration": configuration,
                "AssignmentCallbackUrl": assignment_callback_url,
                "FallbackAssignmentCallbackUrl": fallback_assignment_callback_url,
                "TaskReservationTimeout": task_reservation_timeout,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return WorkflowInstance(
            self._version, payload, workspace_sid=self._solution["workspace_sid"]
        )

    def stream(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[WorkflowInstance]:
        """
        Streams WorkflowInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str friendly_name: The `friendly_name` of the Workflow resources to read.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(friendly_name=friendly_name, page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[WorkflowInstance]:
        """
        Asynchronously streams WorkflowInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param str friendly_name: The `friendly_name` of the Workflow resources to read.
        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(
            friendly_name=friendly_name, page_size=limits["page_size"]
        )

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[WorkflowInstance]:
        """
        Lists WorkflowInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str friendly_name: The `friendly_name` of the Workflow resources to read.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                friendly_name=friendly_name,
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[WorkflowInstance]:
        """
        Asynchronously lists WorkflowInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param str friendly_name: The `friendly_name` of the Workflow resources to read.
        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                friendly_name=friendly_name,
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        friendly_name: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> WorkflowPage:
        """
        Retrieve a single page of WorkflowInstance records from the API.
        Request is executed immediately

        :param friendly_name: The `friendly_name` of the Workflow resources to read.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of WorkflowInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return WorkflowPage(self._version, response, self._solution)

    async def page_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> WorkflowPage:
        """
        Asynchronously retrieve a single page of WorkflowInstance records from the API.
        Request is executed immediately

        :param friendly_name: The `friendly_name` of the Workflow resources to read.
        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of WorkflowInstance
        """
        data = values.of(
            {
                "FriendlyName": friendly_name,
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return WorkflowPage(self._version, response, self._solution)

    def get_page(self, target_url: str) -> WorkflowPage:
        """
        Retrieve a specific page of WorkflowInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of WorkflowInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return WorkflowPage(self._version, response, self._solution)

    async def get_page_async(self, target_url: str) -> WorkflowPage:
        """
        Asynchronously retrieve a specific page of WorkflowInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of WorkflowInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return WorkflowPage(self._version, response, self._solution)

    def get(self, sid: str) -> WorkflowContext:
        """
        Constructs a WorkflowContext

        :param sid: The SID of the Workflow resource to update.
        """
        return WorkflowContext(
            self._version, workspace_sid=self._solution["workspace_sid"], sid=sid
        )

    def __call__(self, sid: str) -> WorkflowContext:
        """
        Constructs a WorkflowContext

        :param sid: The SID of the Workflow resource to update.
        """
        return WorkflowContext(
            self._version, workspace_sid=self._solution["workspace_sid"], sid=sid
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.WorkflowList>"
