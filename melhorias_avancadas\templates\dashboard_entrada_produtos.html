<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 Dashboard Entrada de Produtos - SantaClara</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --dark-bg: #343a40;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }

        .header-section {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .kpi-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 5px solid var(--secondary-color);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .kpi-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        .kpi-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .btn-custom {
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            color: white;
        }

        .table-container {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            background: #f8f9fa;
            color: var(--primary-color);
            font-weight: 600;
            margin-right: 5px;
        }

        .nav-tabs .nav-link.active {
            background: var(--secondary-color);
            color: white;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }

        .loading i {
            font-size: 3rem;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-success { background: #d4edda; color: #155724; }
        .status-warning { background: #fff3cd; color: #856404; }
        .status-danger { background: #f8d7da; color: #721c24; }
        .status-info { background: #d1ecf1; color: #0c5460; }

        .price-trend-up { color: var(--success-color); }
        .price-trend-down { color: var(--danger-color); }
        .price-trend-stable { color: var(--info-color); }

        @media (max-width: 768px) {
            .main-container {
                margin: 10px;
                padding: 15px;
            }
            
            .kpi-value {
                font-size: 1.8rem;
            }
            
            .chart-container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Header -->
        <div class="header-section">
            <h1><i class="fas fa-boxes me-3"></i>Dashboard Entrada de Produtos</h1>
            <p class="mb-0">Análise completa de entrada de produtos com filtros avançados e KPIs</p>
        </div>

        <!-- Filtros -->
        <div class="filter-section">
            <h4><i class="fas fa-filter me-2"></i>Filtros Avançados</h4>
            <div class="row">
                <div class="col-md-2">
                    <label class="form-label">Unidade</label>
                    <select class="form-select" id="filtroUnidade">
                        <option value="TODAS">Todas as Unidades</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Data Início</label>
                    <input type="date" class="form-control" id="filtroDataInicio">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Data Fim</label>
                    <input type="date" class="form-control" id="filtroDataFim">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Produto</label>
                    <select class="form-select" id="filtroProduto">
                        <option value="">Todos os Produtos</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">Fornecedor</label>
                    <input type="text" class="form-control" id="filtroFornecedor" placeholder="Nome do fornecedor">
                </div>
                <div class="col-md-2">
                    <label class="form-label">Nº da NF</label>
                    <input type="text" class="form-control" id="filtroNumeroNF" placeholder="Número da nota">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <button class="btn btn-custom me-2" onclick="aplicarFiltros()">
                        <i class="fas fa-search me-2"></i>Aplicar Filtros
                    </button>
                    <button class="btn btn-outline-secondary" onclick="limparFiltros()">
                        <i class="fas fa-eraser me-2"></i>Limpar Filtros
                    </button>
                </div>
            </div>
        </div>

        <!-- KPIs -->
        <div class="row" id="kpisContainer">
            <div class="col-md-2">
                <div class="kpi-card">
                    <div class="kpi-value" id="kpiTotalProdutos">-</div>
                    <div class="kpi-label">Total de Produtos</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="kpi-card">
                    <div class="kpi-value" id="kpiProdutosUnicos">-</div>
                    <div class="kpi-label">Produtos Únicos</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="kpi-card">
                    <div class="kpi-value" id="kpiValorTotal">-</div>
                    <div class="kpi-label">Valor Total</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="kpi-card">
                    <div class="kpi-value" id="kpiFornecedores">-</div>
                    <div class="kpi-label">Fornecedores</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="kpi-card">
                    <div class="kpi-value" id="kpiUnidades">-</div>
                    <div class="kpi-label">Unidades Ativas</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="kpi-card">
                    <div class="kpi-value" id="kpiTicketMedio">-</div>
                    <div class="kpi-label">Ticket Médio</div>
                </div>
            </div>
        </div>

        <!-- Tabs de Conteúdo -->
        <ul class="nav nav-tabs" id="dashboardTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="lista-tab" data-bs-toggle="tab" data-bs-target="#lista" type="button">
                    <i class="fas fa-list me-2"></i>Lista de Produtos
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="graficos-tab" data-bs-toggle="tab" data-bs-target="#graficos" type="button">
                    <i class="fas fa-chart-bar me-2"></i>Gráficos e Análises
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="precos-tab" data-bs-toggle="tab" data-bs-target="#precos" type="button">
                    <i class="fas fa-chart-line me-2"></i>Análise de Preços
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="mensal-tab" data-bs-toggle="tab" data-bs-target="#mensal" type="button">
                    <i class="fas fa-calendar me-2"></i>Produtos por Mês
                </button>
            </li>
        </ul>

        <div class="tab-content" id="dashboardTabsContent">
            <!-- Tab Lista de Produtos -->
            <div class="tab-pane fade show active" id="lista" role="tabpanel">
                <div class="table-container">
                    <h4><i class="fas fa-table me-2"></i>Lista Detalhada de Produtos</h4>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="tabelaProdutos">
                            <thead class="table-dark">
                                <tr>
                                    <th>Unidade</th>
                                    <th>Data</th>
                                    <th>Nº NF</th>
                                    <th>Fornecedor</th>
                                    <th>Produto</th>
                                    <th>Qtd</th>
                                    <th>Valor Unit.</th>
                                    <th>Valor Total</th>
                                </tr>
                            </thead>
                            <tbody id="tabelaProdutosBody">
                                <tr>
                                    <td colspan="8" class="loading">
                                        <i class="fas fa-spinner"></i><br>
                                        Carregando dados...
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Tab Gráficos -->
            <div class="tab-pane fade" id="graficos" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h5><i class="fas fa-sort-amount-up me-2"></i>Top Produtos por Quantidade</h5>
                            <canvas id="chartTopQuantidade"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h5><i class="fas fa-dollar-sign me-2"></i>Top Produtos por Valor</h5>
                            <canvas id="chartTopValor"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Análise de Preços -->
            <div class="tab-pane fade" id="precos" role="tabpanel">
                <div class="chart-container">
                    <h5><i class="fas fa-chart-line me-2"></i>Análise de Preços e Variação Histórica</h5>
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-select" id="seletorProdutoPreco">
                                <option value="TODOS">Todos os Produtos</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <select class="form-select" id="seletorPeriodoPreco">
                                <option value="6">Últimos 6 meses</option>
                                <option value="12" selected>Últimos 12 meses</option>
                                <option value="24">Últimos 24 meses</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-custom" onclick="carregarAnalisePrecos()">
                                <i class="fas fa-sync me-2"></i>Atualizar
                            </button>
                        </div>
                    </div>
                    <canvas id="chartAnalisePrecos"></canvas>
                </div>
            </div>

            <!-- Tab Produtos por Mês -->
            <div class="tab-pane fade" id="mensal" role="tabpanel">
                <div class="table-container">
                    <h5><i class="fas fa-calendar-alt me-2"></i>Produtos e Preços Médios por Mês</h5>
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <select class="form-select" id="seletorAnoMensal">
                                <option value="">Todos os Anos</option>
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                                <option value="2022">2022</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-custom" onclick="carregarProdutosMensais()">
                                <i class="fas fa-sync me-2"></i>Carregar
                            </button>
                        </div>
                    </div>
                    <div id="containerProdutosMensais">
                        <div class="loading">
                            <i class="fas fa-spinner"></i><br>
                            Selecione um ano para carregar os dados
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // Variáveis globais
        let tabelaProdutos = null;
        let chartTopQuantidade = null;
        let chartTopValor = null;
        let chartAnalisePrecos = null;

        // Inicialização
        $(document).ready(function() {
            console.log('🎯 Iniciando Dashboard de Entrada de Produtos');
            carregarOpcoesFiltros();
            carregarKPIs();
            carregarListaProdutos();
            carregarGraficos();
        });

        // Carregar opções de filtros
        async function carregarOpcoesFiltros() {
            try {
                const response = await fetch('/api/opcoes-filtros');
                const dados = await response.json();

                // Preencher select de unidades
                const selectUnidade = document.getElementById('filtroUnidade');
                selectUnidade.innerHTML = '<option value="TODAS">Todas as Unidades</option>';
                dados.unidades.forEach(unidade => {
                    if (unidade !== 'TODAS') {
                        selectUnidade.innerHTML += `<option value="${unidade}">${unidade}</option>`;
                    }
                });

                // Preencher select de produtos
                const selectProduto = document.getElementById('filtroProduto');
                const selectProdutoPreco = document.getElementById('seletorProdutoPreco');
                selectProduto.innerHTML = '<option value="">Todos os Produtos</option>';
                selectProdutoPreco.innerHTML = '<option value="TODOS">Todos os Produtos</option>';

                dados.produtos.forEach(produto => {
                    if (produto !== 'TODOS') {
                        selectProduto.innerHTML += `<option value="${produto}">${produto}</option>`;
                        selectProdutoPreco.innerHTML += `<option value="${produto}">${produto}</option>`;
                    }
                });

                console.log('✅ Opções de filtros carregadas');
            } catch (error) {
                console.error('❌ Erro ao carregar opções de filtros:', error);
            }
        }

        // Carregar KPIs
        async function carregarKPIs() {
            try {
                const unidade = document.getElementById('filtroUnidade').value;
                const url = unidade && unidade !== 'TODAS' ? `/api/kpis-produtos?unidade=${unidade}` : '/api/kpis-produtos';

                const response = await fetch(url);
                const kpis = await response.json();

                // Atualizar KPIs na tela
                document.getElementById('kpiTotalProdutos').textContent = kpis.total_produtos?.toLocaleString() || '0';
                document.getElementById('kpiProdutosUnicos').textContent = kpis.produtos_unicos?.toLocaleString() || '0';
                document.getElementById('kpiValorTotal').textContent = kpis.valor_total_formatado || 'R$ 0,00';
                document.getElementById('kpiFornecedores').textContent = kpis.fornecedores_unicos?.toLocaleString() || '0';
                document.getElementById('kpiUnidades').textContent = kpis.unidades_ativas?.toLocaleString() || '0';
                document.getElementById('kpiTicketMedio').textContent = kpis.ticket_medio_formatado || 'R$ 0,00';

                console.log('✅ KPIs carregados');
            } catch (error) {
                console.error('❌ Erro ao carregar KPIs:', error);
            }
        }

        // Carregar lista de produtos
        async function carregarListaProdutos() {
            try {
                const filtros = obterFiltros();
                const queryString = new URLSearchParams(filtros).toString();
                const url = queryString ? `/api/lista-produtos?${queryString}` : '/api/lista-produtos';

                const response = await fetch(url);
                const produtos = await response.json();

                // Destruir tabela existente se houver
                if (tabelaProdutos) {
                    tabelaProdutos.destroy();
                }

                // Preencher tabela
                const tbody = document.getElementById('tabelaProdutosBody');
                tbody.innerHTML = '';

                if (produtos.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="8" class="text-center">Nenhum produto encontrado</td></tr>';
                } else {
                    produtos.forEach(produto => {
                        tbody.innerHTML += `
                            <tr>
                                <td>${produto.unidade}</td>
                                <td>${produto.data}</td>
                                <td>${produto.numero_nf}</td>
                                <td>${produto.fornecedor}</td>
                                <td title="${produto.produto_original}">${produto.produto}</td>
                                <td>${produto.quantidade}</td>
                                <td>${produto.valor_unitario_formatado}</td>
                                <td>${produto.valor_total_formatado}</td>
                            </tr>
                        `;
                    });
                }

                // Inicializar DataTable
                tabelaProdutos = $('#tabelaProdutos').DataTable({
                    language: {
                        url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/pt-BR.json'
                    },
                    pageLength: 25,
                    order: [[1, 'desc']], // Ordenar por data decrescente
                    columnDefs: [
                        { targets: [5, 6, 7], className: 'text-end' } // Alinhar números à direita
                    ]
                });

                console.log(`✅ Lista de produtos carregada: ${produtos.length} itens`);
            } catch (error) {
                console.error('❌ Erro ao carregar lista de produtos:', error);
            }
        }

        // Obter filtros atuais
        function obterFiltros() {
            const filtros = {};

            const unidade = document.getElementById('filtroUnidade').value;
            if (unidade && unidade !== 'TODAS') filtros.unidade = unidade;

            const dataInicio = document.getElementById('filtroDataInicio').value;
            if (dataInicio) filtros.data_inicio = dataInicio;

            const dataFim = document.getElementById('filtroDataFim').value;
            if (dataFim) filtros.data_fim = dataFim;

            const produto = document.getElementById('filtroProduto').value;
            if (produto) filtros.produto = produto;

            const fornecedor = document.getElementById('filtroFornecedor').value;
            if (fornecedor) filtros.fornecedor = fornecedor;

            const numeroNF = document.getElementById('filtroNumeroNF').value;
            if (numeroNF) filtros.numero_nf = numeroNF;

            return filtros;
        }

        // Aplicar filtros
        function aplicarFiltros() {
            console.log('🔍 Aplicando filtros...');
            carregarKPIs();
            carregarListaProdutos();
            carregarGraficos();
        }

        // Limpar filtros
        function limparFiltros() {
            document.getElementById('filtroUnidade').value = 'TODAS';
            document.getElementById('filtroDataInicio').value = '';
            document.getElementById('filtroDataFim').value = '';
            document.getElementById('filtroProduto').value = '';
            document.getElementById('filtroFornecedor').value = '';
            document.getElementById('filtroNumeroNF').value = '';

            aplicarFiltros();
        }

        // Carregar gráficos
        async function carregarGraficos() {
            await carregarTopQuantidade();
            await carregarTopValor();
        }

        // Top produtos por quantidade
        async function carregarTopQuantidade() {
            try {
                const unidade = document.getElementById('filtroUnidade').value;
                const url = unidade && unidade !== 'TODAS' ? `/api/top-produtos-quantidade?unidade=${unidade}&limit=10` : '/api/top-produtos-quantidade?limit=10';

                const response = await fetch(url);
                const dados = await response.json();

                const ctx = document.getElementById('chartTopQuantidade').getContext('2d');

                if (chartTopQuantidade) {
                    chartTopQuantidade.destroy();
                }

                chartTopQuantidade = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: dados.labels,
                        datasets: [{
                            label: 'Quantidade Total',
                            data: dados.data,
                            backgroundColor: 'rgba(52, 152, 219, 0.8)',
                            borderColor: 'rgba(52, 152, 219, 1)',
                            borderWidth: 2
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });

                console.log('✅ Gráfico top quantidade carregado');
            } catch (error) {
                console.error('❌ Erro ao carregar gráfico top quantidade:', error);
            }
        }

        // Top produtos por valor
        async function carregarTopValor() {
            try {
                const unidade = document.getElementById('filtroUnidade').value;
                const url = unidade && unidade !== 'TODAS' ? `/api/top-produtos-valor?unidade=${unidade}&limit=10` : '/api/top-produtos-valor?limit=10';

                const response = await fetch(url);
                const dados = await response.json();

                const ctx = document.getElementById('chartTopValor').getContext('2d');

                if (chartTopValor) {
                    chartTopValor.destroy();
                }

                chartTopValor = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: dados.labels,
                        datasets: [{
                            data: dados.data,
                            backgroundColor: [
                                '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
                                '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#f1c40f'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });

                console.log('✅ Gráfico top valor carregado');
            } catch (error) {
                console.error('❌ Erro ao carregar gráfico top valor:', error);
            }
        }

        // Carregar análise de preços
        async function carregarAnalisePrecos() {
            try {
                const produto = document.getElementById('seletorProdutoPreco').value;
                const periodo = document.getElementById('seletorPeriodoPreco').value;

                const url = `/api/analise-precos?produto=${produto}&periodo=${periodo}`;
                const response = await fetch(url);
                const dados = await response.json();

                if (dados.erro) {
                    console.warn('⚠️ Erro na análise de preços:', dados.erro);
                    return;
                }

                const ctx = document.getElementById('chartAnalisePrecos').getContext('2d');

                if (chartAnalisePrecos) {
                    chartAnalisePrecos.destroy();
                }

                // Preparar dados para o gráfico
                const datasets = [];
                const produtos = [...new Set(dados.dados_mensais.map(d => d.Produto_Padronizado))];

                produtos.forEach((prod, index) => {
                    const dadosProduto = dados.dados_mensais.filter(d => d.Produto_Padronizado === prod);
                    const cores = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6'];

                    datasets.push({
                        label: prod,
                        data: dadosProduto.map(d => d.Preco_Medio),
                        borderColor: cores[index % cores.length],
                        backgroundColor: cores[index % cores.length] + '20',
                        tension: 0.4
                    });
                });

                const periodos = [...new Set(dados.dados_mensais.map(d => d.Periodo))].sort();

                chartAnalisePrecos = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: periodos,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return 'R$ ' + value.toLocaleString('pt-BR', {minimumFractionDigits: 2});
                                    }
                                }
                            }
                        }
                    }
                });

                console.log('✅ Análise de preços carregada');
            } catch (error) {
                console.error('❌ Erro ao carregar análise de preços:', error);
            }
        }

        // Carregar produtos mensais
        async function carregarProdutosMensais() {
            try {
                const ano = document.getElementById('seletorAnoMensal').value;
                const url = ano ? `/api/produtos-por-mes?ano=${ano}` : '/api/produtos-por-mes';

                const response = await fetch(url);
                const dados = await response.json();

                const container = document.getElementById('containerProdutosMensais');
                container.innerHTML = '';

                if (Object.keys(dados).length === 0) {
                    container.innerHTML = '<div class="text-center">Nenhum dado encontrado para o período selecionado</div>';
                    return;
                }

                // Criar acordeão para cada mês
                let acordeaoHTML = '<div class="accordion" id="acordeaoMensais">';

                Object.keys(dados).sort().reverse().forEach((mes, index) => {
                    const produtos = dados[mes];
                    const mesFormatado = new Date(mes + '-01').toLocaleDateString('pt-BR', { year: 'numeric', month: 'long' });

                    acordeaoHTML += `
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="heading${index}">
                                <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${index}">
                                    📅 ${mesFormatado} (${produtos.length} produtos)
                                </button>
                            </h2>
                            <div id="collapse${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" data-bs-parent="#acordeaoMensais">
                                <div class="accordion-body">
                                    <div class="table-responsive">
                                        <table class="table table-sm table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Produto</th>
                                                    <th>Preço Médio</th>
                                                    <th>Quantidade Total</th>
                                                    <th>Valor Total</th>
                                                    <th>Compras</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                    `;

                    produtos.forEach(produto => {
                        acordeaoHTML += `
                            <tr>
                                <td>${produto.produto}</td>
                                <td>${produto.preco_medio_formatado}</td>
                                <td>${produto.quantidade_total}</td>
                                <td>${produto.valor_total_formatado}</td>
                                <td>${produto.compras}</td>
                            </tr>
                        `;
                    });

                    acordeaoHTML += `
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                acordeaoHTML += '</div>';
                container.innerHTML = acordeaoHTML;

                console.log('✅ Produtos mensais carregados');
            } catch (error) {
                console.error('❌ Erro ao carregar produtos mensais:', error);
            }
        }
    </script>
</body>
</html>
