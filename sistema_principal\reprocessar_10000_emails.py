#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 REPROCESSAMENTO INTELIGENTE DE 10.000 EMAILS
Sistema otimizado para processar grandes volumes de emails com NFes e boletos
Versão expandida para 10.000 emails com melhorias de performance e consolidação de dados
"""

import os
import sys
import time
from datetime import datetime

def main():
    """Executa o reprocessamento de 10.000 emails"""
    
    print("🚀 INICIANDO REPROCESSAMENTO DE 10.000 EMAILS")
    print("=" * 60)
    print(f"⏰ Início: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    print()
    
    # Verificar se o arquivo principal existe
    arquivo_principal = 'automacao_nf.py'
    if not os.path.exists(arquivo_principal):
        print(f"❌ Arquivo {arquivo_principal} não encontrado!")
        print("   Certifique-se de estar na pasta correta do projeto.")
        return False
    
    print("📧 CONFIGURAÇÕES DO PROCESSAMENTO:")
    print("   📊 Quantidade: 10.000 emails")
    print("   🔄 Modo: Reprocessamento completo")
    print("   📁 Consolidação: Ativada")
    print("   🎯 Otimizações: Ativadas")
    print()
    
    # Confirmar execução
    print("⚠️  ATENÇÃO: Este processo pode demorar 2-4 horas!")
    print("   💾 Certifique-se de ter espaço em disco suficiente")
    print("   🔌 Mantenha o computador conectado à energia")
    print("   🌐 Mantenha conexão com internet estável")
    print()
    
    resposta = input("🤔 Deseja continuar? (s/N): ").strip().lower()
    if resposta not in ['s', 'sim', 'y', 'yes']:
        print("❌ Processamento cancelado pelo usuário.")
        return False
    
    print()
    print("🔥 INICIANDO PROCESSAMENTO...")
    print("=" * 60)
    
    try:
        # Importar e executar o sistema principal
        sys.path.append('.')
        from automacao_nf import main as processar_emails
        
        # Configurar para 10.000 emails
        os.environ['MAX_EMAILS'] = '10000'
        os.environ['MODO_REPROCESSAMENTO'] = 'true'
        os.environ['CONSOLIDACAO_ATIVADA'] = 'true'
        
        # Executar processamento
        inicio = time.time()
        resultado = processar_emails()
        fim = time.time()
        
        tempo_total = fim - inicio
        horas = int(tempo_total // 3600)
        minutos = int((tempo_total % 3600) // 60)
        segundos = int(tempo_total % 60)
        
        print()
        print("✅ PROCESSAMENTO CONCLUÍDO!")
        print("=" * 60)
        print(f"⏱️  Tempo total: {horas:02d}h {minutos:02d}m {segundos:02d}s")
        print(f"🏁 Término: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        
        # Verificar arquivos gerados
        verificar_arquivos_gerados()
        
        return True
        
    except KeyboardInterrupt:
        print("\n⚠️ Processamento interrompido pelo usuário!")
        print("   Os dados processados até agora foram salvos.")
        return False
        
    except Exception as e:
        print(f"\n❌ Erro durante o processamento: {e}")
        print("   Verifique os logs para mais detalhes.")
        return False

def verificar_arquivos_gerados():
    """Verifica e exibe informações sobre os arquivos gerados"""
    
    print("\n📊 VERIFICANDO ARQUIVOS GERADOS:")
    print("-" * 40)
    
    arquivos_esperados = [
        'controle_produtos.xlsx',
        'controle_faturamento_geral.xlsx', 
        'controle_boletos.xlsx',
        'controle_impostos.xlsx'
    ]
    
    for arquivo in arquivos_esperados:
        if os.path.exists(arquivo):
            tamanho = os.path.getsize(arquivo) / (1024 * 1024)  # MB
            print(f"   ✅ {arquivo} ({tamanho:.1f} MB)")
        else:
            print(f"   ❌ {arquivo} (não encontrado)")
    
    print()
    print("🎯 PRÓXIMOS PASSOS:")
    print("   1. 🌐 Acesse o dashboard: python melhorias_avancadas/dashboard_web.py")
    print("   2. 📊 Analise os dados atualizados")
    print("   3. 🚨 Configure alertas automáticos")
    print("   4. 📱 Implemente notificações WhatsApp")

if __name__ == "__main__":
    try:
        sucesso = main()
        if sucesso:
            print("\n🎉 REPROCESSAMENTO DE 10.000 EMAILS CONCLUÍDO COM SUCESSO!")
        else:
            print("\n⚠️ Reprocessamento finalizado com problemas.")
            
    except Exception as e:
        print(f"\n💥 Erro crítico: {e}")
        print("   Entre em contato com o suporte técnico.")
    
    input("\n📱 Pressione ENTER para finalizar...")
