#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TESTE ESPECÍFICO DE PAGINAÇÃO COM 1000 E-MAILS
"""

from automacao_nf import autenticar_gmail, buscar_emails
from datetime import datetime

def main():
    print("🧪 TESTE DE PAGINAÇÃO - 1000 E-MAILS")
    print("=" * 50)
    
    # Autenticar
    servico_gmail = autenticar_gmail()
    if not servico_gmail:
        print("❌ Falha na autenticação")
        return
    
    print("✅ Autenticação bem-sucedida!")
    
    # Teste com 1000 e-mails
    print("\n📧 Buscando 1000 e-mails com paginação...")
    inicio = datetime.now()
    
    emails = buscar_emails(servico_gmail, incluir_lidos=True, max_results=1000)
    
    fim = datetime.now()
    tempo = (fim - inicio).total_seconds()
    
    print(f"\n✅ RESULTADO:")
    print(f"   📧 E-mails encontrados: {len(emails)}")
    print(f"   ⏱️ Tempo de execução: {tempo:.2f} segundos")
    print(f"   📊 E-mails por segundo: {len(emails)/tempo:.1f}")
    
    if len(emails) >= 500:
        print("✅ Paginação funcionando - mais de 500 e-mails encontrados!")
    else:
        print("⚠️ Menos de 500 e-mails - pode não ter mais e-mails disponíveis")

if __name__ == '__main__':
    main()
