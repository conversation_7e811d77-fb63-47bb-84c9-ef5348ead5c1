import{r,E as Q,_ as Y,cD as y,M as m,O as Z,l as mt,n as D,z as gt,bi as ht,ct as It,H as K,C as F,j as c,bs as yt,bG as Tt,bt as wt,bb as Ct,bu as St,D as vt,bp as M}from"./index.C1z8KpLA.js";import{u as Vt}from"./uniqueId.j-1rlNNH.js";import{u as kt}from"./FormClearHelper.B67tgll0.js";import{I as Rt}from"./InputInstructions.D-Y8geDN.js";import{s as xt}from"./sprintf.D7DtBTRn.js";import{I as Dt}from"./input.DsCfafm0.js";import"./base-input.BoAa1U94.js";var tt=r.forwardRef(function(t,e){var a={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return r.createElement(Q,Y({iconAttrs:a,iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},t,{ref:e}),r.createElement("path",{d:"M0 3v2h8V3H0z"}))});tt.displayName="Minus";var et=r.forwardRef(function(t,e){var a={fill:"currentColor",xmlns:"http://www.w3.org/2000/svg"};return r.createElement(Q,Y({iconAttrs:a,iconVerticalAlign:"middle",iconViewBox:"0 0 8 8"},t,{ref:e}),r.createElement("path",{d:"M3 0v3H0v2h3v3h2V5h3V3H5V0H3z"}))});et.displayName="Plus";const Et=mt.getLogger("NumberInput");function Nt(t){return m(t)||t===""?void 0:t}const W=({value:t,format:e,step:a,dataType:b})=>{if(m(t))return null;let o=Nt(e);if(m(o)&&Z(a)){const d=a.toString();b===y.DataType.FLOAT&&a!==0&&d.includes(".")&&(o=`%0.${d.split(".")[1].length}f`)}if(m(o))return t.toString();try{return xt.sprintf(o,t)}catch(d){return Et.warn(`Error in sprintf(${o}, ${t}): ${d}`),String(t)}},zt=(t,e,a)=>m(t)?!1:t-e>=a,Ft=(t,e,a)=>m(t)?!1:t+e<=a,Wt=t=>(t.element.dataType===y.DataType.INT?t.widgetMgr.getIntValue(t.element):t.widgetMgr.getDoubleValue(t.element))??t.element.default??null,q=({step:t,dataType:e})=>t||(e===y.DataType.INT?1:.01),Bt=D("div",{target:"e5tuigk0"})(({theme:t})=>({display:"flex",flexDirection:"row",flexWrap:"nowrap",alignItems:"center",height:t.sizes.minElementHeight,borderWidth:t.sizes.borderWidth,borderStyle:"solid",borderColor:t.colors.widgetBorderColor??t.colors.secondaryBg,transitionDuration:"200ms",transitionProperty:"border",transitionTimingFunction:"cubic-bezier(0.2, 0.8, 0.4, 1)",borderRadius:t.radii.default,overflow:"hidden","&.focused":{borderColor:t.colors.primary},input:{MozAppearance:"textfield","&::-webkit-inner-spin-button, &::-webkit-outer-spin-button":{WebkitAppearance:"none",margin:t.spacing.none}}})),Lt=D("div",{target:"e5tuigk1"})({display:"flex",flexDirection:"row",alignSelf:"stretch"}),J=D("button",{target:"e5tuigk2"})(({theme:t})=>({margin:t.spacing.none,border:"none",height:t.sizes.full,display:"flex",alignItems:"center",width:t.sizes.numberInputControlsWidth,justifyContent:"center",color:t.colors.bodyText,transition:"color 300ms, backgroundColor 300ms",backgroundColor:t.colors.secondaryBg,"&:hover:enabled, &:focus:enabled":{color:t.colors.white,backgroundColor:t.colors.primary,transition:"none",outline:"none"},"&:active":{outline:"none",border:"none"},"&:last-of-type":{borderTopRightRadius:t.radii.default,borderBottomRightRadius:t.radii.default},"&:disabled":{cursor:"not-allowed",color:t.colors.fadedText40}})),At=D("div",{target:"e5tuigk3"})(({theme:t,clearable:e})=>({position:"absolute",marginRight:t.spacing.twoXS,left:0,right:`calc(${t.sizes.numberInputControlsWidth} * 2 + ${e?"1em":"0em"})`})),Ht=({disabled:t,element:e,widgetMgr:a,fragmentId:b})=>{var X;const o=gt(),{dataType:d,id:S,formId:p,default:B,format:L,icon:T,min:g,max:h}=e,[A,ot]=ht(),[s,rt]=r.useState(()=>q(e)),H=Wt({element:e,widgetMgr:a}),[I,w]=r.useState(!1),[l,C]=r.useState(H),[P,v]=r.useState(()=>W({value:H,...e,step:s})),[U,$]=r.useState(!1),V=r.useRef(null),O=r.useRef(Vt("number_input_")),k=zt(l,s,g),R=Ft(l,s,h),j=It({formId:p}),nt=j?a.allowFormEnterToSubmit(p):I,at=U&&A>o.breakpoints.hideWidgetDetails;r.useEffect(()=>{rt(q({step:e.step,dataType:e.dataType}))},[e.dataType,e.step]);const u=r.useCallback(({value:n,source:i})=>{var f;if(Z(n)&&(g>n||n>h))(f=V.current)==null||f.reportValidity();else{const x=n??B??null;switch(d){case y.DataType.INT:a.setIntValue({id:S,formId:p},x,i,b);break;case y.DataType.FLOAT:a.setDoubleValue({id:S,formId:p},x,i,b);break;default:throw new Error("Invalid data type")}w(!1),C(x),v(W({value:x,dataType:d,format:L,step:s}))}},[g,h,V,a,b,s,d,S,p,B,L]),st=r.useCallback(()=>{I&&u({value:l,source:{fromUi:!0}}),$(!1)},[I,l,u]),it=r.useCallback(()=>{$(!0)},[]),_=r.useCallback(()=>{const{value:n}=e;e.setValue=!1,C(n??null),v(W({value:n??null,...e,step:s})),u({value:n??null,source:{fromUi:!1}})},[e,s,u]);r.useEffect(()=>{e.setValue?_():u({value:l,source:{fromUi:!1}});const n=V.current;if(n){const i=f=>{f.preventDefault()};return n.addEventListener("wheel",i),()=>{n.removeEventListener("wheel",i)}}},[]),e.setValue&&_();const E=m(e.default)&&!t,lt=r.useCallback(()=>{const n=e.default??null;C(n),u({value:n,source:{fromUi:!0}})},[e]);kt({element:e,widgetMgr:a,onFormCleared:lt});const ct=n=>{const{value:i}=n.target;if(i==="")w(!0),C(null),v(null);else{let f;e.dataType===y.DataType.INT?f=parseInt(i,10):f=parseFloat(i),w(!0),C(f),v(i)}},N=r.useCallback(()=>{R&&(w(!0),u({value:(l??g)+s,source:{fromUi:!0}}))},[l,g,s,R]),z=r.useCallback(()=>{k&&(w(!0),u({value:(l??h)-s,source:{fromUi:!0}}))},[l,h,s,k]),ut=r.useCallback(n=>{const{key:i}=n;switch(i){case"ArrowUp":n.preventDefault(),N();break;case"ArrowDown":n.preventDefault(),z();break}},[N,z]),dt=r.useCallback(n=>{n.key==="Enter"&&(I&&u({value:l,source:{fromUi:!0}}),a.allowFormEnterToSubmit(p)&&a.submitForm(p,b))},[I,l,u,a,p,b]),G=T==null?void 0:T.startsWith(":material"),pt=G?"lg":"base",ft=K(o.iconSizes.lg)+2*K(o.spacing.twoXS),bt=T?o.breakpoints.hideNumberInputControls+ft:o.breakpoints.hideNumberInputControls;return F("div",{className:"stNumberInput","data-testid":"stNumberInput",ref:ot,children:[c(St,{label:e.label,disabled:t,labelVisibility:yt((X=e.labelVisibility)==null?void 0:X.value),htmlFor:O.current,children:e.help&&c(Tt,{children:c(wt,{content:e.help,placement:Ct.TOP_RIGHT})})}),F(Bt,{className:U?"focused":"","data-testid":"stNumberInputContainer",children:[c(Dt,{type:"number",inputRef:V,value:P??"",placeholder:e.placeholder,onBlur:st,onFocus:it,onChange:ct,onKeyPress:dt,onKeyDown:ut,clearable:E,clearOnEscape:E,disabled:t,"aria-label":e.label,startEnhancer:e.icon&&c(vt,{"data-testid":"stNumberInputIcon",iconValue:e.icon,size:pt}),id:O.current,overrides:{ClearIconContainer:{style:{padding:0}},ClearIcon:{props:{overrides:{Svg:{style:{color:o.colors.darkGray,padding:o.spacing.threeXS,height:o.sizes.clearIconSize,width:o.sizes.clearIconSize,":hover":{fill:o.colors.bodyText}}}}}},Input:{props:{"data-testid":"stNumberInputField",step:s,min:g,max:h,type:"number",inputMode:""},style:{lineHeight:o.lineHeights.inputWidget,paddingRight:o.spacing.sm,paddingLeft:o.spacing.md,paddingBottom:o.spacing.sm,paddingTop:o.spacing.sm}},InputContainer:{style:()=>({borderTopRightRadius:0,borderBottomRightRadius:0})},Root:{style:{borderTopRightRadius:0,borderBottomRightRadius:0,borderTopLeftRadius:0,borderBottomLeftRadius:0,borderLeftWidth:0,borderRightWidth:0,borderTopWidth:0,borderBottomWidth:0,paddingRight:0,paddingLeft:T?o.spacing.sm:0}},StartEnhancer:{style:{paddingLeft:0,paddingRight:0,minWidth:o.iconSizes.lg,color:G?o.colors.fadedText60:"inherit"}}}}),A>bt&&F(Lt,{children:[c(J,{"data-testid":"stNumberInputStepDown",onClick:z,disabled:!k||t,tabIndex:-1,children:c(M,{content:tt,size:"xs",color:k?"inherit":o.colors.disabled})}),c(J,{"data-testid":"stNumberInputStepUp",onClick:N,disabled:!R||t,tabIndex:-1,children:c(M,{content:et,size:"xs",color:R?"inherit":o.colors.disabled})})]})]}),at&&c(At,{clearable:E,children:c(Rt,{dirty:I,value:P??"",inForm:j,allowEnterToSubmit:nt})})]})},Xt=r.memo(Ht);export{Xt as default};
