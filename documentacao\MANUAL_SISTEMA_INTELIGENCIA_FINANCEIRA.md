# 🏢 MANUAL DO SISTEMA DE INTELIGÊNCIA FINANCEIRA

## 📋 VISÃO GERAL

O Sistema de Inteligência Financeira é uma solução completa que transforma e-mails com NFes e boletos em insights financeiros poderosos, relatórios gerenciais e alertas inteligentes.

## 🚀 FUNCIONALIDADES PRINCIPAIS

### 📧 **PROCESSAMENTO AUTOMÁTICO DE E-MAILS**
- ✅ Leitura automática de e-mails com NFes e boletos
- ✅ Extração de dados de XML (prioridade) e PDF (fallback)
- ✅ Proteção contra duplicação de dados
- ✅ Suporte a múltiplas unidades (15+ unidades ativas)
- ✅ Formatação brasileira automática (R$ 1.234,56)

### 📊 **DASHBOARD FINANCEIRO INTERATIVO**
- 💰 Resumo executivo com valores totais
- 🏪 Performance por unidade
- 🏭 Ranking de fornecedores
- 📈 Análise de fluxo de caixa
- 🚨 Detecção automática de anomalias

### 🔔 **SISTEMA DE ALERTAS INTELIGENTES**
- ⏰ Vencimentos próximos (7, 15, 30 dias)
- 💸 Gastos anômalos por unidade
- 📈 Variações significativas de performance
- 🎯 Alertas categorizados por prioridade

### 📋 **RELATÓRIOS GERENCIAIS AUTOMÁTICOS**
- 📊 Relatórios mensais detalhados
- 📈 Consolidado anual por unidade
- 📅 Controle de vencimentos
- 📄 Exportação para Excel formatado

## 🛠️ ARQUIVOS DO SISTEMA

### **PRINCIPAIS:**
- `automacao_nf.py` - Processamento de e-mails e NFes
- `central_inteligencia_financeira.py` - Sistema central integrado
- `dashboard_financeiro.py` - Dashboard e análises
- `sistema_alertas.py` - Sistema de alertas
- `relatorios_gerenciais.py` - Geração de relatórios

### **AUXILIARES:**
- `reprocessar_5000_emails.py` - Reprocessamento histórico
- `automacao_diaria.py` - Automação diária
- `teste_protecao_duplicacao.py` - Testes de proteção
- `reformatar_planilhas.py` - Reformatação de dados

### **CONFIGURAÇÃO:**
- `configurar_agendamento.bat` - Configurar execução automática
- `unidades_cnpjs.csv` - Cadastro de unidades
- `credentials.json` - Credenciais do Gmail API

## 📊 PLANILHAS GERADAS

### **CONTROLE_PRODUTOS.XLSX**
- 📦 Produtos de todas as NFes
- 💰 Valores formatados em R$
- 📊 Quantidades em formato brasileiro
- 🏪 Separação por unidade

### **CONTROLE_FATURAMENTO_GERAL.XLSX**
- 💵 Faturamento consolidado
- 📅 Controle de vencimentos
- 🏭 Dados de fornecedores
- 💳 Informações de parcelas

### **CONTROLE_BOLETOS.XLSX**
- 🧾 Boletos processados
- 📅 Datas de vencimento
- 💰 Valores em formato brasileiro

## 🎯 COMO USAR

### **1. USO DIÁRIO (RECOMENDADO)**
```bash
python central_inteligencia_financeira.py
# Escolher opção 1: Processar e-mails não lidos
```

### **2. REPROCESSAMENTO HISTÓRICO**
```bash
python reprocessar_5000_emails.py
# Ou escolher opção 2 no menu principal
```

### **3. APENAS DASHBOARD**
```bash
python dashboard_financeiro.py
# Ou: python central_inteligencia_financeira.py --dashboard
```

### **4. APENAS ALERTAS**
```bash
python sistema_alertas.py
# Ou: python central_inteligencia_financeira.py --alertas
```

### **5. APENAS RELATÓRIOS**
```bash
python relatorios_gerenciais.py
# Ou: python central_inteligencia_financeira.py --relatorios
```

### **6. ANÁLISE COMPLETA**
```bash
python central_inteligencia_financeira.py --analise
```

### **7. AUTOMAÇÃO DIÁRIA**
```bash
python automacao_diaria.py
```

## ⚙️ CONFIGURAÇÃO DE AUTOMAÇÃO

### **WINDOWS (AGENDADOR DE TAREFAS)**
1. Execute como Administrador: `configurar_agendamento.bat`
2. Digite o horário desejado (ex: 08:00)
3. Digite o caminho do Python
4. Confirme a configuração

### **MANUAL (AGENDADOR DE TAREFAS)**
1. Abra "Agendador de Tarefas"
2. Criar Tarefa Básica
3. Nome: "Sistema Inteligência Financeira"
4. Gatilho: Diariamente
5. Ação: Iniciar programa
6. Programa: `python.exe`
7. Argumentos: `caminho\automacao_diaria.py`

## 📈 INTERPRETANDO OS RESULTADOS

### **DASHBOARD FINANCEIRO**
- **Valor Total Processado**: Soma de todas as NFes
- **Unidades Ativas**: Quantas unidades têm movimentação
- **Top Unidades**: Ranking por valor de gastos
- **Top Fornecedores**: Maiores fornecedores por valor
- **Anomalias**: Valores muito acima da média

### **SISTEMA DE ALERTAS**
- **🔴 ALTA PRIORIDADE**: Vencimentos em 7 dias
- **🟡 MÉDIA PRIORIDADE**: Vencimentos em 15 dias, gastos anômalos
- **🟢 BAIXA PRIORIDADE**: Vencimentos em 30 dias, reduções de gastos

### **RELATÓRIOS GERENCIAIS**
- **Performance por Unidade**: Valor total, quantidade de NFs, valor médio
- **Evolução Mensal**: Comparativo mês a mês
- **Controle de Vencimentos**: Parcelas a vencer por período

## 🛡️ PROTEÇÕES IMPLEMENTADAS

### **CONTRA DUPLICAÇÃO**
- ✅ Verificação de arquivos já processados
- ✅ Chaves únicas para identificar registros duplicados
- ✅ Logs detalhados de duplicatas removidas
- ✅ Modo reprocessamento para casos especiais

### **CONTROLE DE QUALIDADE**
- ✅ Validação de dados antes da inserção
- ✅ Formatação automática de valores brasileiros
- ✅ Backup automático de planilhas
- ✅ Logs de execução detalhados

## 📁 ARQUIVOS DE CONTROLE

### **ARQUIVOS_PROCESSADOS.JSON**
- Lista de arquivos já processados
- Evita reprocessamento desnecessário

### **ALERTAS_SISTEMA.JSON**
- Histórico de alertas gerados
- Dados para análise de tendências

### **LOG_AUTOMACAO_DIARIA.TXT**
- Log detalhado da execução diária
- Histórico de sucessos e erros

### **RESUMO_EXECUCAO_DIARIA.JSON**
- Resumo da última execução
- Estatísticas de processamento

## 🔧 SOLUÇÃO DE PROBLEMAS

### **ERRO: "ModuleNotFoundError"**
```bash
pip install pandas openpyxl google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client pdfplumber matplotlib seaborn
```

### **ERRO: "Credentials not found"**
1. Baixe `credentials.json` do Google Cloud Console
2. Coloque no mesmo diretório dos scripts
3. Execute novamente

### **PLANILHAS VAZIAS**
1. Verifique se há e-mails com NFes na caixa de entrada
2. Confirme se as tags de busca estão corretas
3. Execute o teste de proteção: `python teste_protecao_duplicacao.py`

### **VALORES INCORRETOS**
1. Execute: `python reformatar_planilhas.py`
2. Isso aplicará formatação brasileira aos dados existentes

## 📞 SUPORTE E MANUTENÇÃO

### **VERIFICAÇÃO DE SAÚDE DO SISTEMA**
```bash
python teste_protecao_duplicacao.py
```

### **LIMPEZA DE DADOS**
```bash
# Remover arquivo de controle (cuidado!)
del arquivos_processados.json
```

### **BACKUP DE SEGURANÇA**
- As planilhas originais são automaticamente salvas como `.backup`
- Logs são mantidos em `log_automacao_diaria.txt`

## 🎉 BENEFÍCIOS DO SISTEMA

### **PARA GESTORES**
- 📊 Visão completa da situação financeira
- 🚨 Alertas proativos de vencimentos
- 📈 Análise de performance por unidade
- 📋 Relatórios executivos automáticos

### **PARA OPERACIONAL**
- 🤖 Automação completa do processamento
- 🛡️ Proteção contra erros e duplicatas
- 📱 Execução simples e intuitiva
- 🔄 Reprocessamento seguro quando necessário

### **PARA TI**
- 🏗️ Arquitetura modular e escalável
- 📝 Logs detalhados para troubleshooting
- ⚙️ Configuração flexível de automação
- 🔧 Ferramentas de manutenção integradas

---

## 🚀 **SISTEMA PRONTO PARA PRODUÇÃO!**

**O Sistema de Inteligência Financeira está completamente implementado e testado, pronto para transformar sua gestão financeira em uma máquina de insights e controle!**
