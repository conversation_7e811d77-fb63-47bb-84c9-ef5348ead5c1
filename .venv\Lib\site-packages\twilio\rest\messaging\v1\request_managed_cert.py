r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Messaging
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, Optional
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class RequestManagedCertInstance(InstanceResource):
    """
    :ivar domain_sid: The unique string that we created to identify the Domain resource.
    :ivar date_updated: Date that this Domain was last updated.
    :ivar date_created: Date that this Domain was registered to the Twilio platform to create a new Domain object.
    :ivar date_expires: Date that the private certificate associated with this domain expires. This is the expiration date of your existing cert.
    :ivar domain_name: Full url path for this domain.
    :ivar certificate_sid: The unique string that we created to identify this Certificate resource.
    :ivar url:
    :ivar managed: A boolean flag indicating if the certificate is managed by Twilio.
    :ivar requesting: A boolean flag indicating if a managed certificate needs to be fulfilled by Twilio.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        domain_sid: Optional[str] = None,
    ):
        super().__init__(version)

        self.domain_sid: Optional[str] = payload.get("domain_sid")
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_expires: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_expires")
        )
        self.domain_name: Optional[str] = payload.get("domain_name")
        self.certificate_sid: Optional[str] = payload.get("certificate_sid")
        self.url: Optional[str] = payload.get("url")
        self.managed: Optional[bool] = payload.get("managed")
        self.requesting: Optional[bool] = payload.get("requesting")

        self._solution = {
            "domain_sid": domain_sid or self.domain_sid,
        }
        self._context: Optional[RequestManagedCertContext] = None

    @property
    def _proxy(self) -> "RequestManagedCertContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: RequestManagedCertContext for this RequestManagedCertInstance
        """
        if self._context is None:
            self._context = RequestManagedCertContext(
                self._version,
                domain_sid=self._solution["domain_sid"],
            )
        return self._context

    def update(self) -> "RequestManagedCertInstance":
        """
        Update the RequestManagedCertInstance


        :returns: The updated RequestManagedCertInstance
        """
        return self._proxy.update()

    async def update_async(self) -> "RequestManagedCertInstance":
        """
        Asynchronous coroutine to update the RequestManagedCertInstance


        :returns: The updated RequestManagedCertInstance
        """
        return await self._proxy.update_async()

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Messaging.V1.RequestManagedCertInstance {}>".format(context)


class RequestManagedCertContext(InstanceContext):

    def __init__(self, version: Version, domain_sid: str):
        """
        Initialize the RequestManagedCertContext

        :param version: Version that contains the resource
        :param domain_sid: Unique string used to identify the domain that this certificate should be associated with.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "domain_sid": domain_sid,
        }
        self._uri = "/LinkShortening/Domains/{domain_sid}/RequestManagedCert".format(
            **self._solution
        )

    def update(self) -> RequestManagedCertInstance:
        """
        Update the RequestManagedCertInstance


        :returns: The updated RequestManagedCertInstance
        """

        data = values.of({})
        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return RequestManagedCertInstance(
            self._version, payload, domain_sid=self._solution["domain_sid"]
        )

    async def update_async(self) -> RequestManagedCertInstance:
        """
        Asynchronous coroutine to update the RequestManagedCertInstance


        :returns: The updated RequestManagedCertInstance
        """

        data = values.of({})
        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return RequestManagedCertInstance(
            self._version, payload, domain_sid=self._solution["domain_sid"]
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Messaging.V1.RequestManagedCertContext {}>".format(context)


class RequestManagedCertList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the RequestManagedCertList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, domain_sid: str) -> RequestManagedCertContext:
        """
        Constructs a RequestManagedCertContext

        :param domain_sid: Unique string used to identify the domain that this certificate should be associated with.
        """
        return RequestManagedCertContext(self._version, domain_sid=domain_sid)

    def __call__(self, domain_sid: str) -> RequestManagedCertContext:
        """
        Constructs a RequestManagedCertContext

        :param domain_sid: Unique string used to identify the domain that this certificate should be associated with.
        """
        return RequestManagedCertContext(self._version, domain_sid=domain_sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Messaging.V1.RequestManagedCertList>"
