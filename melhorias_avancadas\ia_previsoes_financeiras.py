#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
INTELIGÊNCIA ARTIFICIAL PARA PREVISÕES FINANCEIRAS
Sistema de ML para análise preditiva e insights automáticos
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timedelta
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class IAPrevisoes:
    def __init__(self):
        self.df_faturamento = None
        self.df_produtos = None
        self.modelos = {}
        self.scalers = {}
        self.previsoes = {}
        self.insights = []
        self.carregar_dados()
    
    def carregar_dados(self):
        """Carrega e processa dados para análise"""
        try:
            if os.path.exists('controle_faturamento_geral.xlsx'):
                self.df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
                self.processar_dados_faturamento()
            
            if os.path.exists('controle_produtos.xlsx'):
                self.df_produtos = pd.read_excel('controle_produtos.xlsx')
                self.processar_dados_produtos()
                
            print("✅ Dados carregados para análise de IA")
            
        except Exception as e:
            print(f"❌ Erro ao carregar dados: {e}")
    
    def processar_dados_faturamento(self):
        """Processa dados de faturamento para ML"""
        if self.df_faturamento is not None:
            # Converter valores
            self.df_faturamento['Valor_Numerico'] = self.df_faturamento['Valor Total NF'].apply(self.converter_valor_brasileiro)
            
            # Converter datas
            self.df_faturamento['Data_Emissao_Dt'] = pd.to_datetime(self.df_faturamento['Data Emissao'], format='%d/%m/%Y', errors='coerce')
            
            # Criar features temporais
            self.df_faturamento['Ano'] = self.df_faturamento['Data_Emissao_Dt'].dt.year
            self.df_faturamento['Mes'] = self.df_faturamento['Data_Emissao_Dt'].dt.month
            self.df_faturamento['Dia_Semana'] = self.df_faturamento['Data_Emissao_Dt'].dt.dayofweek
            self.df_faturamento['Dia_Mes'] = self.df_faturamento['Data_Emissao_Dt'].dt.day
            
            # Remover dados com datas inválidas
            self.df_faturamento = self.df_faturamento.dropna(subset=['Data_Emissao_Dt'])
    
    def processar_dados_produtos(self):
        """Processa dados de produtos para ML"""
        if self.df_produtos is not None:
            self.df_produtos['Valor_Total_Numerico'] = self.df_produtos['Valor Total Item'].apply(self.converter_valor_brasileiro)
            self.df_produtos['Quantidade_Numerica'] = self.df_produtos['Quantidade'].apply(self.converter_quantidade_brasileira)
            self.df_produtos['Data_Emissao_Dt'] = pd.to_datetime(self.df_produtos['Data Emissao'], format='%d/%m/%Y', errors='coerce')
    
    def converter_valor_brasileiro(self, valor):
        """Converte valor brasileiro para float"""
        try:
            if pd.isna(valor) or valor == '':
                return 0.0
            valor_str = str(valor).replace('R$', '').replace(' ', '').replace('.', '').replace(',', '.')
            return float(valor_str)
        except:
            return 0.0
    
    def converter_quantidade_brasileira(self, quantidade):
        """Converte quantidade brasileira para float"""
        try:
            if pd.isna(quantidade) or quantidade == '':
                return 0.0
            quantidade_str = str(quantidade).replace(',', '.')
            return float(quantidade_str)
        except:
            return 0.0
    
    def criar_dataset_temporal(self, unidade=None):
        """Cria dataset temporal para previsões"""
        if self.df_faturamento is None:
            return None
        
        df = self.df_faturamento.copy()
        
        # Filtrar por unidade se especificado
        if unidade:
            df = df[df['Unidade'] == unidade]
        
        # Agrupar por mês
        df_mensal = df.groupby([df['Data_Emissao_Dt'].dt.to_period('M')]).agg({
            'Valor_Numerico': ['sum', 'count', 'mean'],
            'Unidade': 'nunique'
        }).reset_index()
        
        # Flatten column names
        df_mensal.columns = ['Periodo', 'Valor_Total', 'Qtd_NFs', 'Valor_Medio', 'Qtd_Unidades']
        
        # Converter período para datetime
        df_mensal['Data'] = df_mensal['Periodo'].dt.to_timestamp()
        
        # Criar features temporais
        df_mensal['Mes'] = df_mensal['Data'].dt.month
        df_mensal['Ano'] = df_mensal['Data'].dt.year
        df_mensal['Trimestre'] = df_mensal['Data'].dt.quarter
        
        # Ordenar por data
        df_mensal = df_mensal.sort_values('Data').reset_index(drop=True)
        
        return df_mensal
    
    def treinar_modelo_previsao(self, unidade=None):
        """Treina modelo de previsão para uma unidade ou geral"""
        dataset = self.criar_dataset_temporal(unidade)
        
        if dataset is None or len(dataset) < 6:  # Mínimo 6 meses de dados
            return None
        
        # Preparar features
        features = ['Mes', 'Trimestre', 'Qtd_NFs', 'Valor_Medio']
        if unidade is None:
            features.append('Qtd_Unidades')
        
        X = dataset[features].fillna(0)
        y = dataset['Valor_Total']
        
        # Dividir dados (80% treino, 20% teste)
        split_idx = int(len(X) * 0.8)
        X_train, X_test = X[:split_idx], X[split_idx:]
        y_train, y_test = y[:split_idx], y[split_idx:]
        
        if len(X_train) < 3:  # Mínimo para treino
            return None
        
        # Normalizar dados
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test) if len(X_test) > 0 else None
        
        # Treinar modelos
        modelos = {
            'linear': LinearRegression(),
            'random_forest': RandomForestRegressor(n_estimators=50, random_state=42)
        }
        
        resultados = {}
        
        for nome, modelo in modelos.items():
            try:
                # Treinar
                modelo.fit(X_train_scaled, y_train)
                
                # Avaliar se há dados de teste
                if X_test_scaled is not None and len(X_test) > 0:
                    y_pred = modelo.predict(X_test_scaled)
                    mae = mean_absolute_error(y_test, y_pred)
                    r2 = r2_score(y_test, y_pred)
                else:
                    # Usar dados de treino para avaliação básica
                    y_pred = modelo.predict(X_train_scaled)
                    mae = mean_absolute_error(y_train, y_pred)
                    r2 = r2_score(y_train, y_pred)
                
                resultados[nome] = {
                    'modelo': modelo,
                    'scaler': scaler,
                    'mae': mae,
                    'r2': r2,
                    'features': features
                }
                
            except Exception as e:
                print(f"Erro ao treinar modelo {nome}: {e}")
                continue
        
        # Escolher melhor modelo
        if resultados:
            melhor_modelo = min(resultados.keys(), key=lambda k: resultados[k]['mae'])
            return resultados[melhor_modelo]
        
        return None
    
    def prever_proximo_mes(self, unidade=None):
        """Faz previsão para o próximo mês"""
        modelo_info = self.treinar_modelo_previsao(unidade)
        
        if modelo_info is None:
            return None
        
        # Dados do último período
        dataset = self.criar_dataset_temporal(unidade)
        ultimo_periodo = dataset.iloc[-1]
        
        # Preparar features para próximo mês
        proximo_mes = datetime.now() + timedelta(days=30)
        
        features_previsao = {
            'Mes': proximo_mes.month,
            'Trimestre': (proximo_mes.month - 1) // 3 + 1,
            'Qtd_NFs': ultimo_periodo['Qtd_NFs'],  # Usar média histórica
            'Valor_Medio': ultimo_periodo['Valor_Medio']
        }
        
        if unidade is None:
            features_previsao['Qtd_Unidades'] = ultimo_periodo['Qtd_Unidades']
        
        # Criar array de features
        X_previsao = np.array([list(features_previsao.values())])
        X_previsao_scaled = modelo_info['scaler'].transform(X_previsao)
        
        # Fazer previsão
        previsao = modelo_info['modelo'].predict(X_previsao_scaled)[0]
        
        return {
            'unidade': unidade or 'GERAL',
            'valor_previsto': previsao,
            'mes_previsao': proximo_mes.strftime('%m/%Y'),
            'confianca': modelo_info['r2'],
            'erro_medio': modelo_info['mae']
        }
    
    def detectar_tendencias(self, unidade=None):
        """Detecta tendências nos dados"""
        dataset = self.criar_dataset_temporal(unidade)
        
        if dataset is None or len(dataset) < 3:
            return None
        
        # Calcular tendência dos últimos 6 meses
        ultimos_6_meses = dataset.tail(6)
        
        if len(ultimos_6_meses) < 3:
            return None
        
        # Regressão linear simples para tendência
        X = np.arange(len(ultimos_6_meses)).reshape(-1, 1)
        y = ultimos_6_meses['Valor_Total'].values
        
        modelo_tendencia = LinearRegression()
        modelo_tendencia.fit(X, y)
        
        # Coeficiente angular indica tendência
        tendencia = modelo_tendencia.coef_[0]
        
        # Classificar tendência
        if tendencia > 1000:
            classificacao = "CRESCIMENTO FORTE"
            emoji = "📈"
        elif tendencia > 0:
            classificacao = "CRESCIMENTO MODERADO"
            emoji = "📊"
        elif tendencia > -1000:
            classificacao = "ESTÁVEL"
            emoji = "➡️"
        else:
            classificacao = "DECLÍNIO"
            emoji = "📉"
        
        return {
            'unidade': unidade or 'GERAL',
            'tendencia': tendencia,
            'classificacao': classificacao,
            'emoji': emoji,
            'valor_medio_6m': ultimos_6_meses['Valor_Total'].mean()
        }
    
    def detectar_sazonalidade(self):
        """Detecta padrões sazonais"""
        if self.df_faturamento is None:
            return None
        
        # Agrupar por mês do ano
        sazonalidade = self.df_faturamento.groupby('Mes')['Valor_Numerico'].agg(['mean', 'count']).reset_index()
        
        # Encontrar meses com maior e menor atividade
        mes_maior = sazonalidade.loc[sazonalidade['mean'].idxmax()]
        mes_menor = sazonalidade.loc[sazonalidade['mean'].idxmin()]
        
        meses_nomes = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
                      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
        
        return {
            'mes_pico': {
                'mes': meses_nomes[int(mes_maior['Mes']) - 1],
                'valor_medio': mes_maior['mean']
            },
            'mes_baixo': {
                'mes': meses_nomes[int(mes_menor['Mes']) - 1],
                'valor_medio': mes_menor['mean']
            },
            'variacao_sazonal': (mes_maior['mean'] - mes_menor['mean']) / mes_menor['mean'] * 100
        }
    
    def gerar_insights_automaticos(self):
        """Gera insights automáticos usando IA"""
        insights = []
        
        # Insight 1: Previsão geral
        previsao_geral = self.prever_proximo_mes()
        if previsao_geral:
            valor_formatado = f"R$ {previsao_geral['valor_previsto']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
            confianca = previsao_geral['confianca'] * 100
            
            insights.append({
                'tipo': 'PREVISÃO',
                'titulo': f"Previsão para {previsao_geral['mes_previsao']}",
                'descricao': f"Valor previsto: {valor_formatado} (confiança: {confianca:.1f}%)",
                'prioridade': 'ALTA' if confianca > 70 else 'MEDIA'
            })
        
        # Insight 2: Tendências por unidade
        if self.df_faturamento is not None:
            unidades = self.df_faturamento['Unidade'].unique()[:5]  # Top 5 unidades
            
            for unidade in unidades:
                tendencia = self.detectar_tendencias(unidade)
                if tendencia:
                    insights.append({
                        'tipo': 'TENDÊNCIA',
                        'titulo': f"{tendencia['emoji']} {unidade}",
                        'descricao': f"{tendencia['classificacao']} - Variação: R$ {tendencia['tendencia']:,.0f}/mês",
                        'prioridade': 'ALTA' if 'DECLÍNIO' in tendencia['classificacao'] else 'MEDIA'
                    })
        
        # Insight 3: Sazonalidade
        sazonalidade = self.detectar_sazonalidade()
        if sazonalidade:
            insights.append({
                'tipo': 'SAZONALIDADE',
                'titulo': f"Padrão Sazonal Detectado",
                'descricao': f"Pico em {sazonalidade['mes_pico']['mes']}, baixa em {sazonalidade['mes_baixo']['mes']} (variação: {sazonalidade['variacao_sazonal']:.1f}%)",
                'prioridade': 'MEDIA'
            })
        
        self.insights = insights
        return insights
    
    def salvar_previsoes(self):
        """Salva previsões em arquivo JSON"""
        try:
            # Gerar previsões para todas as unidades principais
            previsoes = {}
            
            # Previsão geral
            previsao_geral = self.prever_proximo_mes()
            if previsao_geral:
                previsoes['GERAL'] = previsao_geral
            
            # Previsões por unidade
            if self.df_faturamento is not None:
                unidades = self.df_faturamento['Unidade'].value_counts().head(10).index
                
                for unidade in unidades:
                    previsao = self.prever_proximo_mes(unidade)
                    if previsao:
                        previsoes[unidade] = previsao
            
            # Adicionar insights
            insights = self.gerar_insights_automaticos()
            
            # Salvar arquivo
            resultado = {
                'timestamp': datetime.now().isoformat(),
                'previsoes': previsoes,
                'insights': insights,
                'sazonalidade': self.detectar_sazonalidade()
            }
            
            with open('previsoes_ia.json', 'w', encoding='utf-8') as f:
                json.dump(resultado, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Previsões salvas: {len(previsoes)} unidades, {len(insights)} insights")
            
        except Exception as e:
            print(f"❌ Erro ao salvar previsões: {e}")
    
    def gerar_relatorio_ia(self):
        """Gera relatório completo de IA"""
        print("🤖 RELATÓRIO DE INTELIGÊNCIA ARTIFICIAL")
        print("=" * 60)
        
        # Previsão geral
        previsao_geral = self.prever_proximo_mes()
        if previsao_geral:
            print(f"\n📈 PREVISÃO GERAL PARA {previsao_geral['mes_previsao']}:")
            valor_formatado = f"R$ {previsao_geral['valor_previsto']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
            print(f"   💰 Valor Previsto: {valor_formatado}")
            print(f"   🎯 Confiança: {previsao_geral['confianca']*100:.1f}%")
        
        # Insights automáticos
        insights = self.gerar_insights_automaticos()
        if insights:
            print(f"\n🧠 INSIGHTS AUTOMÁTICOS ({len(insights)}):")
            for insight in insights:
                emoji = "🔴" if insight['prioridade'] == 'ALTA' else "🟡"
                print(f"   {emoji} {insight['titulo']}")
                print(f"      {insight['descricao']}")
        
        # Sazonalidade
        sazonalidade = self.detectar_sazonalidade()
        if sazonalidade:
            print(f"\n📅 ANÁLISE SAZONAL:")
            print(f"   📈 Mês de Pico: {sazonalidade['mes_pico']['mes']}")
            print(f"   📉 Mês de Baixa: {sazonalidade['mes_baixo']['mes']}")
            print(f"   📊 Variação: {sazonalidade['variacao_sazonal']:.1f}%")
        
        # Salvar previsões
        self.salvar_previsoes()

def main():
    """Função principal"""
    print("🤖 SISTEMA DE INTELIGÊNCIA ARTIFICIAL FINANCEIRA")
    print("=" * 60)
    
    ia = IAPrevisoes()
    
    if ia.df_faturamento is None:
        print("❌ Nenhum dado disponível para análise")
        return
    
    print(f"✅ Dados carregados: {len(ia.df_faturamento)} registros")
    
    # Menu de opções
    while True:
        print("\n🤖 OPÇÕES DE IA:")
        print("1. 📈 Gerar previsões")
        print("2. 🧠 Análise de tendências")
        print("3. 📅 Detectar sazonalidade")
        print("4. 💡 Insights automáticos")
        print("5. 📋 Relatório completo")
        print("6. ❌ Sair")
        
        opcao = input("\nEscolha uma opção (1-6): ").strip()
        
        if opcao == '1':
            previsao = ia.prever_proximo_mes()
            if previsao:
                valor = f"R$ {previsao['valor_previsto']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
                print(f"📈 Previsão: {valor} para {previsao['mes_previsao']}")
            else:
                print("❌ Dados insuficientes para previsão")
        
        elif opcao == '2':
            tendencia = ia.detectar_tendencias()
            if tendencia:
                print(f"{tendencia['emoji']} Tendência: {tendencia['classificacao']}")
            else:
                print("❌ Dados insuficientes para análise de tendência")
        
        elif opcao == '3':
            sazonalidade = ia.detectar_sazonalidade()
            if sazonalidade:
                print(f"📈 Pico: {sazonalidade['mes_pico']['mes']}")
                print(f"📉 Baixa: {sazonalidade['mes_baixo']['mes']}")
            else:
                print("❌ Dados insuficientes para análise sazonal")
        
        elif opcao == '4':
            insights = ia.gerar_insights_automaticos()
            for insight in insights:
                print(f"💡 {insight['titulo']}: {insight['descricao']}")
        
        elif opcao == '5':
            ia.gerar_relatorio_ia()
        
        elif opcao == '6':
            print("👋 Encerrando IA...")
            break
        
        else:
            print("❌ Opção inválida!")

if __name__ == '__main__':
    main()
