import os
import pickle
import base64
import re
import io
import json
import xml.etree.ElementTree as ET
from datetime import datetime
import pdfplumber
import pandas as pd
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import traceback

# --- CONFIGURAÇÕES GLOBAIS ---
SCOPES = ['https://www.googleapis.com/auth/gmail.modify']
CAMINHO_CREDENCIAIS = 'credentials.json'
ARQUIVO_TOKEN = 'token.pickle'
PASTA_BASE = os.getcwd()
PASTA_INCONSISTENCIAS = os.path.join(PASTA_BASE, 'INCONSISTENCIAS')
ARQUIVO_UNIDADES_CNPJ = 'unidades_cnpjs.csv'

# Planilhas de controle
PLANILHA_FATURAMENTO_NF = 'controle_faturamento_geral.xlsx'
PLANILHA_PRODUTOS = 'controle_produtos.xlsx'
PLANILHA_BOLETOS = 'controle_boletos.xlsx'
PLANILHA_IMPOSTOS = 'controle_impostos.xlsx'

# Arquivo de controle de duplicação
ARQUIVO_CONTROLE_PROCESSADOS = 'arquivos_processados.json'

# Palavras-chave para classificação e busca no Gmail - EXPANDIDAS
KW_NOTAS_FISCAIS = ['nota fiscal', 'notas fiscais', 'nfe', 'nf-e', 'nf', 'danfe', 'xml']
KW_BOLETOS = ['boleto', 'fatura', 'cobrança', 'duplicata']
KW_IMPOSTOS = ['pis', 'cofins', 'irpj', 'csll', 'iss', 'das', 'darf', 'guia', 'recolhimento']

# --- FUNÇÕES GMAIL ---
def autenticar_gmail():
    creds = None
    if os.path.exists(ARQUIVO_TOKEN):
        with open(ARQUIVO_TOKEN, 'rb') as token:
            creds = pickle.load(token)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            if os.path.exists(ARQUIVO_TOKEN): os.remove(ARQUIVO_TOKEN)
            try:
                flow = InstalledAppFlow.from_client_secrets_file(CAMINHO_CREDENCIAIS, SCOPES)
                creds = flow.run_local_server(port=0)
            except FileNotFoundError:
                print(f"ERRO: Arquivo '{CAMINHO_CREDENCIAIS}' não encontrado.")
                return None
        with open(ARQUIVO_TOKEN, 'wb') as token:
            pickle.dump(creds, token)
    try:
        service = build('gmail', 'v1', credentials=creds)
        print("Autenticação com Gmail bem-sucedida!")
        return service
    except HttpError as error:
        print(f'Erro ao construir o serviço do Gmail: {error}')
        return None

def buscar_emails(service, incluir_lidos=False, max_results=100):
    """Busca e-mails com anexos relacionados a notas fiscais com paginação para grandes volumes"""
    # Busca expandida para capturar mais e-mails com NFe/XML
    query_parts = [f'subject:("{kw}")' for kw in KW_NOTAS_FISCAIS + KW_BOLETOS + KW_IMPOSTOS]

    # Construir query final - incluir ou não e-mails lidos
    if incluir_lidos:
        query = f"has:attachment ({' OR '.join(query_parts)})"
        print(f"🔍 Buscando TODOS os e-mails (incluindo lidos) com a query: {query}")
        print(f"📧 Limite máximo: {max_results} e-mails")
    else:
        query = f"is:unread has:attachment ({' OR '.join(query_parts)})"
        print(f"Buscando e-mails NÃO LIDOS com a query: {query}")

    try:
        all_messages = []
        page_token = None
        emails_coletados = 0

        # Se max_results <= 500, usar método simples
        if max_results <= 500:
            result = service.users().messages().list(
                userId='me',
                q=query,
                maxResults=max_results
            ).execute()
            return result.get('messages', [])

        # Para mais de 500 e-mails, usar paginação
        print(f"📄 Usando paginação para buscar {max_results} e-mails...")

        while emails_coletados < max_results:
            # Calcular quantos e-mails buscar nesta página (máximo 500 por página)
            emails_restantes = max_results - emails_coletados
            page_size = min(500, emails_restantes)

            print(f"   📄 Buscando página com {page_size} e-mails... (Total coletado: {emails_coletados})")

            # Fazer a requisição
            request_params = {
                'userId': 'me',
                'q': query,
                'maxResults': page_size
            }

            if page_token:
                request_params['pageToken'] = page_token

            result = service.users().messages().list(**request_params).execute()

            # Adicionar mensagens desta página
            messages = result.get('messages', [])
            all_messages.extend(messages)
            emails_coletados += len(messages)

            print(f"   ✅ Coletados {len(messages)} e-mails desta página. Total: {emails_coletados}")

            # Verificar se há mais páginas
            page_token = result.get('nextPageToken')
            if not page_token or not messages:
                print(f"   🏁 Não há mais páginas ou e-mails. Finalizando busca.")
                break

        print(f"✅ BUSCA CONCLUÍDA: {len(all_messages)} e-mails encontrados")
        return all_messages

    except HttpError as error:
        print(f'❌ Erro ao buscar e-mails: {error}')
        return []

def baixar_anexo_temporario(service, msg_id, part):
    try:
        attachment = service.users().messages().attachments().get(userId='me', messageId=msg_id, id=part['body']['attachmentId']).execute()
        return base64.urlsafe_b64decode(attachment['data'].encode('UTF-8'))
    except HttpError as error:
        print(f"  -> Erro ao baixar anexo: {error}")
        return None

def marcar_email_como_processado(service, msg_id):
    try:
        service.users().messages().modify(userId='me', id=msg_id, body={'removeLabelIds': ['UNREAD']}).execute()
        print(f"  -> E-mail ID {msg_id} marcado como processado.")
    except HttpError as error:
        print(f"  -> Erro ao marcar e-mail como lido: {error}")

# --- FUNÇÕES DE PROCESSAMENTO XML ---

def extrair_dados_xml_nfe(dados_xml):
    """Extrai dados completos de uma NFe a partir do XML"""
    try:
        # Remove namespace para facilitar a busca
        xml_content = dados_xml.decode('utf-8') if isinstance(dados_xml, bytes) else dados_xml
        # Remove namespaces para simplificar
        xml_content = re.sub(r'xmlns[^=]*="[^"]*"', '', xml_content)
        xml_content = re.sub(r'<(\w+):', r'<\1_', xml_content)
        xml_content = re.sub(r'</(\w+):', r'</\1_', xml_content)

        root = ET.fromstring(xml_content)

        dados = {}

        # Busca dados da NFe
        inf_nfe = root.find('.//infNFe') or root.find('.//infNFe_')
        if inf_nfe is None:
            return None

        # Dados básicos da nota
        ide = inf_nfe.find('.//ide') or inf_nfe.find('.//ide_')
        if ide is not None:
            dados['Numero Nota'] = ide.findtext('.//nNF') or ide.findtext('.//nNF_') or ''
            data_emissao = ide.findtext('.//dhEmi') or ide.findtext('.//dhEmi_') or ide.findtext('.//dEmi') or ide.findtext('.//dEmi_') or ''
            if data_emissao:
                # Converte formato ISO para DD/MM/YYYY
                if 'T' in data_emissao:
                    data_emissao = data_emissao.split('T')[0]
                if '-' in data_emissao:
                    partes = data_emissao.split('-')
                    if len(partes) == 3:
                        dados['Data Emissao'] = f"{partes[2]}/{partes[1]}/{partes[0]}"
                else:
                    dados['Data Emissao'] = data_emissao

        # Dados do emitente (fornecedor)
        emit = inf_nfe.find('.//emit') or inf_nfe.find('.//emit_')
        if emit is not None:
            nome_emit = emit.findtext('.//xNome') or emit.findtext('.//xNome_') or ''
            cnpj_emit = emit.findtext('.//CNPJ') or emit.findtext('.//CNPJ_') or ''
            dados['Fornecedor'] = nome_emit
            dados['CNPJ Emitente'] = cnpj_emit

        # Dados do destinatário
        dest = inf_nfe.find('.//dest') or inf_nfe.find('.//dest_')
        if dest is not None:
            cnpj_dest = dest.findtext('.//CNPJ') or dest.findtext('.//CNPJ_') or ''
            cnpj_limpo = re.sub(r'\D', '', cnpj_dest)
            dados['CNPJ Destinatario'] = cnpj_limpo
            print(f"    -> DEBUG: CNPJ destinatário extraído do XML: '{cnpj_limpo}'")

        # Valores totais
        total = inf_nfe.find('.//total') or inf_nfe.find('.//total_')
        if total is not None:
            icms_tot = total.find('.//ICMSTot') or total.find('.//ICMSTot_')
            if icms_tot is not None:
                dados['Valor Total'] = icms_tot.findtext('.//vNF') or icms_tot.findtext('.//vNF_') or '0'
                dados['Valor Produtos'] = icms_tot.findtext('.//vProd') or icms_tot.findtext('.//vProd_') or '0'

        # Dados de cobrança/duplicatas
        cobr = inf_nfe.find('.//cobr') or inf_nfe.find('.//cobr_')
        parcelas = []
        if cobr is not None:
            dups = cobr.findall('.//dup') or cobr.findall('.//dup_')
            for dup in dups:
                parcela = {}
                parcela['Num Parcela'] = dup.findtext('.//nDup') or dup.findtext('.//nDup_') or '1'
                data_venc = dup.findtext('.//dVenc') or dup.findtext('.//dVenc_') or ''
                if data_venc and '-' in data_venc:
                    partes = data_venc.split('-')
                    if len(partes) == 3:
                        parcela['Data Vencimento'] = f"{partes[2]}/{partes[1]}/{partes[0]}"
                else:
                    parcela['Data Vencimento'] = data_venc
                parcela['Valor Parcela'] = dup.findtext('.//vDup') or dup.findtext('.//vDup_') or '0'
                parcelas.append(parcela)
        dados['Parcelas'] = parcelas

        return dados

    except Exception as e:
        print(f"    -> Erro ao processar XML da NFe: {e}")
        return None

def extrair_itens_xml_nfe(dados_xml):
    """Extrai itens/produtos de uma NFe a partir do XML"""
    try:
        xml_content = dados_xml.decode('utf-8') if isinstance(dados_xml, bytes) else dados_xml
        # Remove namespaces
        xml_content = re.sub(r'xmlns[^=]*="[^"]*"', '', xml_content)
        xml_content = re.sub(r'<(\w+):', r'<\1_', xml_content)
        xml_content = re.sub(r'</(\w+):', r'</\1_', xml_content)

        root = ET.fromstring(xml_content)

        itens = []

        # Busca todos os itens/produtos
        dets = root.findall('.//det') or root.findall('.//det_')

        for det in dets:
            item = {}

            # Dados do produto
            prod = det.find('.//prod') or det.find('.//prod_')
            if prod is not None:
                item['Cod. Produto'] = prod.findtext('.//cProd') or prod.findtext('.//cProd_') or ''
                item['Descricao'] = prod.findtext('.//xProd') or prod.findtext('.//xProd_') or ''
                item['NCM'] = prod.findtext('.//NCM') or prod.findtext('.//NCM_') or ''
                item['CFOP'] = prod.findtext('.//CFOP') or prod.findtext('.//CFOP_') or ''
                item['Unidade'] = prod.findtext('.//uCom') or prod.findtext('.//uCom_') or ''
                item['Quantidade'] = prod.findtext('.//qCom') or prod.findtext('.//qCom_') or '0'
                item['Valor Unitario'] = prod.findtext('.//vUnCom') or prod.findtext('.//vUnCom_') or '0'
                item['Valor Total'] = prod.findtext('.//vProd') or prod.findtext('.//vProd_') or '0'

            # Dados de impostos
            imposto = det.find('.//imposto') or det.find('.//imposto_')
            if imposto is not None:
                # ICMS
                icms = imposto.find('.//ICMS') or imposto.find('.//ICMS_')
                if icms is not None:
                    for icms_tipo in icms:
                        item['ICMS CST'] = icms_tipo.findtext('.//CST') or icms_tipo.findtext('.//CST_') or ''
                        item['ICMS Aliquota'] = icms_tipo.findtext('.//pICMS') or icms_tipo.findtext('.//pICMS_') or '0'
                        item['ICMS Valor'] = icms_tipo.findtext('.//vICMS') or icms_tipo.findtext('.//vICMS_') or '0'
                        break

                # PIS
                pis = imposto.find('.//PIS') or imposto.find('.//PIS_')
                if pis is not None:
                    for pis_tipo in pis:
                        item['PIS CST'] = pis_tipo.findtext('.//CST') or pis_tipo.findtext('.//CST_') or ''
                        item['PIS Aliquota'] = pis_tipo.findtext('.//pPIS') or pis_tipo.findtext('.//pPIS_') or '0'
                        item['PIS Valor'] = pis_tipo.findtext('.//vPIS') or pis_tipo.findtext('.//vPIS_') or '0'
                        break

                # COFINS
                cofins = imposto.find('.//COFINS') or imposto.find('.//COFINS_')
                if cofins is not None:
                    for cofins_tipo in cofins:
                        item['COFINS CST'] = cofins_tipo.findtext('.//CST') or cofins_tipo.findtext('.//CST_') or ''
                        item['COFINS Aliquota'] = cofins_tipo.findtext('.//pCOFINS') or cofins_tipo.findtext('.//pCOFINS_') or '0'
                        item['COFINS Valor'] = cofins_tipo.findtext('.//vCOFINS') or cofins_tipo.findtext('.//vCOFINS_') or '0'
                        break

            if item.get('Descricao'):  # Só adiciona se tem descrição
                itens.append(item)

        return itens if itens else None

    except Exception as e:
        print(f"    -> Erro ao extrair itens do XML: {e}")
        return None

# --- FUNÇÕES DE LÓGICA E PROCESSAMENTO ---

def carregar_unidades(arquivo_csv):
    try:
        df = pd.read_csv(arquivo_csv, sep=';', dtype=str)
        # Remove todos os caracteres não numéricos do CNPJ (pontos, vírgulas, etc.)
        df['CNPJ_LIMPO'] = df['CNPJ'].str.replace(r'\D', '', regex=True)
        # Remove apenas zeros finais em excesso (mantém CNPJs válidos de 14 dígitos)
        # Ex: 2983267600012300 -> 29832676000123, mas mantém 32025245000160
        df['CNPJ_LIMPO'] = df['CNPJ_LIMPO'].apply(lambda x: x[:14] if len(x) > 14 else x)
        print("Tabela de unidades carregada com sucesso.")
        print(f"CNPJs carregados: {list(df['CNPJ_LIMPO'].values)}")
        return df
    except FileNotFoundError:
        print(f"ERRO CRÍTICO: Arquivo de unidades '{arquivo_csv}' não encontrado.")
        return None

def classificar_documento(assunto, nome_arquivo):
    texto_busca = f"{assunto.lower()} {nome_arquivo.lower()}"
    if any(kw in texto_busca for kw in KW_BOLETOS): return 'Boletos'
    if any(kw in texto_busca for kw in KW_NOTAS_FISCAIS): return 'Notas Fiscais'
    if any(kw in texto_busca for kw in KW_IMPOSTOS): return 'Impostos'
    return 'Outros'

def extrair_cnpj_destinatario(texto_pdf):
    try:
        bloco_destinatario = re.search(r'DESTINAT[ÁA]RIO(?:/REMETENTE)?\s*(.*?)(?:FATURA|CÁLCULO DO IMPOSTO)', texto_pdf, re.DOTALL | re.IGNORECASE)
        if bloco_destinatario:
            texto_bloco = bloco_destinatario.group(1)
            cnpj_match = re.search(r'(\d{2}\.\d{3}\.\d{3}/\d{4}-\d{2})', texto_bloco) or re.search(r'(\d{14})', texto_bloco)
            if cnpj_match:
                return re.sub(r'\D', '', cnpj_match.group(1))
    except Exception: pass
    return None

def extrair_todos_cnpjs(texto_pdf):
    try:
        cnpjs_encontrados = re.findall(r'(\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}|\d{14})', texto_pdf)
        return list(set([re.sub(r'\D', '', cnpj) for cnpj in cnpjs_encontrados]))
    except Exception: return []

def extrair_dados_nfe(texto_pdf):
    dados = {}
    try:
        dados['Numero Nota'] = (re.search(r'N[Ff][- ]e\s*N[º°.]\s*([\d\.\s]*)', texto_pdf, re.S) or re.search(r'N[º°.]\s*(\d[\d. ]{5,})', texto_pdf)).group(1).strip().replace(" ", "").replace(".", "")
        dados['Data Emissao'] = (re.search(r'EMISSÃO\s*([\d/]+)', texto_pdf, re.I) or re.search(r'Data de Emissão\s*[:\s]*\n?([\d/]+)', texto_pdf, re.I)).group(1)
        emitente_bloco = re.search(r'(?:Identificação do emitente|remetente)\s*(.*?)(?:\n\s*Endereço|\n\s*AER|\n\s*AV|\n\s*RUA|\n\s*ESTRADA)', texto_pdf, re.DOTALL | re.I)
        dados['Fornecedor'] = emitente_bloco.group(1).replace('\n', ' ').strip() if emitente_bloco else 'NÃO ENCONTRADO'
        
        parcelas = []
        faturas_bloco = re.search(r'(FATURA(?:/DUPLICATA)?)(.*?)(CÁLCULO DO IMPOSTO|DADOS ADICIONAIS)', texto_pdf, re.DOTALL | re.IGNORECASE)
        texto_faturas = faturas_bloco.group(2) if faturas_bloco else texto_pdf
        faturas = re.findall(r'(\d{2,3})?\s*([\d]{2}\/[\d]{2}\/[\d]{4})\s+([\d.,]+)', texto_faturas)
        for fatura in faturas:
            parcelas.append({'Num Parcela': fatura[0] or '1', 'Data Vencimento': fatura[1], 'Valor Parcela': fatura[2]})
        dados['Parcelas'] = parcelas
        
        return dados
    except (AttributeError, IndexError):
        return None

def extrair_dados_boleto(texto_pdf):
    try:
        dados = {}
        dados['Vencimento'] = (re.search(r'Vencimento\s*[:\s]*([\d/]+)', texto_pdf, re.I) or re.search(r'\n([\d/]{10})\s*[\d,.-]+', texto_pdf)).group(1)
        dados['Valor'] = (re.search(r'Valor do Documento\s*[:\s]*R?\$\s*([\d,.-]+)', texto_pdf, re.I) or re.search(r'VALOR\s*COBRADO\s*[:\s]*R?\$\s*([\d,.-]+)', texto_pdf, re.I)).group(1)
        return dados
    except(AttributeError, IndexError): return None

def extrair_itens_nfe(pdf_obj):
    itens_encontrados = []
    try:
        for page in pdf_obj.pages:
            tables = page.extract_tables({"vertical_strategy": "text", "horizontal_strategy": "lines", "snap_tolerance": 3, "text_tolerance": 3})
            for table in tables:
                if not (table and table[0]): continue
                header = str(table[0]).upper()
                if 'DESCRIÇÃO' in header or 'PROD' in header:
                    for row in table[1:]:
                        # Verifica se a linha tem o número mínimo de colunas e se a descrição não é vazia
                        if len(row) > 8 and row[1]:
                            item = {'Cod. Produto': row[0] or '','Descricao': (row[1] or '').replace('\n', ' '),'Quantidade': row[6] or '0','Valor Unitario': row[7] or '0','Valor Total': row[8] or '0'}
                            if 'DADOS DO ISSQN' not in item['Descricao'].upper():
                                itens_encontrados.append(item)
    except Exception as e:
        print(f"    -> Erro ao extrair tabela de itens: {e}")
    return itens_encontrados if itens_encontrados else None # Retorna None se nada for encontrado

def formatar_valor_brasileiro(valor):
    """Converte valor para formato brasileiro R$ 1.234,56"""
    try:
        if valor == '' or valor is None:
            return ''
        # Converter para float se for string
        if isinstance(valor, str):
            # Remover caracteres não numéricos exceto ponto e vírgula
            valor_limpo = valor.replace(',', '.').replace(' ', '').replace('R$', '').replace('$', '')
            valor_float = float(valor_limpo)
        else:
            valor_float = float(valor)

        # Formatar como moeda brasileira
        return f"R$ {valor_float:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
    except:
        return valor

def formatar_quantidade(quantidade):
    """Formata quantidade como número decimal padronizado"""
    try:
        if quantidade == '' or quantidade is None:
            return ''
        # Converter para float se for string
        if isinstance(quantidade, str):
            quantidade_limpa = quantidade.replace(',', '.').replace(' ', '')
            quantidade_float = float(quantidade_limpa)
        else:
            quantidade_float = float(quantidade)

        # Formatar com 3 casas decimais
        return f"{quantidade_float:.3f}".replace('.', ',')
    except:
        return quantidade

def carregar_arquivos_processados():
    """Carrega lista de arquivos já processados"""
    try:
        if os.path.exists(ARQUIVO_CONTROLE_PROCESSADOS):
            with open(ARQUIVO_CONTROLE_PROCESSADOS, 'r', encoding='utf-8') as f:
                return set(json.load(f))
        return set()
    except Exception as e:
        print(f"Erro ao carregar arquivos processados: {e}")
        return set()

def salvar_arquivo_processado(nome_arquivo):
    """Adiciona arquivo à lista de processados"""
    try:
        arquivos_processados = carregar_arquivos_processados()
        arquivos_processados.add(nome_arquivo)

        with open(ARQUIVO_CONTROLE_PROCESSADOS, 'w', encoding='utf-8') as f:
            json.dump(list(arquivos_processados), f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"Erro ao salvar arquivo processado: {e}")

def arquivo_ja_processado(nome_arquivo):
    """Verifica se arquivo já foi processado"""
    arquivos_processados = carregar_arquivos_processados()
    return nome_arquivo in arquivos_processados

def gerar_chave_unica(dados, tipo_planilha):
    """Gera uma chave única para identificar registros duplicados"""
    if 'produtos' in tipo_planilha.lower():
        # Para produtos: Unidade + Numero Nota + Cod. Produto + Descricao
        chave = f"{dados.get('Unidade', '')}-{dados.get('Numero Nota', '')}-{dados.get('Cod. Produto', '')}-{dados.get('Descricao Produto', '')}"
    elif 'faturamento' in tipo_planilha.lower():
        # Para faturamento: Unidade + Numero Nota + Num Parcela (se existir)
        chave = f"{dados.get('Unidade', '')}-{dados.get('Numero Nota', '')}-{dados.get('Num Parcela', 'UNICA')}"
    elif 'boletos' in tipo_planilha.lower():
        # Para boletos: Unidade + Data Vencimento + Valor
        chave = f"{dados.get('Unidade', '')}-{dados.get('Data Vencimento', '')}-{dados.get('Valor Documento', '')}"
    else:
        # Fallback genérico
        chave = f"{dados.get('Unidade', '')}-{dados.get('Arquivo', '')}"

    return chave.strip('-')

def verificar_duplicatas(df_existente, novos_dados, tipo_planilha):
    """Verifica e remove duplicatas dos novos dados"""
    if df_existente.empty:
        return novos_dados  # Se não há dados existentes, todos são novos

    dados_unicos = []
    duplicatas_encontradas = 0

    # Gerar chaves únicas para dados existentes
    chaves_existentes = set()
    for _, row in df_existente.iterrows():
        chave = gerar_chave_unica(row.to_dict(), tipo_planilha)
        chaves_existentes.add(chave)

    # Verificar novos dados contra existentes
    for dados in novos_dados:
        chave_nova = gerar_chave_unica(dados, tipo_planilha)

        if chave_nova not in chaves_existentes:
            dados_unicos.append(dados)
            chaves_existentes.add(chave_nova)  # Adicionar para evitar duplicatas dentro dos novos dados
        else:
            duplicatas_encontradas += 1
            print(f"    -> DUPLICATA DETECTADA e IGNORADA: {chave_nova}")

    if duplicatas_encontradas > 0:
        print(f"    -> 🛡️ PROTEÇÃO ATIVA: {duplicatas_encontradas} duplicata(s) removida(s)")
        print(f"    -> ✅ DADOS ÚNICOS: {len(dados_unicos)} registro(s) serão adicionados")

    return dados_unicos

def atualizar_planilha(nome_planilha, novos_dados_lista):
    """Atualiza planilhas com formatação brasileira e proteção contra duplicação"""
    try:
        if not novos_dados_lista:
            print(f"  -> Nenhum dado para adicionar à planilha '{nome_planilha}'")
            return

        # Aplicar formatação específica baseada no tipo de planilha
        if 'produtos' in nome_planilha.lower():
            # Formatação para planilha de produtos
            for dados in novos_dados_lista:
                if 'Quantidade' in dados:
                    dados['Quantidade'] = formatar_quantidade(dados['Quantidade'])
                if 'Valor Unitario' in dados:
                    dados['Valor Unitario'] = formatar_valor_brasileiro(dados['Valor Unitario'])
                if 'Valor Total Item' in dados:
                    dados['Valor Total Item'] = formatar_valor_brasileiro(dados['Valor Total Item'])
                if 'Valor Total' in dados:
                    dados['Valor Total'] = formatar_valor_brasileiro(dados['Valor Total'])
                # Formatação de impostos
                if 'ICMS Valor' in dados:
                    dados['ICMS Valor'] = formatar_valor_brasileiro(dados['ICMS Valor'])
                if 'PIS Valor' in dados:
                    dados['PIS Valor'] = formatar_valor_brasileiro(dados['PIS Valor'])
                if 'COFINS Valor' in dados:
                    dados['COFINS Valor'] = formatar_valor_brasileiro(dados['COFINS Valor'])

        elif 'faturamento' in nome_planilha.lower():
            # Formatação para planilha de faturamento
            for dados in novos_dados_lista:
                if 'Valor Parcela' in dados:
                    dados['Valor Parcela'] = formatar_valor_brasileiro(dados['Valor Parcela'])
                if 'Valor Total NF' in dados:
                    dados['Valor Total NF'] = formatar_valor_brasileiro(dados['Valor Total NF'])
                if 'Valor Produtos' in dados:
                    dados['Valor Produtos'] = formatar_valor_brasileiro(dados['Valor Produtos'])

        elif 'boletos' in nome_planilha.lower():
            # Formatação para planilha de boletos
            for dados in novos_dados_lista:
                if 'Valor' in dados:
                    dados['Valor'] = formatar_valor_brasileiro(dados['Valor'])

        # Carregar dados existentes para verificação de duplicatas
        if os.path.exists(nome_planilha):
            df_antigo = pd.read_excel(nome_planilha, engine='openpyxl', dtype=str)

            # 🛡️ PROTEÇÃO CONTRA DUPLICAÇÃO
            print(f"    -> 🔍 Verificando duplicatas em '{nome_planilha}'...")
            dados_unicos = verificar_duplicatas(df_antigo, novos_dados_lista, nome_planilha)

            if not dados_unicos:
                print(f"    -> ⚠️ TODOS os dados são duplicatas! Nenhum registro adicionado.")
                return

            df_novo = pd.DataFrame(dados_unicos)
            df_final = pd.concat([df_antigo, df_novo], ignore_index=True)
        else:
            df_novo = pd.DataFrame(novos_dados_lista)
            df_final = df_novo
            print(f"    -> 📋 Criando nova planilha '{nome_planilha}' com {len(novos_dados_lista)} registro(s)")

        df_final.to_excel(nome_planilha, index=False, engine='openpyxl')
        print(f"  -> Planilha '{nome_planilha}' atualizada com sucesso.")
    except Exception as e:
        print(f"    -> ERRO ao atualizar a planilha '{nome_planilha}': {e}")

def salvar_arquivo(dados_pdf, unidade, tipo_doc, nome_original):
    agora = datetime.now()
    ano, mes = str(agora.year), f"{agora.month:02d}"
    nome_pasta_unidade = "".join(c for c in unidade if c.isalnum() or c in (' ', '-')).rstrip()
    if unidade == "INCONSISTENCIAS": caminho_final = PASTA_INCONSISTENCIAS
    else: caminho_final = os.path.join(PASTA_BASE, nome_pasta_unidade, ano, mes, tipo_doc)
    os.makedirs(caminho_final, exist_ok=True)
    caminho_arquivo = os.path.join(caminho_final, nome_original)
    with open(caminho_arquivo, 'wb') as f: f.write(dados_pdf)
    print(f"  -> Arquivo salvo em: {caminho_arquivo}")
    return caminho_arquivo

def processar_anexo(dados_anexo, nome_arquivo, assunto, df_unidades, modo_reprocessamento=False):
    """Processa anexo - prioriza XML, fallback para PDF com proteção contra duplicação"""

    # 🛡️ PROTEÇÃO CONTRA DUPLICAÇÃO DE ARQUIVOS
    if not modo_reprocessamento and arquivo_ja_processado(nome_arquivo):
        print(f"  -> 🛡️ ARQUIVO JÁ PROCESSADO: '{nome_arquivo}' - IGNORANDO para evitar duplicação")
        return

    tipo_doc = classificar_documento(assunto, nome_arquivo)
    if tipo_doc == 'Outros':
        print(f"  -> Documento '{nome_arquivo}' classificado como 'Outros'. Ignorando.")
        return

    print(f"  -> Classificado como: {tipo_doc}")

    # Verifica se é XML (prioridade máxima para NFe)
    is_xml = nome_arquivo.lower().endswith('.xml')

    if is_xml and tipo_doc == 'Notas Fiscais':
        print("  -> Processando XML da NFe (método preferencial)")
        resultado = processar_xml_nfe(dados_anexo, nome_arquivo, assunto, df_unidades)
        # Marcar como processado apenas se foi bem-sucedido
        if resultado is not False:
            salvar_arquivo_processado(nome_arquivo)
        return resultado

    # Fallback para PDF
    if nome_arquivo.lower().endswith('.pdf'):
        print("  -> Processando PDF")
        resultado = processar_pdf_documento(dados_anexo, nome_arquivo, assunto, df_unidades, tipo_doc)
        # Marcar como processado apenas se foi bem-sucedido
        if resultado is not False:
            salvar_arquivo_processado(nome_arquivo)
        return resultado

    print(f"  -> Tipo de arquivo não suportado: {nome_arquivo}")
    salvar_arquivo(dados_anexo, "INCONSISTENCIAS", "", nome_arquivo)

def processar_xml_nfe(dados_xml, nome_arquivo, assunto, df_unidades):
    """Processa especificamente XMLs de NFe"""
    try:
        # Extrai dados do XML
        dados_gerais = extrair_dados_xml_nfe(dados_xml)
        itens_nfe = extrair_itens_xml_nfe(dados_xml)

        if not dados_gerais:
            print("    -> Falha ao extrair dados do XML. Movendo para inconsistências.")
            salvar_arquivo(dados_xml, "INCONSISTENCIAS", "", nome_arquivo)
            return

        # Identifica unidade pelo CNPJ do destinatário
        cnpj_destinatario = dados_gerais.get('CNPJ Destinatario', '')
        unidade = "INCONSISTENCIAS"
        cnpj_identificado = None

        if cnpj_destinatario and any(df_unidades['CNPJ_LIMPO'] == cnpj_destinatario):
            unidade = df_unidades.loc[df_unidades['CNPJ_LIMPO'] == cnpj_destinatario, 'UNIDADE'].iloc[0]
            cnpj_identificado = cnpj_destinatario
            print(f"  -> CNPJ destinatário {cnpj_destinatario} encontrado! Unidade: {unidade}")
        else:
            # Fallback: busca nome da unidade no assunto
            for _, row in df_unidades.iterrows():
                if row['UNIDADE'].replace(" ", "").lower() in assunto.replace(" ", "").lower():
                    unidade, cnpj_identificado = row['UNIDADE'], row['CNPJ_LIMPO']
                    print(f"  -> Nome da unidade '{unidade}' encontrado no assunto do e-mail!")
                    break

        if unidade == "INCONSISTENCIAS":
            print("  -> Não foi possível determinar a unidade.")

        # Salva o arquivo XML
        caminho_salvo = salvar_arquivo(dados_xml, unidade, 'Notas Fiscais', nome_arquivo)

        if unidade != "INCONSISTENCIAS" and dados_gerais and itens_nfe:
            print(f"    -> Extração XML bem-sucedida! Itens encontrados: {len(itens_nfe)}")

            # Prepara dados para planilha de produtos (com impostos detalhados)
            dados_produtos = []
            for item in itens_nfe:
                produto = {
                    'Unidade': unidade,
                    'CNPJ Destinatario': cnpj_identificado,
                    'Data Emissao': dados_gerais.get('Data Emissao'),
                    'Numero Nota': dados_gerais.get('Numero Nota'),
                    'Fornecedor': dados_gerais.get('Fornecedor'),
                    'CNPJ Emitente': dados_gerais.get('CNPJ Emitente'),
                    'Cod. Produto': item.get('Cod. Produto'),
                    'Descricao Produto': item.get('Descricao'),
                    'NCM': item.get('NCM'),
                    'CFOP': item.get('CFOP'),
                    'Unidade Medida': item.get('Unidade'),
                    'Quantidade': item.get('Quantidade'),
                    'Valor Unitario': item.get('Valor Unitario'),
                    'Valor Total Item': item.get('Valor Total'),
                    'ICMS CST': item.get('ICMS CST'),
                    'ICMS Aliquota': item.get('ICMS Aliquota'),
                    'ICMS Valor': item.get('ICMS Valor'),
                    'PIS CST': item.get('PIS CST'),
                    'PIS Aliquota': item.get('PIS Aliquota'),
                    'PIS Valor': item.get('PIS Valor'),
                    'COFINS CST': item.get('COFINS CST'),
                    'COFINS Aliquota': item.get('COFINS Aliquota'),
                    'COFINS Valor': item.get('COFINS Valor'),
                    'Arquivo': os.path.basename(caminho_salvo)
                }
                dados_produtos.append(produto)

            atualizar_planilha(PLANILHA_PRODUTOS, dados_produtos)

            # Prepara dados para planilha de faturamento
            dados_faturamento = []
            parcelas = dados_gerais.get('Parcelas', [])
            if not parcelas:
                # Se não tem parcelas, cria uma entrada única
                dados_faturamento.append({
                    'Unidade': unidade,
                    'CNPJ Destinatario': cnpj_identificado,
                    'Data Emissao': dados_gerais.get('Data Emissao'),
                    'Numero Nota': dados_gerais.get('Numero Nota'),
                    'Fornecedor': dados_gerais.get('Fornecedor'),
                    'CNPJ Emitente': dados_gerais.get('CNPJ Emitente'),
                    'Valor Total NF': dados_gerais.get('Valor Total'),
                    'Valor Produtos': dados_gerais.get('Valor Produtos'),
                    'Arquivo': os.path.basename(caminho_salvo)
                })
            else:
                # Cria uma entrada para cada parcela
                for p in parcelas:
                    dados_faturamento.append({
                        'Unidade': unidade,
                        'CNPJ Destinatario': cnpj_identificado,
                        'Data Emissao': dados_gerais.get('Data Emissao'),
                        'Numero Nota': dados_gerais.get('Numero Nota'),
                        'Fornecedor': dados_gerais.get('Fornecedor'),
                        'CNPJ Emitente': dados_gerais.get('CNPJ Emitente'),
                        'Num Parcela': p.get('Num Parcela'),
                        'Data Vencimento': p.get('Data Vencimento'),
                        'Valor Parcela': p.get('Valor Parcela'),
                        'Valor Total NF': dados_gerais.get('Valor Total'),
                        'Valor Produtos': dados_gerais.get('Valor Produtos'),
                        'Arquivo': os.path.basename(caminho_salvo)
                    })

            atualizar_planilha(PLANILHA_FATURAMENTO_NF, dados_faturamento)

        else:
            print("   -> ATENÇÃO: Falha na extração de dados ou itens do XML.")

    except Exception as e:
        print(f"    -> Erro ao processar XML: {e}")
        salvar_arquivo(dados_xml, "INCONSISTENCIAS", "", nome_arquivo)

def processar_pdf_documento(dados_pdf, nome_arquivo, assunto, df_unidades, tipo_doc):
    """Processa documentos PDF (fallback quando não há XML)"""
    pdf_obj = None
    try:
        pdf_obj = pdfplumber.open(io.BytesIO(dados_pdf))
        texto_pdf = "\n".join(page.extract_text(x_tolerance=1, y_tolerance=1) or "" for page in pdf_obj.pages)
    except Exception as e:
        print(f"    -> Erro grave ao ler PDF: {e}. Movendo para inconsistências.")
        salvar_arquivo(dados_pdf, "INCONSISTENCIAS", "", nome_arquivo)
        if pdf_obj: pdf_obj.close()
        return

    unidade, cnpj_identificado = "INCONSISTENCIAS", None

    # Lógica Hierárquica para encontrar a Unidade
    cnpj_dest = extrair_cnpj_destinatario(texto_pdf)
    if cnpj_dest and any(df_unidades['CNPJ_LIMPO'] == cnpj_dest):
        unidade = df_unidades.loc[df_unidades['CNPJ_LIMPO'] == cnpj_dest, 'UNIDADE'].iloc[0]
        cnpj_identificado = cnpj_dest
        print(f"  -> CNPJ do destinatário {cnpj_dest} encontrado! Unidade: {unidade}")
    else:
        todos_cnpjs = extrair_todos_cnpjs(texto_pdf)
        for cnpj in todos_cnpjs:
            if any(df_unidades['CNPJ_LIMPO'] == cnpj):
                unidade = df_unidades.loc[df_unidades['CNPJ_LIMPO'] == cnpj, 'UNIDADE'].iloc[0]
                cnpj_identificado = cnpj
                print(f"  -> CNPJ genérico {cnpj} encontrado no PDF! Unidade: {unidade}")
                break
        if unidade == "INCONSISTENCIAS":
            for _, row in df_unidades.iterrows():
                if row['UNIDADE'].replace(" ", "").lower() in assunto.replace(" ", "").lower():
                    unidade, cnpj_identificado = row['UNIDADE'], row['CNPJ_LIMPO']
                    print(f"  -> Nome da unidade '{unidade}' encontrado no assunto do e-mail!")
                    break

    if unidade == "INCONSISTENCIAS": print("  -> Não foi possível determinar a unidade.")

    caminho_salvo = salvar_arquivo(dados_pdf, unidade, tipo_doc, nome_arquivo)

    if unidade != "INCONSISTENCIAS":
        if tipo_doc == 'Notas Fiscais':
            dados_gerais = extrair_dados_nfe(texto_pdf)
            itens_nfe = extrair_itens_nfe(pdf_obj)

            if dados_gerais and itens_nfe:
                print(f"    -> Extração de dados e itens da NFe bem-sucedida! Itens encontrados: {len(itens_nfe)}")
                # Prepara dados para a planilha de produtos
                dados_produtos = [{'Unidade': unidade, 'CNPJ Destinatario': cnpj_identificado, 'Data Emissao': dados_gerais.get('Data Emissao'), 'Numero Nota': dados_gerais.get('Numero Nota'),'Fornecedor': dados_gerais.get('Fornecedor'), 'Cod. Produto': item.get('Cod. Produto'),'Descricao Produto': item.get('Descricao'), 'Quantidade': item.get('Quantidade'),'Valor Unitario': item.get('Valor Unitario'), 'Valor Total Item': item.get('Valor Total'),'Arquivo': os.path.basename(caminho_salvo)} for item in itens_nfe]
                atualizar_planilha(PLANILHA_PRODUTOS, dados_produtos)

                # Prepara dados para a planilha de faturamento
                dados_faturamento = []
                parcelas = dados_gerais.get('Parcelas', [])
                if not parcelas:
                     dados_faturamento.append({'Unidade': unidade, 'CNPJ Destinatario': cnpj_identificado, 'Data Emissao': dados_gerais.get('Data Emissao'), 'Numero Nota': dados_gerais.get('Numero Nota'),'Fornecedor': dados_gerais.get('Fornecedor'),'Arquivo': os.path.basename(caminho_salvo)})
                else:
                    for p in parcelas:
                        dados_faturamento.append({'Unidade': unidade, 'CNPJ Destinatario': cnpj_identificado, 'Data Emissao': dados_gerais.get('Data Emissao'), 'Numero Nota': dados_gerais.get('Numero Nota'),'Fornecedor': dados_gerais.get('Fornecedor'), 'Num Parcela': p.get('Num Parcela'), 'Data Vencimento': p.get('Data Vencimento'), 'Valor Parcela': p.get('Valor Parcela'), 'Arquivo': os.path.basename(caminho_salvo)})
                atualizar_planilha(PLANILHA_FATURAMENTO_NF, dados_faturamento)

            else:
                 print("   -> ATENÇÃO: Falha na extração de dados gerais ou itens da NFe. O arquivo foi salvo, mas verifique-o.")
        elif tipo_doc == 'Boletos':
            dados_boleto = extrair_dados_boleto(texto_pdf) or {}
            dados_planilha = [{'Unidade': unidade, 'CNPJ Encontrado': cnpj_identificado, 'Data Vencimento': dados_boleto.get('Vencimento'), 'Valor Documento': dados_boleto.get('Valor'), 'Tipo Documento': tipo_doc, 'Data Processamento': datetime.now().strftime("%d/%m/%Y %H:%M:%S"), 'Arquivo': os.path.basename(caminho_salvo)}]
            atualizar_planilha(PLANILHA_BOLETOS, dados_planilha)
        elif tipo_doc == 'Impostos':
            dados_planilha = [{'Unidade': unidade, 'CNPJ Encontrado': cnpj_identificado, 'Tipo Documento': tipo_doc, 'Data Processamento': datetime.now().strftime("%d/%m/%Y %H:%M:%S"), 'Arquivo': os.path.basename(caminho_salvo)}]
            atualizar_planilha(PLANILHA_IMPOSTOS, dados_planilha)

    if pdf_obj: pdf_obj.close()


# --- BLOCO PRINCIPAL ---
if __name__ == '__main__':
    import time
    print("Iniciando automação...")
    os.makedirs(PASTA_INCONSISTENCIAS, exist_ok=True)
    df_unidades = carregar_unidades(ARQUIVO_UNIDADES_CNPJ)
    if df_unidades is None: exit()

    servico_gmail = autenticar_gmail()
    if servico_gmail:
        # Perguntar ao usuário se quer reprocessar histórico
        print("\n🔄 OPÇÕES DE PROCESSAMENTO:")
        print("1. Processar apenas e-mails NÃO LIDOS (padrão)")
        print("2. Reprocessar ÚLTIMOS 5000 e-mails (incluindo lidos)")

        opcao = input("\nEscolha uma opção (1 ou 2): ").strip()

        if opcao == "2":
            print("\n🔍 MODO REPROCESSAMENTO ATIVADO!")
            print("⚠️  ATENÇÃO: Isso irá reprocessar os últimos 5000 e-mails!")
            confirmacao = input("Tem certeza? (S/N): ").strip().upper()

            if confirmacao == "S":
                emails = buscar_emails(servico_gmail, incluir_lidos=True, max_results=5000)
            else:
                print("Operação cancelada. Processando apenas e-mails não lidos.")
                emails = buscar_emails(servico_gmail, incluir_lidos=False, max_results=100)
        else:
            emails = buscar_emails(servico_gmail, incluir_lidos=False, max_results=100)

        if not emails:
            print("Nenhum e-mail encontrado.")
        else:
            total_emails = len(emails)
            emails_processados = 0
            emails_com_erro = 0
            emails_sem_anexo = 0

            print(f"Encontrados {total_emails} e-mails para analisar.")
            print("🚀 INICIANDO PROCESSAMENTO COMPLETO...")

            for i, email_info in enumerate(emails, 1):
                msg_id = email_info['id']
                try:
                    print(f"\n[{i}/{total_emails}] Processando e-mail ID: {msg_id} | Progresso: {(i/total_emails)*100:.1f}%")

                    message = servico_gmail.users().messages().get(userId='me', id=msg_id, format='full').execute()
                    payload = message.get('payload', {})
                    headers = payload.get('headers', [])
                    assunto = next((h['value'] for h in headers if h['name'].lower() == 'subject'), '')

                    print(f"  Assunto: '{assunto}'")

                    parts_a_verificar = [payload]
                    anexos_encontrados_neste_email = False

                    while parts_a_verificar:
                        part = parts_a_verificar.pop(0)
                        if 'parts' in part:
                            parts_a_verificar.extend(part.get('parts', []))

                        filename = part.get('filename', '').lower()
                        # Processa XMLs e PDFs
                        if filename and (filename.endswith('.xml') or filename.endswith('.pdf')):
                            anexos_encontrados_neste_email = True
                            tipo_arquivo = "XML" if filename.endswith('.xml') else "PDF"
                            print(f"  -> Anexo {tipo_arquivo} encontrado: {part.get('filename')}")

                            try:
                                dados_anexo = baixar_anexo_temporario(servico_gmail, msg_id, part)
                                if dados_anexo:
                                    # Detectar se é modo reprocessamento baseado na opção escolhida
                                    modo_reprocessamento = opcao == "2" if 'opcao' in locals() else False
                                    processar_anexo(dados_anexo, part.get('filename'), assunto, df_unidades, modo_reprocessamento)
                            except Exception as e:
                                print(f"    -> ERRO ao processar anexo {part.get('filename')}: {e}")
                                continue

                    if not anexos_encontrados_neste_email:
                        print("  -> Nenhum anexo XML/PDF encontrado neste e-mail.")
                        emails_sem_anexo += 1
                    else:
                        emails_processados += 1

                    marcar_email_como_processado(servico_gmail, msg_id)

                    # Pequena pausa para evitar rate limiting
                    time.sleep(0.3)

                except Exception as e:
                    emails_com_erro += 1
                    print(f"❌ ERRO GERAL ao processar e-mail ID {msg_id}: {e}")
                    print(f"   Continuando com próximo e-mail...")
                    continue

            # Estatísticas finais
            print(f"\n🎉 AUTOMAÇÃO CONCLUÍDA!")
            print(f"📊 ESTATÍSTICAS FINAIS:")
            print(f"   ✅ E-mails processados com sucesso: {emails_processados}")
            print(f"   📎 E-mails sem anexos relevantes: {emails_sem_anexo}")
            print(f"   ❌ E-mails com erro: {emails_com_erro}")
            print(f"   📧 Total de e-mails analisados: {total_emails}")
            print(f"   📈 Taxa de sucesso: {((emails_processados + emails_sem_anexo)/total_emails)*100:.1f}%")

    print("\n🏁 FIM DA EXECUÇÃO.")