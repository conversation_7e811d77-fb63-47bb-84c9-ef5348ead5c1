r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Taskrouter
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, Optional, Union
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class WorkflowRealTimeStatisticsInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Workflow resource.
    :ivar longest_task_waiting_age: The age of the longest waiting Task.
    :ivar longest_task_waiting_sid: The SID of the longest waiting Task.
    :ivar tasks_by_priority: The number of Tasks by priority. For example: `{\"0\": \"10\", \"99\": \"5\"}` shows 10 Tasks at priority 0 and 5 at priority 99.
    :ivar tasks_by_status: The number of Tasks by their current status. For example: `{\"pending\": \"1\", \"reserved\": \"3\", \"assigned\": \"2\", \"completed\": \"5\"}`.
    :ivar total_tasks: The total number of Tasks.
    :ivar workflow_sid: Returns the list of Tasks that are being controlled by the Workflow with the specified SID value.
    :ivar workspace_sid: The SID of the Workspace that contains the Workflow.
    :ivar url: The absolute URL of the Workflow statistics resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        workspace_sid: str,
        workflow_sid: str,
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.longest_task_waiting_age: Optional[int] = deserialize.integer(
            payload.get("longest_task_waiting_age")
        )
        self.longest_task_waiting_sid: Optional[str] = payload.get(
            "longest_task_waiting_sid"
        )
        self.tasks_by_priority: Optional[Dict[str, object]] = payload.get(
            "tasks_by_priority"
        )
        self.tasks_by_status: Optional[Dict[str, object]] = payload.get(
            "tasks_by_status"
        )
        self.total_tasks: Optional[int] = deserialize.integer(
            payload.get("total_tasks")
        )
        self.workflow_sid: Optional[str] = payload.get("workflow_sid")
        self.workspace_sid: Optional[str] = payload.get("workspace_sid")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "workspace_sid": workspace_sid,
            "workflow_sid": workflow_sid,
        }
        self._context: Optional[WorkflowRealTimeStatisticsContext] = None

    @property
    def _proxy(self) -> "WorkflowRealTimeStatisticsContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: WorkflowRealTimeStatisticsContext for this WorkflowRealTimeStatisticsInstance
        """
        if self._context is None:
            self._context = WorkflowRealTimeStatisticsContext(
                self._version,
                workspace_sid=self._solution["workspace_sid"],
                workflow_sid=self._solution["workflow_sid"],
            )
        return self._context

    def fetch(
        self, task_channel: Union[str, object] = values.unset
    ) -> "WorkflowRealTimeStatisticsInstance":
        """
        Fetch the WorkflowRealTimeStatisticsInstance

        :param task_channel: Only calculate real-time statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkflowRealTimeStatisticsInstance
        """
        return self._proxy.fetch(
            task_channel=task_channel,
        )

    async def fetch_async(
        self, task_channel: Union[str, object] = values.unset
    ) -> "WorkflowRealTimeStatisticsInstance":
        """
        Asynchronous coroutine to fetch the WorkflowRealTimeStatisticsInstance

        :param task_channel: Only calculate real-time statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkflowRealTimeStatisticsInstance
        """
        return await self._proxy.fetch_async(
            task_channel=task_channel,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkflowRealTimeStatisticsInstance {}>".format(
            context
        )


class WorkflowRealTimeStatisticsContext(InstanceContext):

    def __init__(self, version: Version, workspace_sid: str, workflow_sid: str):
        """
        Initialize the WorkflowRealTimeStatisticsContext

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the Workflow to fetch.
        :param workflow_sid: Returns the list of Tasks that are being controlled by the Workflow with the specified SID value.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
            "workflow_sid": workflow_sid,
        }
        self._uri = "/Workspaces/{workspace_sid}/Workflows/{workflow_sid}/RealTimeStatistics".format(
            **self._solution
        )

    def fetch(
        self, task_channel: Union[str, object] = values.unset
    ) -> WorkflowRealTimeStatisticsInstance:
        """
        Fetch the WorkflowRealTimeStatisticsInstance

        :param task_channel: Only calculate real-time statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkflowRealTimeStatisticsInstance
        """

        data = values.of(
            {
                "TaskChannel": task_channel,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return WorkflowRealTimeStatisticsInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            workflow_sid=self._solution["workflow_sid"],
        )

    async def fetch_async(
        self, task_channel: Union[str, object] = values.unset
    ) -> WorkflowRealTimeStatisticsInstance:
        """
        Asynchronous coroutine to fetch the WorkflowRealTimeStatisticsInstance

        :param task_channel: Only calculate real-time statistics on this TaskChannel. Can be the TaskChannel's SID or its `unique_name`, such as `voice`, `sms`, or `default`.

        :returns: The fetched WorkflowRealTimeStatisticsInstance
        """

        data = values.of(
            {
                "TaskChannel": task_channel,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return WorkflowRealTimeStatisticsInstance(
            self._version,
            payload,
            workspace_sid=self._solution["workspace_sid"],
            workflow_sid=self._solution["workflow_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Taskrouter.V1.WorkflowRealTimeStatisticsContext {}>".format(
            context
        )


class WorkflowRealTimeStatisticsList(ListResource):

    def __init__(self, version: Version, workspace_sid: str, workflow_sid: str):
        """
        Initialize the WorkflowRealTimeStatisticsList

        :param version: Version that contains the resource
        :param workspace_sid: The SID of the Workspace with the Workflow to fetch.
        :param workflow_sid: Returns the list of Tasks that are being controlled by the Workflow with the specified SID value.

        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "workspace_sid": workspace_sid,
            "workflow_sid": workflow_sid,
        }

    def get(self) -> WorkflowRealTimeStatisticsContext:
        """
        Constructs a WorkflowRealTimeStatisticsContext

        """
        return WorkflowRealTimeStatisticsContext(
            self._version,
            workspace_sid=self._solution["workspace_sid"],
            workflow_sid=self._solution["workflow_sid"],
        )

    def __call__(self) -> WorkflowRealTimeStatisticsContext:
        """
        Constructs a WorkflowRealTimeStatisticsContext

        """
        return WorkflowRealTimeStatisticsContext(
            self._version,
            workspace_sid=self._solution["workspace_sid"],
            workflow_sid=self._solution["workflow_sid"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Taskrouter.V1.WorkflowRealTimeStatisticsList>"
