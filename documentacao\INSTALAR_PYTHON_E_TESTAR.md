# 🐍 INSTALAR PYTHON E TESTAR AS MELHORIAS

## ⚠️ PROBLEMA IDENTIFICADO
O Python não está instalado no seu sistema Windows.

## 🚀 SOLUÇÃO RÁPIDA - INSTALAR PYTHON

### **Método 1: Microsoft Store (MAIS FÁCIL)**
1. **Abrir Microsoft Store**
   - Pressionar `Windows + S`
   - Digitar "Microsoft Store"
   - Abrir a loja

2. **Buscar Python**
   - Na barra de busca, digitar: `Python 3.11`
   - Clicar no resultado "Python 3.11"

3. **Instalar**
   - Clicar em "Instalar" ou "Obter"
   - Aguardar download e instalação

### **Método 2: Site Oficial**
1. **Ir para**: https://www.python.org/downloads/
2. **Baixar**: Python 3.11+ para Windows
3. **Executar instalador**
4. **⚠️ IMPORTANTE**: Marcar "Add Python to PATH"
5. **Clicar**: "Install Now"

## ✅ VERIFICAR INSTALAÇÃO

Após instalar, abrir **PowerShell** ou **Prompt de Comando** e testar:

```bash
python --version
```

**Deve retornar algo como**: `Python 3.11.x`

## 📦 INSTALAR DEPENDÊNCIAS

```bash
# Navegar para a pasta do projeto
cd "C:\Users\<USER>\OneDrive\Área de Trabalho\GMAIL AUTOMATIZACAO BAIXAR NFs"

# Instalar todas as dependências
pip install -r requirements.txt
```

## 🧪 TESTAR AS MELHORIAS IMPLEMENTADAS

### **1. Teste de Sintaxe**
```bash
python TESTE_SINTAXE.py
```

**Resultado esperado:**
```
🧪 TESTE DE SINTAXE E FUNCIONALIDADES
==================================================
1. Testando importações...
✅ os - OK
✅ pickle - OK
✅ base64 - OK
✅ re - OK
✅ io - OK
✅ xml.etree.ElementTree - OK
✅ datetime - OK
✅ pdfplumber - OK
✅ pandas - OK
✅ Google APIs - OK
✅ traceback - OK

2. Verificando arquivos...
✅ automacao_nf.py
✅ unidades_cnpjs.csv
✅ requirements.txt
✅ config.py
✅ credentials.json

3. Testando palavras-chave...
Palavras-chave para Notas Fiscais:
  - 'nota fiscal'
  - 'notas fiscais'
  - 'nfe'
  - 'nf-e'
  - 'nf'
  - 'danfe'
  - 'xml'

4. Testando processamento XML...
✅ Número da NFe extraído: 46
✅ Emitente extraído: EMPRESA EMITENTE LTDA
✅ CNPJ destinatário extraído: 37053499000105
✅ 1 produto(s) encontrado(s)
  - Produto: PRODUTO TESTE
✅ Processamento XML funcionando corretamente!

==================================================
✅ TODOS OS TESTES CONCLUÍDOS!
```

### **2. Executar Sistema Principal**
```bash
python automacao_nf.py
```

**Resultado esperado:**
```
Iniciando automação...
Tabela de unidades carregada com sucesso.
Autenticação com Gmail bem-sucedida!
Buscando e-mails com a query: is:unread has:attachment (subject:("nota fiscal") OR subject:("notas fiscais") OR subject:("nfe") OR subject:("nf-e") OR subject:("nf") OR subject:("danfe") OR subject:("xml") OR ...)
Encontrados X e-mails para analisar.

[+] Processando e-mail ID: 123456 | Assunto: 'NFe 12345 - Fornecedor ABC'
  -> Anexo XML encontrado: nfe_12345.xml
  -> Classificado como: Notas Fiscais
  -> Processando XML da NFe (método preferencial)
  -> CNPJ destinatário 37053499000105 encontrado! Unidade: ARAXA
  -> Arquivo salvo em: ARAXA\2025\01\Notas Fiscais\nfe_12345.xml
    -> Extração XML bem-sucedida! Itens encontrados: 8
  -> Planilha 'controle_produtos.xlsx' atualizada com sucesso.
  -> Planilha 'controle_faturamento_geral.xlsx' atualizada com sucesso.
  -> E-mail ID 123456 marcado como processado.

Fim da execução.
```

## 🔍 O QUE OBSERVAR NOS LOGS

### **✅ Logs de Sucesso (XML):**
```
"-> Processando XML da NFe (método preferencial)"
"-> Extração XML bem-sucedida! Itens encontrados: X"
"-> CNPJ destinatário XXXXXX encontrado! Unidade: XXXXX"
```

### **⚠️ Logs de Fallback (PDF):**
```
"-> Processando PDF"
"-> Extração de dados e itens da NFe bem-sucedida!"
```

### **❌ Logs de Problema:**
```
"-> Não foi possível determinar a unidade"
"-> Falha ao extrair dados do XML"
"Nenhum e-mail novo encontrado"
```

## 📊 VERIFICAR PLANILHAS GERADAS

### **controle_produtos.xlsx**
**Novas colunas adicionadas:**
- CNPJ Emitente
- NCM
- CFOP
- Unidade Medida
- ICMS CST, ICMS Aliquota, ICMS Valor
- PIS CST, PIS Aliquota, PIS Valor
- COFINS CST, COFINS Aliquota, COFINS Valor

### **controle_faturamento_geral.xlsx**
**Novas colunas adicionadas:**
- CNPJ Emitente
- Valor Total NF
- Valor Produtos
- Dados de parcelas mais detalhados

## 🎯 MELHORIAS QUE VOCÊ VAI NOTAR

### **1. Busca Expandida**
- Agora captura e-mails com "NF", "XML", "Notas Fiscais"
- Muito mais e-mails serão processados

### **2. Processamento XML**
- XMLs de NFe são processados com prioridade
- Dados 100% precisos extraídos
- Impostos detalhados (ICMS, PIS, COFINS)

### **3. Dados Mais Ricos**
- NCM e CFOP dos produtos
- CNPJ do emitente
- Informações fiscais completas

### **4. Organização Inteligente**
- XMLs e PDFs organizados por unidade
- Estrutura hierárquica mantida

## 🐛 PROBLEMAS COMUNS E SOLUÇÕES

### **"Python não foi encontrado"**
```
Solução: Instalar Python conforme instruções acima
```

### **"ModuleNotFoundError"**
```
Solução: pip install -r requirements.txt
```

### **"Nenhum e-mail novo encontrado"**
```
Verificar:
1. Há e-mails não lidos no Gmail?
2. E-mails têm anexos?
3. Assuntos contêm as palavras-chave?
```

### **"Erro ao autenticar Gmail"**
```
Verificar:
1. credentials.json está presente?
2. Internet funcionando?
3. Primeira execução abrirá navegador
```

## 📋 CHECKLIST FINAL

- [ ] Python instalado (`python --version`)
- [ ] Dependências instaladas (`pip install -r requirements.txt`)
- [ ] Teste de sintaxe passou (`python TESTE_SINTAXE.py`)
- [ ] Sistema executou sem erros (`python automacao_nf.py`)
- [ ] Planilhas foram atualizadas com novos dados
- [ ] XMLs são processados com prioridade
- [ ] Busca expandida funcionando

## 🎉 RESULTADO FINAL

Após seguir estes passos, você terá:

✅ **Sistema funcionando** com Python instalado  
✅ **Processamento XML** prioritário para NFe  
✅ **Busca expandida** capturando mais e-mails  
✅ **Dados fiscais completos** nas planilhas  
✅ **Impostos detalhados** (ICMS, PIS, COFINS)  
✅ **Organização inteligente** por unidade  

---

**🚀 AGORA É SÓ INSTALAR O PYTHON E TESTAR!**
