<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏢 Dashboard Financeiro - Sistema de Inteligência</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }
        
        .dashboard-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            font-weight: bold;
            padding: 15px 20px;
        }
        
        .metric-card {
            text-align: center;
            padding: 20px;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .alert-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-offline { background-color: #dc3545; }
        
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 40px rgba(0,0,0,0.3);
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        @media (max-width: 768px) {
            .dashboard-container {
                margin: 10px;
                padding: 15px;
            }
            
            .metric-value {
                font-size: 1.8rem;
            }
            
            .chart-container {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center mb-3">
                    <i class="fas fa-chart-line text-primary"></i>
                    Dashboard Financeiro
                </h1>
                <div class="text-center">
                    <span class="status-indicator status-online"></span>
                    <small class="text-muted">Sistema Online - Última atualização: <span id="lastUpdate">Carregando...</span></small>
                </div>
            </div>
        </div>

        <!-- Métricas Principais -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card">
                    <div class="metric-value" id="totalNFs">-</div>
                    <div class="metric-label">
                        <i class="fas fa-file-invoice"></i> Total de NFes
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card">
                    <div class="metric-value" id="valorTotal">-</div>
                    <div class="metric-label">
                        <i class="fas fa-dollar-sign"></i> Valor Total
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card">
                    <div class="metric-value" id="unidadesAtivas">-</div>
                    <div class="metric-label">
                        <i class="fas fa-building"></i> Unidades Ativas
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card position-relative">
                    <div class="metric-value" id="alertasTotal">-</div>
                    <div class="metric-label">
                        <i class="fas fa-exclamation-triangle"></i> Alertas Ativos
                    </div>
                    <div class="alert-badge" id="alertasBadge" style="display: none;">!</div>
                </div>
            </div>
        </div>

        <!-- Gráficos -->
        <div class="row">
            <!-- Fluxo Mensal -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-chart-line"></i> Fluxo Mensal
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="fluxoMensalChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Unidades -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-building"></i> Top Unidades
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="topUnidadesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Fornecedores -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-industry"></i> Top Fornecedores
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="topFornecedoresChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Vencimentos -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-calendar-alt"></i> Vencimentos Próximos
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="vencimentosChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Botão de Refresh -->
    <button class="refresh-btn" onclick="refreshDashboard()" title="Atualizar Dashboard">
        <i class="fas fa-sync-alt" id="refreshIcon"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Variáveis globais para os gráficos
        let fluxoChart, unidadesChart, fornecedoresChart, vencimentosChart;
        
        // Configuração padrão dos gráficos
        Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
        Chart.defaults.color = '#6c757d';
        
        // Função para formatar valores em Real
        function formatarReal(valor) {
            return new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
            }).format(valor);
        }
        
        // Função para formatar números
        function formatarNumero(numero) {
            return new Intl.NumberFormat('pt-BR').format(numero);
        }
        
        // Carregar dados do resumo
        async function carregarResumo() {
            try {
                const response = await fetch('/api/resumo');
                const data = await response.json();
                
                document.getElementById('totalNFs').textContent = formatarNumero(data.total_nfs || 0);
                document.getElementById('valorTotal').textContent = data.valor_total_formatado || 'R$ 0,00';
                document.getElementById('unidadesAtivas').textContent = formatarNumero(data.unidades_ativas || 0);
                
            } catch (error) {
                console.error('Erro ao carregar resumo:', error);
            }
        }
        
        // Carregar alertas
        async function carregarAlertas() {
            try {
                const response = await fetch('/api/alertas');
                const data = await response.json();
                
                document.getElementById('alertasTotal').textContent = formatarNumero(data.total || 0);
                
                if (data.total > 0) {
                    document.getElementById('alertasBadge').style.display = 'flex';
                    document.getElementById('alertasBadge').textContent = data.alta || 0;
                } else {
                    document.getElementById('alertasBadge').style.display = 'none';
                }
                
            } catch (error) {
                console.error('Erro ao carregar alertas:', error);
            }
        }
        
        // Criar gráfico de fluxo mensal
        async function criarFluxoMensal() {
            try {
                const response = await fetch('/api/fluxo-mensal');
                const data = await response.json();
                
                const ctx = document.getElementById('fluxoMensalChart').getContext('2d');
                
                if (fluxoChart) {
                    fluxoChart.destroy();
                }
                
                fluxoChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.labels || [],
                        datasets: [{
                            label: 'Valor Mensal',
                            data: data.data || [],
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return formatarReal(value);
                                    }
                                }
                            }
                        }
                    }
                });
                
            } catch (error) {
                console.error('Erro ao criar gráfico de fluxo mensal:', error);
            }
        }
        
        // Criar gráfico de top unidades
        async function criarTopUnidades() {
            try {
                const response = await fetch('/api/top-unidades?limit=8');
                const data = await response.json();
                
                const ctx = document.getElementById('topUnidadesChart').getContext('2d');
                
                if (unidadesChart) {
                    unidadesChart.destroy();
                }
                
                unidadesChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: data.labels || [],
                        datasets: [{
                            label: 'Valor Total',
                            data: data.data || [],
                            backgroundColor: [
                                '#667eea', '#764ba2', '#f093fb', '#f5576c',
                                '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
                            ],
                            borderRadius: 8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return formatarReal(value);
                                    }
                                }
                            },
                            x: {
                                ticks: {
                                    maxRotation: 45
                                }
                            }
                        }
                    }
                });
                
            } catch (error) {
                console.error('Erro ao criar gráfico de top unidades:', error);
            }
        }
        
        // Criar gráfico de top fornecedores
        async function criarTopFornecedores() {
            try {
                const response = await fetch('/api/top-fornecedores?limit=6');
                const data = await response.json();
                
                const ctx = document.getElementById('topFornecedoresChart').getContext('2d');
                
                if (fornecedoresChart) {
                    fornecedoresChart.destroy();
                }
                
                fornecedoresChart = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.labels || [],
                        datasets: [{
                            data: data.data || [],
                            backgroundColor: [
                                '#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'
                            ],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
                
            } catch (error) {
                console.error('Erro ao criar gráfico de top fornecedores:', error);
            }
        }
        
        // Criar gráfico de vencimentos
        async function criarVencimentos() {
            try {
                const response = await fetch('/api/vencimentos');
                const data = await response.json();
                
                const ctx = document.getElementById('vencimentosChart').getContext('2d');
                
                if (vencimentosChart) {
                    vencimentosChart.destroy();
                }
                
                vencimentosChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: ['7 dias', '15 dias', '30 dias'],
                        datasets: [{
                            label: 'Quantidade',
                            data: [data['7_dias'] || 0, data['15_dias'] || 0, data['30_dias'] || 0],
                            backgroundColor: ['#dc3545', '#ffc107', '#28a745'],
                            borderRadius: 8
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
                
            } catch (error) {
                console.error('Erro ao criar gráfico de vencimentos:', error);
            }
        }
        
        // Função para refresh do dashboard
        async function refreshDashboard() {
            const refreshIcon = document.getElementById('refreshIcon');
            const container = document.querySelector('.dashboard-container');
            
            // Animação de loading
            refreshIcon.classList.add('fa-spin');
            container.classList.add('loading');
            
            try {
                // Recarregar dados no backend
                await fetch('/api/refresh');
                
                // Recarregar todos os componentes
                await Promise.all([
                    carregarResumo(),
                    carregarAlertas(),
                    criarFluxoMensal(),
                    criarTopUnidades(),
                    criarTopFornecedores(),
                    criarVencimentos()
                ]);
                
                // Atualizar timestamp
                document.getElementById('lastUpdate').textContent = new Date().toLocaleString('pt-BR');
                
            } catch (error) {
                console.error('Erro ao atualizar dashboard:', error);
            } finally {
                // Remover animação de loading
                refreshIcon.classList.remove('fa-spin');
                container.classList.remove('loading');
            }
        }
        
        // Inicializar dashboard
        async function inicializarDashboard() {
            await refreshDashboard();
            
            // Auto-refresh a cada 30 segundos
            setInterval(refreshDashboard, 30000);
        }
        
        // Inicializar quando a página carregar
        document.addEventListener('DOMContentLoaded', inicializarDashboard);
    </script>
</body>
</html>
