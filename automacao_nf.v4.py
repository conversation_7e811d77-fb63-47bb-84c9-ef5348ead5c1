import os
import pickle
import base64
import re
import io
from datetime import datetime
import pdfplumber
import pandas as pd
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import traceback

# --- CONFIGURAÇÕES GLOBAIS ---
SCOPES = ['https://www.googleapis.com/auth/gmail.modify']
CAMINHO_CREDENCIAIS = 'credentials.json'
ARQUIVO_TOKEN = 'token.pickle'
PASTA_BASE = os.getcwd()
PASTA_INCONSISTENCIAS = os.path.join(PASTA_BASE, 'INCONSISTENCIAS')
ARQUIVO_UNIDADES_CNPJ = 'unidades_cnpjs.csv'

# Planilhas de controle
PLANILHA_NF = 'controle_notas_fiscais.xlsx'
PLANILHA_BOLETOS = 'controle_boletos.xlsx'
PLANILHA_IMPOSTOS = 'controle_impostos.xlsx'

# Palavras-chave para classificação
KW_NOTAS_FISCAIS = ['nota fiscal', 'nfe', 'nf-e', 'danfe']
KW_BOLETOS = ['boleto', 'fatura', 'cobrança', 'duplicata']
KW_IMPOSTOS = ['pis', 'cofins', 'irpj', 'csll', 'iss', 'das', 'darf', 'guia', 'recolhimento']

# --- FUNÇÕES GMAIL (sem alterações) ---
def autenticar_gmail():
    creds = None
    if os.path.exists(ARQUIVO_TOKEN):
        with open(ARQUIVO_TOKEN, 'rb') as token:
            creds = pickle.load(token)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            if os.path.exists(ARQUIVO_TOKEN): os.remove(ARQUIVO_TOKEN)
            try:
                flow = InstalledAppFlow.from_client_secrets_file(CAMINHO_CREDENCIAIS, SCOPES)
                creds = flow.run_local_server(port=0)
            except FileNotFoundError:
                print(f"ERRO: Arquivo '{CAMINHO_CREDENCIAIS}' não encontrado.")
                return None
        with open(ARQUIVO_TOKEN, 'wb') as token:
            pickle.dump(creds, token)
    try:
        service = build('gmail', 'v1', credentials=creds)
        print("Autenticação com Gmail bem-sucedida!")
        return service
    except HttpError as error:
        print(f'Erro ao construir o serviço do Gmail: {error}')
        return None

def buscar_emails(service, query="is:unread"):
    try:
        result = service.users().messages().list(userId='me', q=query).execute()
        return result.get('messages', [])
    except HttpError as error:
        print(f'Erro ao buscar e-mails: {error}')
        return []

def baixar_anexo_temporario(service, msg_id, part):
    try:
        attachment = service.users().messages().attachments().get(userId='me', messageId=msg_id, id=part['body']['attachmentId']).execute()
        return base64.urlsafe_b64decode(attachment['data'].encode('UTF-8'))
    except HttpError as error:
        print(f"  -> Erro ao baixar anexo: {error}")
        return None

def marcar_email_como_processado(service, msg_id):
    try:
        service.users().messages().modify(userId='me', id=msg_id, body={'removeLabelIds': ['UNREAD']}).execute()
        print(f"  -> E-mail ID {msg_id} marcado como processado.")
    except HttpError as error:
        print(f"  -> Erro ao marcar e-mail como lido: {error}")

# --- FUNÇÕES DE LÓGICA E PROCESSAMENTO (AJUSTADAS) ---

def carregar_unidades(arquivo_csv):
    try:
        df = pd.read_csv(arquivo_csv, sep=';', dtype={'CNPJ': str})
        df['CNPJ_LIMPO'] = df['CNPJ'].str.replace(r'\D', '', regex=True)
        print("Tabela de unidades carregada com sucesso.")
        return df
    except FileNotFoundError:
        print(f"ERRO CRÍTICO: Arquivo de unidades '{arquivo_csv}' não encontrado.")
        return None

def classificar_documento(assunto, nome_arquivo):
    """Classifica o documento com base em palavras-chave (ORDEM AJUSTADA)."""
    texto_busca = f"{assunto.lower()} {nome_arquivo.lower()}"
    # 1. Verifica Boletos primeiro, pois é mais específico
    if any(kw in texto_busca for kw in KW_BOLETOS):
        return 'Boletos'
    # 2. Depois, verifica Notas Fiscais
    if any(kw in texto_busca for kw in KW_NOTAS_FISCAIS):
        return 'Notas Fiscais'
    # 3. Em seguida, Impostos
    if any(kw in texto_busca for kw in KW_IMPOSTOS):
        return 'Impostos'
    return 'Outros'

def extrair_cnpj_destinatario(texto_pdf):
    """Extrai o CNPJ especificamente do bloco do destinatário."""
    try:
        # Isola o bloco de texto do destinatário
        bloco_destinatario = re.search(r'DESTINAT[ÁA]RIO/REMETENTE\n(.*?)(?:FATURA|CÁLCULO DO IMPOSTO)', texto_pdf, re.DOTALL | re.IGNORECASE)
        if bloco_destinatario:
            texto_bloco = bloco_destinatario.group(1)
            # Procura pelo CNPJ dentro deste bloco isolado
            cnpj_match = re.search(r'(\d{2}\.\d{3}\.\d{3}/\d{4}-\d{2})', texto_bloco)
            if cnpj_match:
                return re.sub(r'\D', '', cnpj_match.group(1))
    except Exception:
        pass
    # Se falhar, tenta um método mais genérico como plano B
    cnpjs_encontrados = re.findall(r'(\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}|\d{14})', texto_pdf)
    return re.sub(r'\D', '', cnpjs_encontrados[0]) if cnpjs_encontrados else None


def extrair_dados_nfe(texto_pdf):
    """Extrai todos os dados detalhados de uma Nota Fiscal."""
    dados = {}
    try:
        dados['Numero Nota'] = (re.search(r'N[º°.]\s*(\d[\d. ]{5,})', texto_pdf) or re.search(r'DANFE\s*.*?(\d{3}\.\d{3}\.\d{3})', texto_pdf, re.DOTALL)).group(1).strip()
        dados['Data Emissao'] = (re.search(r'EMISSÃO\n([\d/]+)', texto_pdf) or re.search(r'Data de Emissão\n([\d/]+)', texto_pdf)).group(1)
        emitente_bloco = re.search(r'Identificação do emitente\n(.*?)\n', texto_pdf, re.DOTALL) or re.search(r'emitente\n(.*?)\n', texto_pdf, re.DOTALL)
        dados['Fornecedor'] = emitente_bloco.group(1).strip() if emitente_bloco else 'NÃO ENCONTRADO'
        dados['Desconto'] = (re.search(r'DESCONTO\n([\d.,]+)', texto_pdf, re.IGNORECASE) or re.search(r'DESCONTO\n\n([\d.,]+)', texto_pdf, re.IGNORECASE) or re.search(r'Desconto\n([\d.,]+)', texto_pdf)).group(1)
        dados['Valor Total'] = (re.search(r'VALOR TOTAL DA NOTA\n([\d.,]+)', texto_pdf, re.IGNORECASE) or re.search(r'VALOR TOTAL DA NOTA\n\n([\d.,]+)', texto_pdf, re.IGNORECASE)).group(1)
        
        parcelas = []
        faturas = re.findall(r'(\d{3})?\s*([\d]{2}\/[\d]{2}\/[\d]{4})\s+([\d.,]+)', texto_pdf)
        for fatura in faturas:
            parcelas.append({'Num Parcela': fatura[0] if fatura[0] else '', 'Data Vencimento': fatura[1], 'Valor Parcela': fatura[2]})
        dados['Parcelas'] = parcelas
        return dados
    except (AttributeError, IndexError):
        return None

def atualizar_planilha(nome_planilha, novos_dados_lista):
    """Carrega uma planilha existente ou cria uma nova, adiciona os novos dados e salva."""
    try:
        df_novo = pd.DataFrame(novos_dados_lista)
        if os.path.exists(nome_planilha):
            df_antigo = pd.read_excel(nome_planilha)
            df_final = pd.concat([df_antigo, df_novo], ignore_index=True)
        else:
            df_final = df_novo
        
        df_final.to_excel(nome_planilha, index=False, engine='openpyxl')
        print(f"  -> Planilha '{nome_planilha}' atualizada com sucesso.")
    except Exception as e:
        print(f"    -> ERRO ao atualizar a planilha '{nome_planilha}': {e}")

def salvar_arquivo(dados_pdf, unidade, tipo_doc, nome_original):
    """Salva o arquivo na estrutura de pastas correta."""
    agora = datetime.now()
    ano, mes = str(agora.year), f"{agora.month:02d}"
    nome_pasta_unidade = "".join(c for c in unidade if c.isalnum() or c in (' ', '-')).rstrip()
    
    if unidade == "INCONSISTENCIAS":
        caminho_final = PASTA_INCONSISTENCIAS
    else:
        caminho_final = os.path.join(PASTA_BASE, nome_pasta_unidade, ano, mes, tipo_doc)
    
    os.makedirs(caminho_final, exist_ok=True)
    caminho_arquivo = os.path.join(caminho_final, nome_original)
    with open(caminho_arquivo, 'wb') as f:
        f.write(dados_pdf)
    print(f"  -> Arquivo salvo em: {caminho_arquivo}")
    return caminho_arquivo

def processar_anexo(dados_pdf, nome_arquivo, assunto, df_unidades):
    """Função central que processa um único anexo."""
    tipo_doc = classificar_documento(assunto, nome_arquivo)
    if tipo_doc == 'Outros':
        print(f"  -> Documento '{nome_arquivo}' classificado como 'Outros'. Ignorando.")
        return

    print(f"  -> Classificado como: {tipo_doc}")
    texto_pdf = ''
    try:
        with pdfplumber.open(io.BytesIO(dados_pdf)) as pdf:
            texto_pdf = "\n".join(page.extract_text(x_tolerance=1, y_tolerance=1) or "" for page in pdf.pages)
    except Exception as e:
        print(f"    -> Erro grave ao ler PDF com pdfplumber: {e}. Movendo para inconsistências.")
        salvar_arquivo(dados_pdf, "INCONSISTENCIAS", "", nome_arquivo)
        return

    cnpj_limpo = extrair_cnpj_destinatario(texto_pdf) if tipo_doc == 'Notas Fiscais' else (re.sub(r'\D', '', re.findall(r'(\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}|\d{14})', texto_pdf)[0]) if re.findall(r'(\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}|\d{14})', texto_pdf) else None)
    
    unidade = "INCONSISTENCIAS"
    if cnpj_limpo:
        match = df_unidades[df_unidades['CNPJ_LIMPO'] == cnpj_limpo]
        if not match.empty:
            unidade = match.iloc[0]['UNIDADE']
            print(f"  -> CNPJ {cnpj_limpo} encontrado! Unidade: {unidade}")
        else:
            print(f"  -> CNPJ {cnpj_limpo} não corresponde a nenhuma unidade.")
    else:
        print("  -> Nenhum CNPJ conhecido encontrado no PDF.")

    caminho_salvo = salvar_arquivo(dados_pdf, unidade, tipo_doc, nome_arquivo)
    
    if unidade != "INCONSISTENCIAS":
        dados_planilha = []
        if tipo_doc == 'Notas Fiscais':
            dados_nfe = extrair_dados_nfe(texto_pdf)
            if dados_nfe:
                if not dados_nfe.get('Parcelas'):
                    dados_nfe['Parcelas'] = [{'Num Parcela': '1', 'Data Vencimento': dados_nfe.get('Data Emissao', ''), 'Valor Parcela': dados_nfe.get('Valor Total', '0')}]
                for parcela in dados_nfe['Parcelas']:
                    dados_planilha.append({'Unidade': unidade, 'CNPJ Destinatario': cnpj_limpo, 'Data Emissao': dados_nfe.get('Data Emissao'), 'Numero Nota': dados_nfe.get('Numero Nota'), 'Fornecedor': dados_nfe.get('Fornecedor'), 'Desconto': dados_nfe.get('Desconto'), 'Valor Total Nota': dados_nfe.get('Valor Total'), 'Num Parcela': parcela.get('Num Parcela'), 'Data Vencimento': parcela.get('Data Vencimento'), 'Valor Parcela': parcela.get('Valor Parcela'), 'Arquivo': os.path.basename(caminho_salvo)})
                atualizar_planilha(PLANILHA_NF, dados_planilha)
            else:
                 print("   -> Falha na extração de dados da NFe. O arquivo foi salvo, mas verifique-o.")
        else:
            dados_planilha.append({'Unidade': unidade, 'CNPJ Encontrado': cnpj_limpo, 'Tipo Documento': tipo_doc, 'Data Processamento': datetime.now().strftime("%d/%m/%Y %H:%M:%S"), 'Arquivo': os.path.basename(caminho_salvo)})
            if tipo_doc == 'Boletos':
                atualizar_planilha(PLANILHA_BOLETOS, dados_planilha)
            elif tipo_doc == 'Impostos':
                atualizar_planilha(PLANILHA_IMPOSTOS, dados_planilha)

# --- BLOCO PRINCIPAL DE EXECUÇÃO ---
if __name__ == '__main__':
    print("Iniciando automação...")
    os.makedirs(PASTA_INCONSISTENCIAS, exist_ok=True)
    df_unidades = carregar_unidades(ARQUIVO_UNIDADES_CNPJ)
    if df_unidades is None: exit()
    
    servico_gmail = autenticar_gmail()
    if servico_gmail:
        emails = buscar_emails(servico_gmail, query="is:unread")
        if not emails: print("Nenhum e-mail novo encontrado.")
        else:
            print(f"Encontrados {len(emails)} e-mails para analisar.")
            for email_info in emails:
                msg_id = email_info['id']
                try:
                    message = servico_gmail.users().messages().get(userId='me', id=msg_id, format='full').execute()
                    payload = message.get('payload', {})
                    headers = payload.get('headers', [])
                    assunto = next((h['value'] for h in headers if h['name'].lower() == 'subject'), '')
                    
                    print(f"\n[+] Processando e-mail ID: {msg_id} | Assunto: '{assunto}'")

                    parts_a_verificar = [payload]
                    anexos_encontrados_neste_email = False
                    while parts_a_verificar:
                        part = parts_a_verificar.pop(0)
                        if 'parts' in part:
                            parts_a_verificar.extend(part.get('parts', []))
                        if part.get('filename') and part.get('filename').lower().endswith('.pdf'):
                            anexos_encontrados_neste_email = True
                            print(f"  -> Anexo PDF encontrado: {part.get('filename')}")
                            dados_anexo = baixar_anexo_temporario(servico_gmail, msg_id, part)
                            if dados_anexo:
                                processar_anexo(dados_anexo, part.get('filename'), assunto, df_unidades)
                    
                    if not anexos_encontrados_neste_email:
                        print("  -> Nenhum anexo PDF encontrado neste e-mail.")

                    marcar_email_como_processado(servico_gmail, msg_id)
                except Exception as e:
                    print(f"\nERRO GERAL ao processar o e-mail ID {msg_id}: {e}")
                    traceback.print_exc()
    print("\nFim da execução.")