#!/usr/bin/env python3
"""
🔍 VERIFICAÇÃO DOS VALORES CORRETOS
Analisa os valores reais dos dados para corrigir confusões
"""

import pandas as pd

def verificar_valores():
    print("🔍 VERIFICAÇÃO DOS VALORES CORRETOS")
    print("=" * 60)
    
    # Carregar dados
    df_produtos = pd.read_excel('controle_produtos.xlsx')
    
    # Converter valores corretamente
    def converter_valor_brasileiro(valor):
        try:
            if pd.isna(valor) or valor == '':
                return 0.0
            valor_str = str(valor).replace('R$', '').replace(' ', '').replace('.', '').replace(',', '.')
            return float(valor_str)
        except:
            return 0.0
    
    df_produtos['Valor_Total_Numerico'] = df_produtos['Valor Total Item'].apply(converter_valor_brasileiro)
    
    print(f"📊 Total de produtos: {len(df_produtos):,}")
    print(f"💰 VALOR TOTAL GERAL: R$ {df_produtos['Valor_Total_Numerico'].sum():,.2f}")
    
    # Filtrar vacinas
    vacinas_keywords = [
        'VACINA', 'IMUNIZANTE', 'IMUNOBIOLOGICO', 
        'PFIZER', 'CORONAVAC', 'ASTRAZENECA', 'JANSSEN', 'MODERNA',
        'HEPATITE', 'INFLUENZA', 'GRIPE', 'FEBRE AMARELA', 'MENINGITE',
        'PNEUMOCOCICA', 'ROTAVIRUS', 'VARICELA', 'CAXUMBA', 'RUBEOLA',
        'TETANO', 'DIFTERIA', 'COQUELUCHE', 'POLIO', 'HPV', 'DENGUE'
    ]
    
    mask_vacinas = df_produtos['Descricao Produto'].str.contains('|'.join(vacinas_keywords), case=False, na=False)
    df_vacinas = df_produtos[mask_vacinas]
    
    print(f"💉 Produtos de vacinas encontrados: {len(df_vacinas):,}")
    print(f"💰 VALOR TOTAL DE VACINAS: R$ {df_vacinas['Valor_Total_Numerico'].sum():,.2f}")
    
    # Percentual de vacinas
    percentual = (df_vacinas['Valor_Total_Numerico'].sum() / df_produtos['Valor_Total_Numerico'].sum()) * 100
    print(f"📊 Vacinas representam {percentual:.1f}% do valor total de compras")
    
    # Top 5 vacinas por valor
    top_vacinas = df_vacinas.groupby('Descricao Produto')['Valor_Total_Numerico'].sum().sort_values(ascending=False).head(5)
    print(f"\n🏆 TOP 5 VACINAS POR VALOR:")
    for i, (vacina, valor) in enumerate(top_vacinas.items(), 1):
        print(f"   {i}. {vacina[:60]}: R$ {valor:,.2f}")
    
    # Análise por unidade
    print(f"\n🏢 ANÁLISE POR UNIDADE:")
    unidades_valor = df_produtos.groupby('Unidade')['Valor_Total_Numerico'].sum().sort_values(ascending=False).head(5)
    for unidade, valor in unidades_valor.items():
        print(f"   {unidade}: R$ {valor:,.2f}")
    
    # Análise temporal
    print(f"\n📅 ANÁLISE TEMPORAL:")
    df_produtos['Data_Emissao_Dt'] = pd.to_datetime(df_produtos['Data Emissao'], format='%d/%m/%Y', errors='coerce')
    df_produtos['Ano_Mes'] = df_produtos['Data_Emissao_Dt'].dt.to_period('M')
    
    mensal = df_produtos.groupby('Ano_Mes')['Valor_Total_Numerico'].sum().sort_index().tail(6)
    for periodo, valor in mensal.items():
        print(f"   {periodo}: R$ {valor:,.2f}")

if __name__ == "__main__":
    verificar_valores()
