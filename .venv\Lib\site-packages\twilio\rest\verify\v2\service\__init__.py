r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Verify
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, serialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.verify.v2.service.access_token import AccessTokenList
from twilio.rest.verify.v2.service.entity import EntityList
from twilio.rest.verify.v2.service.messaging_configuration import (
    MessagingConfigurationList,
)
from twilio.rest.verify.v2.service.rate_limit import RateLimitList
from twilio.rest.verify.v2.service.verification import VerificationList
from twilio.rest.verify.v2.service.verification_check import VerificationCheckList
from twilio.rest.verify.v2.service.webhook import WebhookList


class ServiceInstance(InstanceResource):
    """
    :ivar sid: The unique string that we created to identify the Service resource.
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Service resource.
    :ivar friendly_name: The name that appears in the body of your verification messages. It can be up to 30 characters long and can include letters, numbers, spaces, dashes, underscores. Phone numbers, special characters or links are NOT allowed. It cannot contain more than 4 (consecutive or non-consecutive) digits. **This value should not contain PII.**
    :ivar code_length: The length of the verification code to generate.
    :ivar lookup_enabled: Whether to perform a lookup with each verification started and return info about the phone number.
    :ivar psd2_enabled: Whether to pass PSD2 transaction parameters when starting a verification.
    :ivar skip_sms_to_landlines: Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
    :ivar dtmf_input_required: Whether to ask the user to press a number before delivering the verify code in a phone call.
    :ivar tts_name: The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
    :ivar do_not_share_warning_enabled: Whether to add a security warning at the end of an SMS verification body. Disabled by default and applies only to SMS. Example SMS body: `Your AppName verification code is: 1234. Don’t share this code with anyone; our employees will never ask for the code`
    :ivar custom_code_enabled: Whether to allow sending verifications with a custom code instead of a randomly generated one.
    :ivar push: Configurations for the Push factors (channel) created under this Service.
    :ivar totp: Configurations for the TOTP factors (channel) created under this Service.
    :ivar default_template_sid:
    :ivar whatsapp:
    :ivar verify_event_subscription_enabled: Whether to allow verifications from the service to reach the stream-events sinks if configured
    :ivar date_created: The date and time in GMT when the resource was created specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar url: The absolute URL of the resource.
    :ivar links: The URLs of related resources.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.sid: Optional[str] = payload.get("sid")
        self.account_sid: Optional[str] = payload.get("account_sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.code_length: Optional[int] = deserialize.integer(
            payload.get("code_length")
        )
        self.lookup_enabled: Optional[bool] = payload.get("lookup_enabled")
        self.psd2_enabled: Optional[bool] = payload.get("psd2_enabled")
        self.skip_sms_to_landlines: Optional[bool] = payload.get(
            "skip_sms_to_landlines"
        )
        self.dtmf_input_required: Optional[bool] = payload.get("dtmf_input_required")
        self.tts_name: Optional[str] = payload.get("tts_name")
        self.do_not_share_warning_enabled: Optional[bool] = payload.get(
            "do_not_share_warning_enabled"
        )
        self.custom_code_enabled: Optional[bool] = payload.get("custom_code_enabled")
        self.push: Optional[Dict[str, object]] = payload.get("push")
        self.totp: Optional[Dict[str, object]] = payload.get("totp")
        self.default_template_sid: Optional[str] = payload.get("default_template_sid")
        self.whatsapp: Optional[Dict[str, object]] = payload.get("whatsapp")
        self.verify_event_subscription_enabled: Optional[bool] = payload.get(
            "verify_event_subscription_enabled"
        )
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[ServiceContext] = None

    @property
    def _proxy(self) -> "ServiceContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ServiceContext for this ServiceInstance
        """
        if self._context is None:
            self._context = ServiceContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "ServiceInstance":
        """
        Fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ServiceInstance":
        """
        Asynchronous coroutine to fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        code_length: Union[int, object] = values.unset,
        lookup_enabled: Union[bool, object] = values.unset,
        skip_sms_to_landlines: Union[bool, object] = values.unset,
        dtmf_input_required: Union[bool, object] = values.unset,
        tts_name: Union[str, object] = values.unset,
        psd2_enabled: Union[bool, object] = values.unset,
        do_not_share_warning_enabled: Union[bool, object] = values.unset,
        custom_code_enabled: Union[bool, object] = values.unset,
        push_include_date: Union[bool, object] = values.unset,
        push_apn_credential_sid: Union[str, object] = values.unset,
        push_fcm_credential_sid: Union[str, object] = values.unset,
        totp_issuer: Union[str, object] = values.unset,
        totp_time_step: Union[int, object] = values.unset,
        totp_code_length: Union[int, object] = values.unset,
        totp_skew: Union[int, object] = values.unset,
        default_template_sid: Union[str, object] = values.unset,
        whatsapp_msg_service_sid: Union[str, object] = values.unset,
        whatsapp_from: Union[str, object] = values.unset,
        verify_event_subscription_enabled: Union[bool, object] = values.unset,
    ) -> "ServiceInstance":
        """
        Update the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the verification service. It can be up to 32 characters long. **This value should not contain PII.**
        :param code_length: The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
        :param lookup_enabled: Whether to perform a lookup with each verification started and return info about the phone number.
        :param skip_sms_to_landlines: Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
        :param dtmf_input_required: Whether to ask the user to press a number before delivering the verify code in a phone call.
        :param tts_name: The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
        :param psd2_enabled: Whether to pass PSD2 transaction parameters when starting a verification.
        :param do_not_share_warning_enabled: Whether to add a privacy warning at the end of an SMS. **Disabled by default and applies only for SMS.**
        :param custom_code_enabled: Whether to allow sending verifications with a custom code instead of a randomly generated one.
        :param push_include_date: Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter.
        :param push_apn_credential_sid: Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param push_fcm_credential_sid: Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param totp_issuer: Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI.
        :param totp_time_step: Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
        :param totp_code_length: Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
        :param totp_skew: Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
        :param default_template_sid: The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
        :param whatsapp_msg_service_sid: The SID of the [Messaging Service](https://www.twilio.com/docs/messaging/services) to associate with the Verification Service.
        :param whatsapp_from: The WhatsApp number to use as the sender of the verification messages. This number must be associated with the WhatsApp Message Service.
        :param verify_event_subscription_enabled: Whether to allow verifications from the service to reach the stream-events sinks if configured

        :returns: The updated ServiceInstance
        """
        return self._proxy.update(
            friendly_name=friendly_name,
            code_length=code_length,
            lookup_enabled=lookup_enabled,
            skip_sms_to_landlines=skip_sms_to_landlines,
            dtmf_input_required=dtmf_input_required,
            tts_name=tts_name,
            psd2_enabled=psd2_enabled,
            do_not_share_warning_enabled=do_not_share_warning_enabled,
            custom_code_enabled=custom_code_enabled,
            push_include_date=push_include_date,
            push_apn_credential_sid=push_apn_credential_sid,
            push_fcm_credential_sid=push_fcm_credential_sid,
            totp_issuer=totp_issuer,
            totp_time_step=totp_time_step,
            totp_code_length=totp_code_length,
            totp_skew=totp_skew,
            default_template_sid=default_template_sid,
            whatsapp_msg_service_sid=whatsapp_msg_service_sid,
            whatsapp_from=whatsapp_from,
            verify_event_subscription_enabled=verify_event_subscription_enabled,
        )

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        code_length: Union[int, object] = values.unset,
        lookup_enabled: Union[bool, object] = values.unset,
        skip_sms_to_landlines: Union[bool, object] = values.unset,
        dtmf_input_required: Union[bool, object] = values.unset,
        tts_name: Union[str, object] = values.unset,
        psd2_enabled: Union[bool, object] = values.unset,
        do_not_share_warning_enabled: Union[bool, object] = values.unset,
        custom_code_enabled: Union[bool, object] = values.unset,
        push_include_date: Union[bool, object] = values.unset,
        push_apn_credential_sid: Union[str, object] = values.unset,
        push_fcm_credential_sid: Union[str, object] = values.unset,
        totp_issuer: Union[str, object] = values.unset,
        totp_time_step: Union[int, object] = values.unset,
        totp_code_length: Union[int, object] = values.unset,
        totp_skew: Union[int, object] = values.unset,
        default_template_sid: Union[str, object] = values.unset,
        whatsapp_msg_service_sid: Union[str, object] = values.unset,
        whatsapp_from: Union[str, object] = values.unset,
        verify_event_subscription_enabled: Union[bool, object] = values.unset,
    ) -> "ServiceInstance":
        """
        Asynchronous coroutine to update the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the verification service. It can be up to 32 characters long. **This value should not contain PII.**
        :param code_length: The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
        :param lookup_enabled: Whether to perform a lookup with each verification started and return info about the phone number.
        :param skip_sms_to_landlines: Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
        :param dtmf_input_required: Whether to ask the user to press a number before delivering the verify code in a phone call.
        :param tts_name: The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
        :param psd2_enabled: Whether to pass PSD2 transaction parameters when starting a verification.
        :param do_not_share_warning_enabled: Whether to add a privacy warning at the end of an SMS. **Disabled by default and applies only for SMS.**
        :param custom_code_enabled: Whether to allow sending verifications with a custom code instead of a randomly generated one.
        :param push_include_date: Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter.
        :param push_apn_credential_sid: Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param push_fcm_credential_sid: Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param totp_issuer: Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI.
        :param totp_time_step: Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
        :param totp_code_length: Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
        :param totp_skew: Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
        :param default_template_sid: The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
        :param whatsapp_msg_service_sid: The SID of the [Messaging Service](https://www.twilio.com/docs/messaging/services) to associate with the Verification Service.
        :param whatsapp_from: The WhatsApp number to use as the sender of the verification messages. This number must be associated with the WhatsApp Message Service.
        :param verify_event_subscription_enabled: Whether to allow verifications from the service to reach the stream-events sinks if configured

        :returns: The updated ServiceInstance
        """
        return await self._proxy.update_async(
            friendly_name=friendly_name,
            code_length=code_length,
            lookup_enabled=lookup_enabled,
            skip_sms_to_landlines=skip_sms_to_landlines,
            dtmf_input_required=dtmf_input_required,
            tts_name=tts_name,
            psd2_enabled=psd2_enabled,
            do_not_share_warning_enabled=do_not_share_warning_enabled,
            custom_code_enabled=custom_code_enabled,
            push_include_date=push_include_date,
            push_apn_credential_sid=push_apn_credential_sid,
            push_fcm_credential_sid=push_fcm_credential_sid,
            totp_issuer=totp_issuer,
            totp_time_step=totp_time_step,
            totp_code_length=totp_code_length,
            totp_skew=totp_skew,
            default_template_sid=default_template_sid,
            whatsapp_msg_service_sid=whatsapp_msg_service_sid,
            whatsapp_from=whatsapp_from,
            verify_event_subscription_enabled=verify_event_subscription_enabled,
        )

    @property
    def access_tokens(self) -> AccessTokenList:
        """
        Access the access_tokens
        """
        return self._proxy.access_tokens

    @property
    def entities(self) -> EntityList:
        """
        Access the entities
        """
        return self._proxy.entities

    @property
    def messaging_configurations(self) -> MessagingConfigurationList:
        """
        Access the messaging_configurations
        """
        return self._proxy.messaging_configurations

    @property
    def rate_limits(self) -> RateLimitList:
        """
        Access the rate_limits
        """
        return self._proxy.rate_limits

    @property
    def verifications(self) -> VerificationList:
        """
        Access the verifications
        """
        return self._proxy.verifications

    @property
    def verification_checks(self) -> VerificationCheckList:
        """
        Access the verification_checks
        """
        return self._proxy.verification_checks

    @property
    def webhooks(self) -> WebhookList:
        """
        Access the webhooks
        """
        return self._proxy.webhooks

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Verify.V2.ServiceInstance {}>".format(context)


class ServiceContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the ServiceContext

        :param version: Version that contains the resource
        :param sid: The Twilio-provided string that uniquely identifies the Service resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/Services/{sid}".format(**self._solution)

        self._access_tokens: Optional[AccessTokenList] = None
        self._entities: Optional[EntityList] = None
        self._messaging_configurations: Optional[MessagingConfigurationList] = None
        self._rate_limits: Optional[RateLimitList] = None
        self._verifications: Optional[VerificationList] = None
        self._verification_checks: Optional[VerificationCheckList] = None
        self._webhooks: Optional[WebhookList] = None

    def delete(self) -> bool:
        """
        Deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ServiceInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> ServiceInstance:
        """
        Fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ServiceInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ServiceInstance:
        """
        Asynchronous coroutine to fetch the ServiceInstance


        :returns: The fetched ServiceInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ServiceInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self,
        friendly_name: Union[str, object] = values.unset,
        code_length: Union[int, object] = values.unset,
        lookup_enabled: Union[bool, object] = values.unset,
        skip_sms_to_landlines: Union[bool, object] = values.unset,
        dtmf_input_required: Union[bool, object] = values.unset,
        tts_name: Union[str, object] = values.unset,
        psd2_enabled: Union[bool, object] = values.unset,
        do_not_share_warning_enabled: Union[bool, object] = values.unset,
        custom_code_enabled: Union[bool, object] = values.unset,
        push_include_date: Union[bool, object] = values.unset,
        push_apn_credential_sid: Union[str, object] = values.unset,
        push_fcm_credential_sid: Union[str, object] = values.unset,
        totp_issuer: Union[str, object] = values.unset,
        totp_time_step: Union[int, object] = values.unset,
        totp_code_length: Union[int, object] = values.unset,
        totp_skew: Union[int, object] = values.unset,
        default_template_sid: Union[str, object] = values.unset,
        whatsapp_msg_service_sid: Union[str, object] = values.unset,
        whatsapp_from: Union[str, object] = values.unset,
        verify_event_subscription_enabled: Union[bool, object] = values.unset,
    ) -> ServiceInstance:
        """
        Update the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the verification service. It can be up to 32 characters long. **This value should not contain PII.**
        :param code_length: The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
        :param lookup_enabled: Whether to perform a lookup with each verification started and return info about the phone number.
        :param skip_sms_to_landlines: Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
        :param dtmf_input_required: Whether to ask the user to press a number before delivering the verify code in a phone call.
        :param tts_name: The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
        :param psd2_enabled: Whether to pass PSD2 transaction parameters when starting a verification.
        :param do_not_share_warning_enabled: Whether to add a privacy warning at the end of an SMS. **Disabled by default and applies only for SMS.**
        :param custom_code_enabled: Whether to allow sending verifications with a custom code instead of a randomly generated one.
        :param push_include_date: Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter.
        :param push_apn_credential_sid: Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param push_fcm_credential_sid: Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param totp_issuer: Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI.
        :param totp_time_step: Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
        :param totp_code_length: Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
        :param totp_skew: Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
        :param default_template_sid: The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
        :param whatsapp_msg_service_sid: The SID of the [Messaging Service](https://www.twilio.com/docs/messaging/services) to associate with the Verification Service.
        :param whatsapp_from: The WhatsApp number to use as the sender of the verification messages. This number must be associated with the WhatsApp Message Service.
        :param verify_event_subscription_enabled: Whether to allow verifications from the service to reach the stream-events sinks if configured

        :returns: The updated ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "CodeLength": code_length,
                "LookupEnabled": serialize.boolean_to_string(lookup_enabled),
                "SkipSmsToLandlines": serialize.boolean_to_string(
                    skip_sms_to_landlines
                ),
                "DtmfInputRequired": serialize.boolean_to_string(dtmf_input_required),
                "TtsName": tts_name,
                "Psd2Enabled": serialize.boolean_to_string(psd2_enabled),
                "DoNotShareWarningEnabled": serialize.boolean_to_string(
                    do_not_share_warning_enabled
                ),
                "CustomCodeEnabled": serialize.boolean_to_string(custom_code_enabled),
                "Push.IncludeDate": serialize.boolean_to_string(push_include_date),
                "Push.ApnCredentialSid": push_apn_credential_sid,
                "Push.FcmCredentialSid": push_fcm_credential_sid,
                "Totp.Issuer": totp_issuer,
                "Totp.TimeStep": totp_time_step,
                "Totp.CodeLength": totp_code_length,
                "Totp.Skew": totp_skew,
                "DefaultTemplateSid": default_template_sid,
                "Whatsapp.MsgServiceSid": whatsapp_msg_service_sid,
                "Whatsapp.From": whatsapp_from,
                "VerifyEventSubscriptionEnabled": serialize.boolean_to_string(
                    verify_event_subscription_enabled
                ),
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload, sid=self._solution["sid"])

    async def update_async(
        self,
        friendly_name: Union[str, object] = values.unset,
        code_length: Union[int, object] = values.unset,
        lookup_enabled: Union[bool, object] = values.unset,
        skip_sms_to_landlines: Union[bool, object] = values.unset,
        dtmf_input_required: Union[bool, object] = values.unset,
        tts_name: Union[str, object] = values.unset,
        psd2_enabled: Union[bool, object] = values.unset,
        do_not_share_warning_enabled: Union[bool, object] = values.unset,
        custom_code_enabled: Union[bool, object] = values.unset,
        push_include_date: Union[bool, object] = values.unset,
        push_apn_credential_sid: Union[str, object] = values.unset,
        push_fcm_credential_sid: Union[str, object] = values.unset,
        totp_issuer: Union[str, object] = values.unset,
        totp_time_step: Union[int, object] = values.unset,
        totp_code_length: Union[int, object] = values.unset,
        totp_skew: Union[int, object] = values.unset,
        default_template_sid: Union[str, object] = values.unset,
        whatsapp_msg_service_sid: Union[str, object] = values.unset,
        whatsapp_from: Union[str, object] = values.unset,
        verify_event_subscription_enabled: Union[bool, object] = values.unset,
    ) -> ServiceInstance:
        """
        Asynchronous coroutine to update the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the verification service. It can be up to 32 characters long. **This value should not contain PII.**
        :param code_length: The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
        :param lookup_enabled: Whether to perform a lookup with each verification started and return info about the phone number.
        :param skip_sms_to_landlines: Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
        :param dtmf_input_required: Whether to ask the user to press a number before delivering the verify code in a phone call.
        :param tts_name: The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
        :param psd2_enabled: Whether to pass PSD2 transaction parameters when starting a verification.
        :param do_not_share_warning_enabled: Whether to add a privacy warning at the end of an SMS. **Disabled by default and applies only for SMS.**
        :param custom_code_enabled: Whether to allow sending verifications with a custom code instead of a randomly generated one.
        :param push_include_date: Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter.
        :param push_apn_credential_sid: Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param push_fcm_credential_sid: Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param totp_issuer: Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI.
        :param totp_time_step: Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
        :param totp_code_length: Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
        :param totp_skew: Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
        :param default_template_sid: The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
        :param whatsapp_msg_service_sid: The SID of the [Messaging Service](https://www.twilio.com/docs/messaging/services) to associate with the Verification Service.
        :param whatsapp_from: The WhatsApp number to use as the sender of the verification messages. This number must be associated with the WhatsApp Message Service.
        :param verify_event_subscription_enabled: Whether to allow verifications from the service to reach the stream-events sinks if configured

        :returns: The updated ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "CodeLength": code_length,
                "LookupEnabled": serialize.boolean_to_string(lookup_enabled),
                "SkipSmsToLandlines": serialize.boolean_to_string(
                    skip_sms_to_landlines
                ),
                "DtmfInputRequired": serialize.boolean_to_string(dtmf_input_required),
                "TtsName": tts_name,
                "Psd2Enabled": serialize.boolean_to_string(psd2_enabled),
                "DoNotShareWarningEnabled": serialize.boolean_to_string(
                    do_not_share_warning_enabled
                ),
                "CustomCodeEnabled": serialize.boolean_to_string(custom_code_enabled),
                "Push.IncludeDate": serialize.boolean_to_string(push_include_date),
                "Push.ApnCredentialSid": push_apn_credential_sid,
                "Push.FcmCredentialSid": push_fcm_credential_sid,
                "Totp.Issuer": totp_issuer,
                "Totp.TimeStep": totp_time_step,
                "Totp.CodeLength": totp_code_length,
                "Totp.Skew": totp_skew,
                "DefaultTemplateSid": default_template_sid,
                "Whatsapp.MsgServiceSid": whatsapp_msg_service_sid,
                "Whatsapp.From": whatsapp_from,
                "VerifyEventSubscriptionEnabled": serialize.boolean_to_string(
                    verify_event_subscription_enabled
                ),
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload, sid=self._solution["sid"])

    @property
    def access_tokens(self) -> AccessTokenList:
        """
        Access the access_tokens
        """
        if self._access_tokens is None:
            self._access_tokens = AccessTokenList(
                self._version,
                self._solution["sid"],
            )
        return self._access_tokens

    @property
    def entities(self) -> EntityList:
        """
        Access the entities
        """
        if self._entities is None:
            self._entities = EntityList(
                self._version,
                self._solution["sid"],
            )
        return self._entities

    @property
    def messaging_configurations(self) -> MessagingConfigurationList:
        """
        Access the messaging_configurations
        """
        if self._messaging_configurations is None:
            self._messaging_configurations = MessagingConfigurationList(
                self._version,
                self._solution["sid"],
            )
        return self._messaging_configurations

    @property
    def rate_limits(self) -> RateLimitList:
        """
        Access the rate_limits
        """
        if self._rate_limits is None:
            self._rate_limits = RateLimitList(
                self._version,
                self._solution["sid"],
            )
        return self._rate_limits

    @property
    def verifications(self) -> VerificationList:
        """
        Access the verifications
        """
        if self._verifications is None:
            self._verifications = VerificationList(
                self._version,
                self._solution["sid"],
            )
        return self._verifications

    @property
    def verification_checks(self) -> VerificationCheckList:
        """
        Access the verification_checks
        """
        if self._verification_checks is None:
            self._verification_checks = VerificationCheckList(
                self._version,
                self._solution["sid"],
            )
        return self._verification_checks

    @property
    def webhooks(self) -> WebhookList:
        """
        Access the webhooks
        """
        if self._webhooks is None:
            self._webhooks = WebhookList(
                self._version,
                self._solution["sid"],
            )
        return self._webhooks

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Verify.V2.ServiceContext {}>".format(context)


class ServicePage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ServiceInstance:
        """
        Build an instance of ServiceInstance

        :param payload: Payload response from the API
        """
        return ServiceInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Verify.V2.ServicePage>"


class ServiceList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the ServiceList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/Services"

    def create(
        self,
        friendly_name: str,
        code_length: Union[int, object] = values.unset,
        lookup_enabled: Union[bool, object] = values.unset,
        skip_sms_to_landlines: Union[bool, object] = values.unset,
        dtmf_input_required: Union[bool, object] = values.unset,
        tts_name: Union[str, object] = values.unset,
        psd2_enabled: Union[bool, object] = values.unset,
        do_not_share_warning_enabled: Union[bool, object] = values.unset,
        custom_code_enabled: Union[bool, object] = values.unset,
        push_include_date: Union[bool, object] = values.unset,
        push_apn_credential_sid: Union[str, object] = values.unset,
        push_fcm_credential_sid: Union[str, object] = values.unset,
        totp_issuer: Union[str, object] = values.unset,
        totp_time_step: Union[int, object] = values.unset,
        totp_code_length: Union[int, object] = values.unset,
        totp_skew: Union[int, object] = values.unset,
        default_template_sid: Union[str, object] = values.unset,
        whatsapp_msg_service_sid: Union[str, object] = values.unset,
        whatsapp_from: Union[str, object] = values.unset,
        verify_event_subscription_enabled: Union[bool, object] = values.unset,
    ) -> ServiceInstance:
        """
        Create the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the verification service. It can be up to 32 characters long. **This value should not contain PII.**
        :param code_length: The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
        :param lookup_enabled: Whether to perform a lookup with each verification started and return info about the phone number.
        :param skip_sms_to_landlines: Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
        :param dtmf_input_required: Whether to ask the user to press a number before delivering the verify code in a phone call.
        :param tts_name: The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
        :param psd2_enabled: Whether to pass PSD2 transaction parameters when starting a verification.
        :param do_not_share_warning_enabled: Whether to add a security warning at the end of an SMS verification body. Disabled by default and applies only to SMS. Example SMS body: `Your AppName verification code is: 1234. Don’t share this code with anyone; our employees will never ask for the code`
        :param custom_code_enabled: Whether to allow sending verifications with a custom code instead of a randomly generated one.
        :param push_include_date: Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter. This timestamp value is the same one as the one found in `date_created`, please use that one instead.
        :param push_apn_credential_sid: Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param push_fcm_credential_sid: Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param totp_issuer: Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI. Defaults to the service friendly name if not provided.
        :param totp_time_step: Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
        :param totp_code_length: Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
        :param totp_skew: Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
        :param default_template_sid: The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
        :param whatsapp_msg_service_sid: The SID of the Messaging Service containing WhatsApp Sender(s) that Verify will use to send WhatsApp messages to your users.
        :param whatsapp_from: The number to use as the WhatsApp Sender that Verify will use to send WhatsApp messages to your users.This WhatsApp Sender must be associated with a Messaging Service SID.
        :param verify_event_subscription_enabled: Whether to allow verifications from the service to reach the stream-events sinks if configured

        :returns: The created ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "CodeLength": code_length,
                "LookupEnabled": serialize.boolean_to_string(lookup_enabled),
                "SkipSmsToLandlines": serialize.boolean_to_string(
                    skip_sms_to_landlines
                ),
                "DtmfInputRequired": serialize.boolean_to_string(dtmf_input_required),
                "TtsName": tts_name,
                "Psd2Enabled": serialize.boolean_to_string(psd2_enabled),
                "DoNotShareWarningEnabled": serialize.boolean_to_string(
                    do_not_share_warning_enabled
                ),
                "CustomCodeEnabled": serialize.boolean_to_string(custom_code_enabled),
                "Push.IncludeDate": serialize.boolean_to_string(push_include_date),
                "Push.ApnCredentialSid": push_apn_credential_sid,
                "Push.FcmCredentialSid": push_fcm_credential_sid,
                "Totp.Issuer": totp_issuer,
                "Totp.TimeStep": totp_time_step,
                "Totp.CodeLength": totp_code_length,
                "Totp.Skew": totp_skew,
                "DefaultTemplateSid": default_template_sid,
                "Whatsapp.MsgServiceSid": whatsapp_msg_service_sid,
                "Whatsapp.From": whatsapp_from,
                "VerifyEventSubscriptionEnabled": serialize.boolean_to_string(
                    verify_event_subscription_enabled
                ),
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload)

    async def create_async(
        self,
        friendly_name: str,
        code_length: Union[int, object] = values.unset,
        lookup_enabled: Union[bool, object] = values.unset,
        skip_sms_to_landlines: Union[bool, object] = values.unset,
        dtmf_input_required: Union[bool, object] = values.unset,
        tts_name: Union[str, object] = values.unset,
        psd2_enabled: Union[bool, object] = values.unset,
        do_not_share_warning_enabled: Union[bool, object] = values.unset,
        custom_code_enabled: Union[bool, object] = values.unset,
        push_include_date: Union[bool, object] = values.unset,
        push_apn_credential_sid: Union[str, object] = values.unset,
        push_fcm_credential_sid: Union[str, object] = values.unset,
        totp_issuer: Union[str, object] = values.unset,
        totp_time_step: Union[int, object] = values.unset,
        totp_code_length: Union[int, object] = values.unset,
        totp_skew: Union[int, object] = values.unset,
        default_template_sid: Union[str, object] = values.unset,
        whatsapp_msg_service_sid: Union[str, object] = values.unset,
        whatsapp_from: Union[str, object] = values.unset,
        verify_event_subscription_enabled: Union[bool, object] = values.unset,
    ) -> ServiceInstance:
        """
        Asynchronously create the ServiceInstance

        :param friendly_name: A descriptive string that you create to describe the verification service. It can be up to 32 characters long. **This value should not contain PII.**
        :param code_length: The length of the verification code to generate. Must be an integer value between 4 and 10, inclusive.
        :param lookup_enabled: Whether to perform a lookup with each verification started and return info about the phone number.
        :param skip_sms_to_landlines: Whether to skip sending SMS verifications to landlines. Requires `lookup_enabled`.
        :param dtmf_input_required: Whether to ask the user to press a number before delivering the verify code in a phone call.
        :param tts_name: The name of an alternative text-to-speech service to use in phone calls. Applies only to TTS languages.
        :param psd2_enabled: Whether to pass PSD2 transaction parameters when starting a verification.
        :param do_not_share_warning_enabled: Whether to add a security warning at the end of an SMS verification body. Disabled by default and applies only to SMS. Example SMS body: `Your AppName verification code is: 1234. Don’t share this code with anyone; our employees will never ask for the code`
        :param custom_code_enabled: Whether to allow sending verifications with a custom code instead of a randomly generated one.
        :param push_include_date: Optional configuration for the Push factors. If true, include the date in the Challenge's response. Otherwise, the date is omitted from the response. See [Challenge](https://www.twilio.com/docs/verify/api/challenge) resource’s details parameter for more info. Default: false. **Deprecated** do not use this parameter. This timestamp value is the same one as the one found in `date_created`, please use that one instead.
        :param push_apn_credential_sid: Optional configuration for the Push factors. Set the APN Credential for this service. This will allow to send push notifications to iOS devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param push_fcm_credential_sid: Optional configuration for the Push factors. Set the FCM Credential for this service. This will allow to send push notifications to Android devices. See [Credential Resource](https://www.twilio.com/docs/notify/api/credential-resource)
        :param totp_issuer: Optional configuration for the TOTP factors. Set TOTP Issuer for this service. This will allow to configure the issuer of the TOTP URI. Defaults to the service friendly name if not provided.
        :param totp_time_step: Optional configuration for the TOTP factors. Defines how often, in seconds, are TOTP codes generated. i.e, a new TOTP code is generated every time_step seconds. Must be between 20 and 60 seconds, inclusive. Defaults to 30 seconds
        :param totp_code_length: Optional configuration for the TOTP factors. Number of digits for generated TOTP codes. Must be between 3 and 8, inclusive. Defaults to 6
        :param totp_skew: Optional configuration for the TOTP factors. The number of time-steps, past and future, that are valid for validation of TOTP codes. Must be between 0 and 2, inclusive. Defaults to 1
        :param default_template_sid: The default message [template](https://www.twilio.com/docs/verify/api/templates). Will be used for all SMS verifications unless explicitly overriden. SMS channel only.
        :param whatsapp_msg_service_sid: The SID of the Messaging Service containing WhatsApp Sender(s) that Verify will use to send WhatsApp messages to your users.
        :param whatsapp_from: The number to use as the WhatsApp Sender that Verify will use to send WhatsApp messages to your users.This WhatsApp Sender must be associated with a Messaging Service SID.
        :param verify_event_subscription_enabled: Whether to allow verifications from the service to reach the stream-events sinks if configured

        :returns: The created ServiceInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
                "CodeLength": code_length,
                "LookupEnabled": serialize.boolean_to_string(lookup_enabled),
                "SkipSmsToLandlines": serialize.boolean_to_string(
                    skip_sms_to_landlines
                ),
                "DtmfInputRequired": serialize.boolean_to_string(dtmf_input_required),
                "TtsName": tts_name,
                "Psd2Enabled": serialize.boolean_to_string(psd2_enabled),
                "DoNotShareWarningEnabled": serialize.boolean_to_string(
                    do_not_share_warning_enabled
                ),
                "CustomCodeEnabled": serialize.boolean_to_string(custom_code_enabled),
                "Push.IncludeDate": serialize.boolean_to_string(push_include_date),
                "Push.ApnCredentialSid": push_apn_credential_sid,
                "Push.FcmCredentialSid": push_fcm_credential_sid,
                "Totp.Issuer": totp_issuer,
                "Totp.TimeStep": totp_time_step,
                "Totp.CodeLength": totp_code_length,
                "Totp.Skew": totp_skew,
                "DefaultTemplateSid": default_template_sid,
                "Whatsapp.MsgServiceSid": whatsapp_msg_service_sid,
                "Whatsapp.From": whatsapp_from,
                "VerifyEventSubscriptionEnabled": serialize.boolean_to_string(
                    verify_event_subscription_enabled
                ),
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ServiceInstance(self._version, payload)

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ServiceInstance]:
        """
        Streams ServiceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ServiceInstance]:
        """
        Asynchronously streams ServiceInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ServiceInstance]:
        """
        Lists ServiceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ServiceInstance]:
        """
        Asynchronously lists ServiceInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ServicePage:
        """
        Retrieve a single page of ServiceInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ServiceInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ServicePage(self._version, response)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ServicePage:
        """
        Asynchronously retrieve a single page of ServiceInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ServiceInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ServicePage(self._version, response)

    def get_page(self, target_url: str) -> ServicePage:
        """
        Retrieve a specific page of ServiceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ServiceInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ServicePage(self._version, response)

    async def get_page_async(self, target_url: str) -> ServicePage:
        """
        Asynchronously retrieve a specific page of ServiceInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ServiceInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ServicePage(self._version, response)

    def get(self, sid: str) -> ServiceContext:
        """
        Constructs a ServiceContext

        :param sid: The Twilio-provided string that uniquely identifies the Service resource to update.
        """
        return ServiceContext(self._version, sid=sid)

    def __call__(self, sid: str) -> ServiceContext:
        """
        Constructs a ServiceContext

        :param sid: The Twilio-provided string that uniquely identifies the Service resource to update.
        """
        return ServiceContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Verify.V2.ServiceList>"
