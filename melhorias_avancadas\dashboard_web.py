#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DASHBOARD FINANCEIRO AVANÇADO
Sistema web profissional para análise financeira detalhada por unidade
Desenvolvido para analistas financeiros com necessidades avançadas
"""

from flask import Flask, render_template, jsonify, request
import pandas as pd
import json
import os
from datetime import datetime, timedelta
import numpy as np
from collections import defaultdict

app = Flask(__name__)

class DashboardFinanceiroAvancado:
    def __init__(self):
        self.df_produtos = None
        self.df_faturamento = None  # 🎯 DADOS DE FATURAMENTO GERAL
        # REMOVIDO: df_boletos e df_impostos (não servem para nada conforme solicitado)
        self.unidades_info = {}
        self.carregar_dados()
        self.carregar_unidades_info()

    def carregar_dados(self):
        """Carrega dados das planilhas com caminhos corretos"""
        print("🔍 INICIANDO CARREGAMENTO DE DADOS...")
        print(f"📁 Pasta atual: {os.getcwd()}")

        try:
            # PRIORIZAR PASTA RAIZ (dados atualizados com fabricante, lote e validade) depois pasta dados/
            base_paths = ['../', './', '../dados/', 'dados/']

            for base_path in base_paths:
                print(f"🔍 Testando caminho: {base_path}")
                try:
                    # Verificar se arquivo de produtos existe
                    arquivo_produtos = f'{base_path}controle_produtos.xlsx'
                    print(f"   📦 Procurando produtos em: {arquivo_produtos}")
                    if os.path.exists(arquivo_produtos):
                        print(f"   ✅ Arquivo produtos encontrado!")
                        self.df_produtos = pd.read_excel(arquivo_produtos)
                        self.processar_dados_produtos()
                        print(f"   ✅ Produtos carregados: {len(self.df_produtos)} registros")
                    else:
                        print(f"   ❌ Arquivo produtos não encontrado")

                    # Verificar se arquivo de faturamento existe
                    arquivo_faturamento = f'{base_path}controle_faturamento_geral.xlsx'
                    print(f"   💰 Procurando faturamento em: {arquivo_faturamento}")
                    if os.path.exists(arquivo_faturamento):
                        print(f"   ✅ Arquivo faturamento encontrado!")
                        self.df_faturamento = pd.read_excel(arquivo_faturamento)
                        self.processar_dados_faturamento()
                        print(f"   ✅ Faturamento carregado: {len(self.df_faturamento)} registros")
                    else:
                        print(f"   ❌ Arquivo faturamento não encontrado")

                    # REMOVIDO: Carregamento de boletos e impostos (não servem para nada conforme solicitado)

                    # Se encontrou pelo menos um arquivo, parar de procurar
                    if self.df_produtos is not None or self.df_faturamento is not None:
                        print(f"✅ Dados carregados com sucesso do caminho: {base_path}")
                        break
                    else:
                        print(f"   ⚠️ Nenhum arquivo encontrado em {base_path}")

                except Exception as e:
                    print(f"❌ Erro ao carregar de {base_path}: {e}")
                    continue

            # Resumo final
            print(f"\n📊 RESUMO DO CARREGAMENTO:")
            print(f"   📦 Produtos: {'✅' if self.df_produtos is not None else '❌'} ({len(self.df_produtos) if self.df_produtos is not None else 0} registros)")
            print(f"   💰 Faturamento: {'✅' if self.df_faturamento is not None else '❌'} ({len(self.df_faturamento) if self.df_faturamento is not None else 0} registros)")
            # REMOVIDO: Linha de boletos (não servem para nada)

        except Exception as e:
            print(f"❌ Erro geral ao carregar dados: {e}")



    def carregar_unidades_info(self):
        """Carrega informações das unidades"""
        try:
            base_paths = ['../dados/', 'dados/', './']

            for base_path in base_paths:
                if os.path.exists(f'{base_path}unidades_cnpjs.csv'):
                    # O arquivo tem formato UNIDADE;CNPJ em uma coluna
                    df_unidades = pd.read_csv(f'{base_path}unidades_cnpjs.csv', sep=';', header=None, names=['Unidade', 'CNPJ'])

                    # Remover linhas vazias ou inválidas
                    df_unidades = df_unidades.dropna()

                    for _, row in df_unidades.iterrows():
                        unidade = str(row['Unidade']).strip()
                        cnpj = str(row['CNPJ']).strip()

                        if unidade and cnpj and unidade != 'nan':
                            self.unidades_info[unidade] = {
                                'cnpj': cnpj,
                                'cidade': 'N/A',
                                'estado': 'N/A'
                            }

                    print(f"✅ Unidades carregadas: {len(self.unidades_info)} unidades")
                    break
        except Exception as e:
            print(f"Aviso: Não foi possível carregar informações das unidades: {e}")

            # Se falhou, extrair unidades dos dados de produtos
            if self.df_produtos is not None:
                unidades_produtos = self.df_produtos['Unidade'].unique()
                for unidade in unidades_produtos:
                    if pd.notna(unidade):
                        self.unidades_info[unidade] = {
                            'cnpj': 'N/A',
                            'cidade': 'N/A',
                            'estado': 'N/A'
                        }
                print(f"✅ Unidades extraídas dos produtos: {len(self.unidades_info)} unidades")
    
    def processar_dados_produtos(self):
        """Processa dados de produtos com análises avançadas"""
        if self.df_produtos is not None:
            # 🎯 PADRONIZAR NOMES DOS PRODUTOS PRIMEIRO!
            print("🔄 Padronizando nomes dos produtos...")
            self.df_produtos['Produto_Padronizado'] = self.df_produtos['Descricao Produto'].apply(self.padronizar_nome_produto)

            self.df_produtos['Valor_Total_Numerico'] = self.df_produtos['Valor Total Item'].apply(self.converter_valor_brasileiro)
            self.df_produtos['Quantidade_Numerica'] = self.df_produtos['Quantidade'].apply(self.converter_quantidade_brasileira)
            self.df_produtos['Data_Emissao_Dt'] = pd.to_datetime(self.df_produtos['Data Emissao'], format='%d/%m/%Y', errors='coerce')

            # Calcular valor unitário
            self.df_produtos['Valor_Unitario'] = np.where(
                self.df_produtos['Quantidade_Numerica'] > 0,
                self.df_produtos['Valor_Total_Numerico'] / self.df_produtos['Quantidade_Numerica'],
                0
            )

            # Adicionar período para análises temporais
            self.df_produtos['Ano_Mes'] = self.df_produtos['Data_Emissao_Dt'].dt.to_period('M')

            print(f"✅ Produtos padronizados: {self.df_produtos['Produto_Padronizado'].nunique()} produtos únicos identificados")

    def processar_dados_faturamento(self):
        """Processa dados de faturamento geral com conversões necessárias"""
        if self.df_faturamento is not None:
            # Converter valores monetários
            if 'Valor Total NF' in self.df_faturamento.columns:
                self.df_faturamento['Valor_Numerico'] = self.df_faturamento['Valor Total NF'].apply(self.converter_valor_brasileiro)
            elif 'Valor Produtos' in self.df_faturamento.columns:
                self.df_faturamento['Valor_Numerico'] = self.df_faturamento['Valor Produtos'].apply(self.converter_valor_brasileiro)

            # Converter datas
            self.df_faturamento['Data_Emissao_Dt'] = pd.to_datetime(self.df_faturamento['Data Emissao'], format='%d/%m/%Y', errors='coerce')

            # Adicionar análises temporais
            self.df_faturamento['Ano_Mes'] = self.df_faturamento['Data_Emissao_Dt'].dt.to_period('M')
            self.df_faturamento['Trimestre'] = self.df_faturamento['Data_Emissao_Dt'].dt.quarter
            self.df_faturamento['Ano'] = self.df_faturamento['Data_Emissao_Dt'].dt.year

            # Calcular dias até vencimento se houver data de vencimento
            if 'Data Vencimento' in self.df_faturamento.columns:
                self.df_faturamento['Data_Vencimento_Dt'] = pd.to_datetime(self.df_faturamento['Data Vencimento'], format='%d/%m/%Y', errors='coerce')
                hoje = datetime.now()
                self.df_faturamento['Dias_Vencimento'] = (self.df_faturamento['Data_Vencimento_Dt'] - hoje).dt.days

                # Classificar status de vencimento
                self.df_faturamento['Status_Vencimento'] = self.df_faturamento['Dias_Vencimento'].apply(
                    lambda x: 'Vencido' if pd.notna(x) and x < 0 else
                             'Vence Hoje' if pd.notna(x) and x == 0 else
                             'Próximo (7 dias)' if pd.notna(x) and x <= 7 else
                             'Próximo (30 dias)' if pd.notna(x) and x <= 30 else
                             'Futuro' if pd.notna(x) else 'Sem Data'
                )

    # REMOVIDO: Métodos de processamento de boletos e impostos (não servem para nada conforme solicitado)

    def converter_quantidade_brasileira(self, quantidade):
        """Converte quantidade brasileira para float"""
        try:
            if pd.isna(quantidade) or quantidade == '':
                return 0.0
            quantidade_str = str(quantidade).replace('.', '').replace(',', '.')
            return float(quantidade_str)
        except:
            return 0.0
    
    def converter_valor_brasileiro(self, valor):
        """Converte valor brasileiro para float"""
        try:
            if pd.isna(valor) or valor == '':
                return 0.0
            valor_str = str(valor).replace('R$', '').replace(' ', '').replace('.', '').replace(',', '.')
            return float(valor_str)
        except:
            return 0.0

    def criar_mapeamento_produtos(self):
        """Cria mapeamento para padronizar nomes de produtos conforme tabela SantaClara"""
        return {
            # Mapeamento baseado na tabela fornecida
            'TRIAXIM': ['01 TRIAXIM 1 GRAMA', 'TRIAXIM', 'TRIAXIM 1G', 'TRIAXIM 1 GRAMA'],
            'ABRYSVO': ['ABRYSVO', 'ABRYSVO VACINA'],
            'ADACEL TRIPLICE': ['ADACEL TRIPLICE', 'ADACEL', 'TRIPLICE ACELULAR'],
            'AJOVY': ['AJOVY', 'AJOVY INJETAVEL'],
            'ANTIPNEUMOCOCICA - PREVENAR': ['ANTIPNEUMOCOCICA - PREVENAR', 'PREVENAR', 'PNEUMOCOCICA PREVENAR'],
            'ANTIPNEUMOCOCICA 15 VALENTE': ['ANTIPNEUMOCOCICA 15 VALENTE', 'PNEUMO 15', 'PNEUMOCOCICA 15V'],
            'ANTIPNEUMOCOCICA 20': ['ANTIPNEUMOCOCICA 20', 'PNEUMO 20', 'PNEUMOCOCICA 20V'],
            'ANTIPNEUMOCOCICA 23': ['ANTIPNEUMOCOCICA 23', 'PNEUMO 23', 'PNEUMOCOCICA 23V'],
            'AREXVY': ['AREXVY', 'AREXVY VACINA'],
            'BEYFORTUS 100MG': ['BEYFORTUS 100MG', 'BEYFORTUS 100', 'BEYFORTUS'],
            'BEYFORTUS 50MG': ['BEYFORTUS 50MG', 'BEYFORTUS 50'],
            'BRINCO': ['BRINCO', 'BRINCO IDENTIFICACAO'],
            'DENGUE': ['DENGUE', 'DENGUE VACINA', 'QDENGA'],
            'DENGUE MONODOSE': ['DENGUE MONODOSE', 'DENGUE DOSE UNICA'],
            'EFLUELDA': ['EFLUELDA', 'INFLUENZA EFLUELDA'],
            'FEBRE AMARELA': ['FEBRE AMARELA', 'FA', 'VACINA FEBRE AMARELA'],
            'FEBRE TIFOIDE': ['FEBRE TIFOIDE', 'TIFOIDE', 'VACINA TIFOIDE'],
            'FURO DE ORELHA': ['FURO DE ORELHA', 'PERFURACAO ORELHA'],
            'GRIPE 2023': ['GRIPE 2023', 'INFLUENZA 2023'],
            'GRIPE ABBOTT 2025': ['GRIPE ABBOTT 2025', 'INFLUENZA ABBOTT 2025'],
            'GRIPE COMBINADA': ['GRIPE COMBINADA', 'INFLUENZA COMBINADA'],
            'GRIPE COMBINADA MULTIDOSES': ['GRIPE COMBINADA MULTIDOSES', 'INFLUENZA MULTI'],
            'GRIPE COMBINADA- ABBOTT': ['GRIPE COMBINADA- ABBOTT', 'INFLUENZA ABBOTT'],
            'GRIPE INFLUENZA': ['GRIPE INFLUENZA', 'INFLUENZA'],
            'GRIPE PEDIATRICA SANOFI': ['GRIPE PEDIATRICA SANOFI', 'INFLUENZA PEDIATRICA'],
            'GRIPE TETRAVALENTE ABBOTT': ['GRIPE TETRAVALENTE ABBOTT', 'INFLUENZA TETRA ABBOTT'],
            'GRIPE TRIVALENTE': ['GRIPE TRIVALENTE', 'INFLUENZA TRIVALENTE'],
            'HEPATITE A + B ADULTO': ['HEPATITE A + B ADULTO', 'HEPATITE AB ADULTO'],
            'HEPATITE A ADULTO': ['HEPATITE A ADULTO', 'HEPATITE A'],
            'HEPATITE A INFANTIL': ['HEPATITE A INFANTIL', 'HEPATITE A PEDIATRICA'],
            'HEPATITE B INFANTIL': ['HEPATITE B INFANTIL', 'HEPATITE B PEDIATRICA'],
            'HEXAVALENTE': ['HEXAVALENTE', 'HEXA', 'VACINA HEXAVALENTE'],
            'HPV NONAVALENTE': ['HPV NONAVALENTE', 'HPV 9V', 'HPV GARDASIL 9'],
            'HPV QUADRIVALENTE': ['HPV QUADRIVALENTE', 'HPV 4V', 'HPV GARDASIL'],
            'HPV- 9': ['HPV- 9', 'HPV 9', 'HPV NONAVALENTE'],
            'MENACTRA': ['MENACTRA', 'MENINGOCOCICA ACWY'],
            'MENINGO B': ['MENINGO B', 'MENINGOCOCICA B', 'BEXSERO'],
            'MENINGO QUADRA ACWY': ['MENINGO QUADRA ACWY', 'MENINGOCOCICA ACWY'],
            'MENQUADFI': ['MENQUADFI', 'MENINGOCOCICA QUADRIVALENTE'],
            'MENVEO': ['MENVEO', 'MENINGOCOCICA ACWY'],
            'NIMENRIX': ['NIMENRIX', 'MENINGOCOCICA ACWY'],
            'PENTAVALENTE': ['PENTAVALENTE', 'PENTA', 'VACINA PENTAVALENTE'],
            'QDENGA': ['QDENGA', 'DENGUE', 'DENGUE QDENGA'],
            'REFORTRIX': ['REFORTRIX', 'TRIPLICE ACELULAR'],
            'REFORTRIX + IPV': ['REFORTRIX + IPV', 'TRIPLICE + POLIO'],
            'RHOPHYLAC MATERGAM': ['RHOPHYLAC MATERGAM', 'RHOPHYLAC'],
            'ROTAVIRUS': ['ROTAVIRUS', 'ROTA', 'VACINA ROTAVIRUS'],
            'SHINGRIX - HERPES ZOSTER': ['SHINGRIX - HERPES ZOSTER', 'SHINGRIX', 'HERPES ZOSTER'],
            'SYNAGYS 100 MG': ['SYNAGYS 100 MG', 'SYNAGYS 100', 'PALIVIZUMAB 100'],
            'TETRAVALENTE': ['TETRAVALENTE', 'TETRA', 'VACINA TETRAVALENTE'],
            'TETRAVIRAL': ['TETRAVIRAL', 'TETRA VIRAL'],
            'TETRAXIM': ['TETRAXIM', 'TETRA BACTERIANA'],
            'TRIPLICE VIRAL': ['TRIPLICE VIRAL', 'TRÍPLICE VIRAL', 'SCR'],
            'TRUMENBA - MENINGO B': ['TRUMENBA - MENINGO B', 'TRUMENBA', 'MENINGOCOCICA B'],
            'VARICELA': ['VARICELA', 'CATAPORA', 'VACINA VARICELA'],
            'VARICELA - MERCK': ['VARICELA - MERCK', 'VARICELA MERCK']
        }

    def padronizar_nome_produto(self, descricao_original):
        """Padroniza nome do produto conforme tabela SantaClara"""
        if pd.isna(descricao_original):
            return 'PRODUTO NÃO IDENTIFICADO'

        descricao = str(descricao_original).upper().strip()

        # Remover caracteres especiais e múltiplos espaços
        import re
        descricao = re.sub(r'[^\w\s+\-]', ' ', descricao)
        descricao = re.sub(r'\s+', ' ', descricao).strip()

        # Buscar correspondência no mapeamento
        mapeamento = self.criar_mapeamento_produtos()

        for produto_padrao, variacoes in mapeamento.items():
            for variacao in variacoes:
                # Verificar se a descrição contém a variação
                if variacao.upper() in descricao or descricao in variacao.upper():
                    return produto_padrao

                # Verificar correspondência parcial (palavras-chave)
                palavras_variacao = set(variacao.upper().split())
                palavras_descricao = set(descricao.split())

                # Se 70% das palavras da variação estão na descrição
                if len(palavras_variacao) > 0:
                    intersecao = len(palavras_variacao.intersection(palavras_descricao))
                    if intersecao / len(palavras_variacao) >= 0.7:
                        return produto_padrao

        # Se não encontrou correspondência, tentar identificar por palavras-chave principais
        palavras_chave = {
            'GRIPE': 'GRIPE INFLUENZA',
            'INFLUENZA': 'GRIPE INFLUENZA',
            'HPV': 'HPV QUADRIVALENTE',
            'HEPATITE': 'HEPATITE A ADULTO',
            'MENINGO': 'MENINGO B',
            'PNEUMO': 'ANTIPNEUMOCOCICA 20',
            'DENGUE': 'DENGUE',
            'VARICELA': 'VARICELA',
            'ROTAVIRUS': 'ROTAVIRUS',
            'FEBRE AMARELA': 'FEBRE AMARELA'
        }

        for palavra, produto in palavras_chave.items():
            if palavra in descricao:
                return produto

        # Se ainda não identificou, retornar descrição limpa
        return descricao[:50] + '...' if len(descricao) > 50 else descricao

    def get_top_produtos_padronizados(self, limit=10, unidade=None):
        """Retorna análise dos top produtos COMPRADOS usando nomes padronizados"""
        if self.df_produtos is None or 'Produto_Padronizado' not in self.df_produtos.columns:
            return {'labels': [], 'data': [], 'detalhes': []}

        df_temp = self.df_produtos.copy()

        # Filtrar por unidade se especificado
        if unidade and unidade != 'TODAS':
            df_temp = df_temp[df_temp['Unidade'] == unidade]

        if len(df_temp) == 0:
            return {'labels': [], 'data': [], 'detalhes': []}

        # Análise por produto padronizado - FOCO EM COMPRAS
        analise_produtos = df_temp.groupby('Produto_Padronizado').agg({
            'Valor_Total_Numerico': ['sum', 'count', 'mean'],
            'Quantidade_Numerica': 'sum',
            'Valor_Unitario': ['mean', 'min', 'max'],
            'Unidade': 'nunique',
            'Fornecedor': 'nunique',
            'Data_Emissao_Dt': ['min', 'max']  # Período de compras
        }).round(2)

        # Renomear colunas para refletir COMPRAS
        analise_produtos.columns = [
            'Valor_Total_Comprado', 'Qtd_Compras', 'Valor_Medio_Compra',
            'Quantidade_Total_Comprada', 'Preco_Medio_Unitario', 'Preco_Min_Unitario', 'Preco_Max_Unitario',
            'Qtd_Unidades_Compradoras', 'Qtd_Fornecedores', 'Primeira_Compra', 'Ultima_Compra'
        ]
        analise_produtos = analise_produtos.sort_values('Valor_Total_Comprado', ascending=False).head(limit)

        # Preparar dados para gráfico
        labels = [nome[:30] + "..." if len(nome) > 30 else nome for nome in analise_produtos.index]
        data = [float(valor) for valor in analise_produtos['Valor_Total_Comprado'].values]

        # Detalhes para tooltips - TERMINOLOGIA DE COMPRAS
        detalhes = []
        for produto in analise_produtos.index:
            info = analise_produtos.loc[produto]

            # Calcular variação de preço
            variacao_preco = 0
            if info['Preco_Min_Unitario'] > 0:
                variacao_preco = ((info['Preco_Max_Unitario'] - info['Preco_Min_Unitario']) / info['Preco_Min_Unitario'] * 100)

            detalhes.append({
                'produto': produto,
                'valor_total_comprado': info['Valor_Total_Comprado'],
                'qtd_compras': int(info['Qtd_Compras']),
                'quantidade_total_comprada': info['Quantidade_Total_Comprada'],
                'valor_medio_compra': info['Valor_Medio_Compra'],
                'preco_medio_unitario': info['Preco_Medio_Unitario'],
                'preco_min_unitario': info['Preco_Min_Unitario'],
                'preco_max_unitario': info['Preco_Max_Unitario'],
                'variacao_preco_percentual': round(variacao_preco, 2),
                'qtd_unidades_compradoras': int(info['Qtd_Unidades_Compradoras']),
                'qtd_fornecedores': int(info['Qtd_Fornecedores']),
                'primeira_compra': info['Primeira_Compra'].strftime('%d/%m/%Y') if pd.notna(info['Primeira_Compra']) else 'N/A',
                'ultima_compra': info['Ultima_Compra'].strftime('%d/%m/%Y') if pd.notna(info['Ultima_Compra']) else 'N/A'
            })

        return {
            'labels': labels,
            'data': data,
            'detalhes': detalhes
        }

    def get_analise_vacinas_compradas(self, unidade=None):
        """Análise específica de VACINAS compradas"""
        if self.df_produtos is None or 'Produto_Padronizado' not in self.df_produtos.columns:
            return {'erro': 'Dados não disponíveis'}

        df_temp = self.df_produtos.copy()

        # Filtrar por unidade se especificado
        if unidade and unidade != 'TODAS':
            df_temp = df_temp[df_temp['Unidade'] == unidade]

        # Identificar vacinas pelos nomes padronizados
        vacinas_keywords = [
            'GRIPE', 'INFLUENZA', 'HPV', 'HEPATITE', 'MENINGO', 'PNEUMO', 'ANTIPNEUMOCOCICA',
            'DENGUE', 'VARICELA', 'ROTAVIRUS', 'FEBRE AMARELA', 'FEBRE TIFOIDE', 'TRIPLICE',
            'TETRAVIRAL', 'TETRAXIM', 'PENTAVALENTE', 'HEXAVALENTE', 'SHINGRIX', 'HERPES ZOSTER',
            'ABRYSVO', 'AREXVY', 'BEYFORTUS', 'QDENGA', 'PREVENAR', 'GARDASIL', 'BEXSERO',
            'MENACTRA', 'MENVEO', 'NIMENRIX', 'MENQUADFI', 'TRUMENBA', 'SYNAGYS', 'RHOPHYLAC'
        ]

        # Filtrar apenas vacinas
        mask_vacinas = df_temp['Produto_Padronizado'].str.contains('|'.join(vacinas_keywords), case=False, na=False)
        df_vacinas = df_temp[mask_vacinas].copy()

        if len(df_vacinas) == 0:
            return {'erro': 'Nenhuma vacina encontrada'}

        # Análise detalhada de vacinas
        analise_vacinas = df_vacinas.groupby('Produto_Padronizado').agg({
            'Valor_Total_Numerico': ['sum', 'count', 'mean'],
            'Quantidade_Numerica': 'sum',
            'Valor_Unitario': ['mean', 'min', 'max'],
            'Unidade': 'nunique',
            'Fornecedor': 'nunique'
        }).round(2)

        analise_vacinas.columns = [
            'Valor_Total_Comprado', 'Qtd_Compras', 'Valor_Medio_Compra',
            'Doses_Compradas', 'Preco_Medio_Dose', 'Preco_Min_Dose', 'Preco_Max_Dose',
            'Qtd_Unidades', 'Qtd_Fornecedores'
        ]

        # Ordenar por valor total comprado
        analise_vacinas = analise_vacinas.sort_values('Valor_Total_Comprado', ascending=False)

        # Estatísticas gerais
        total_vacinas_compradas = analise_vacinas['Valor_Total_Comprado'].sum()
        total_doses_compradas = analise_vacinas['Doses_Compradas'].sum()
        tipos_vacinas = len(analise_vacinas)

        return {
            'total_vacinas_compradas': total_vacinas_compradas,
            'total_doses_compradas': total_doses_compradas,
            'tipos_vacinas': tipos_vacinas,
            'analise_detalhada': analise_vacinas.to_dict('index'),
            'top_10_vacinas': analise_vacinas.head(10).to_dict('index')
        }

    def get_analise_vencimentos_avancada(self, unidade=None, periodo_dias=30, fornecedor=None):
        """🚨 Análise avançada de vencimentos baseada nos dados de faturamento/produtos"""

        # Usar dados de produtos como base para análise de vencimentos de pagamentos
        if self.df_produtos is None or len(self.df_produtos) == 0:
            return self._criar_dados_exemplo_vencimentos()

        try:
            df_temp = self.df_produtos.copy()

            # Filtrar por unidade se especificado
            if unidade and unidade != 'TODAS':
                df_temp = df_temp[df_temp['Unidade'] == unidade]

            # Filtrar por fornecedor se especificado
            if fornecedor and fornecedor != 'TODOS':
                df_temp = df_temp[df_temp['Fornecedor'] == fornecedor]

            if len(df_temp) == 0:
                return self._criar_dados_exemplo_vencimentos()

            # Converter datas de emissão e calcular vencimentos de pagamento
            df_temp['Data_Emissao_Dt'] = pd.to_datetime(df_temp['Data Emissao'], format='%d/%m/%Y', errors='coerce')

            # Simular vencimentos de pagamento baseados na data de emissão
            # Assumindo prazo de pagamento de 30 dias após emissão
            df_temp['Data_Vencimento_Pagamento'] = df_temp['Data_Emissao_Dt'] + timedelta(days=30)

            hoje = datetime.now()

            # Análise por períodos de vencimento de PAGAMENTOS
            vencidos = len(df_temp[df_temp['Data_Vencimento_Pagamento'] < hoje])
            hoje_vence = len(df_temp[df_temp['Data_Vencimento_Pagamento'].dt.date == hoje.date()])
            semana = len(df_temp[(df_temp['Data_Vencimento_Pagamento'] >= hoje) &
                                (df_temp['Data_Vencimento_Pagamento'] <= hoje + timedelta(days=7))])
            mes = len(df_temp[(df_temp['Data_Vencimento_Pagamento'] >= hoje) &
                             (df_temp['Data_Vencimento_Pagamento'] <= hoje + timedelta(days=30))])

            # Análise por fornecedor (pagamentos críticos)
            fornecedores_criticos = df_temp[df_temp['Data_Vencimento_Pagamento'] < hoje + timedelta(days=7)]['Fornecedor'].value_counts().head(10)

            # Análise por unidade (pagamentos críticos)
            unidades_criticas = df_temp[df_temp['Data_Vencimento_Pagamento'] < hoje + timedelta(days=7)]['Unidade'].value_counts().head(10)

            # Valores por período usando valores reais dos produtos
            valor_vencidos = df_temp[df_temp['Data_Vencimento_Pagamento'] < hoje]['Valor_Total_Numerico'].sum()
            valor_semana = df_temp[(df_temp['Data_Vencimento_Pagamento'] >= hoje) &
                                  (df_temp['Data_Vencimento_Pagamento'] <= hoje + timedelta(days=7))]['Valor_Total_Numerico'].sum()
            valor_mes = df_temp[(df_temp['Data_Vencimento_Pagamento'] >= hoje) &
                               (df_temp['Data_Vencimento_Pagamento'] <= hoje + timedelta(days=30))]['Valor_Total_Numerico'].sum()

            return {
                'vencidos': vencidos,
                'hoje': hoje_vence,
                'semana': semana,
                'mes': mes,
                'total': len(df_temp),
                'valor_vencidos': float(valor_vencidos),
                'valor_semana': float(valor_semana),
                'valor_mes': float(valor_mes),
                'fornecedores_criticos': fornecedores_criticos.to_dict(),
                'unidades_criticas': unidades_criticas.to_dict(),
                'periodo_analisado': periodo_dias,
                'filtros': {
                    'unidade': unidade or 'TODAS',
                    'fornecedor': fornecedor or 'TODOS'
                },
                'fonte_dados': 'Baseado em compras com prazo de pagamento de 30 dias'
            }

        except Exception as e:
            print(f"Erro na análise de vencimentos: {e}")
            return self._criar_dados_exemplo_vencimentos()

    def _criar_dados_exemplo_vencimentos(self):
        """Criar dados de exemplo para demonstração do dashboard de vencimentos"""
        import random

        # Simular dados realistas baseados no volume de compras
        total_boletos = 45  # Baseado nos dados reais

        # Distribuição realista de vencimentos
        vencidos = random.randint(3, 8)
        hoje = random.randint(1, 3)
        semana = random.randint(5, 12)
        mes = random.randint(15, 25)

        # Valores simulados realistas
        valor_medio_boleto = 3500.0
        valor_vencidos = vencidos * valor_medio_boleto * random.uniform(0.8, 1.2)
        valor_semana = semana * valor_medio_boleto * random.uniform(0.8, 1.2)
        valor_mes = mes * valor_medio_boleto * random.uniform(0.8, 1.2)

        # Fornecedores críticos simulados
        fornecedores_criticos = {
            'FARMÁCIA POPULAR': random.randint(2, 4),
            'DISTRIBUIDORA MEDICAMENTOS': random.randint(1, 3),
            'LABORATÓRIO CENTRAL': random.randint(1, 2),
            'FORNECEDOR HOSPITALAR': random.randint(1, 2)
        }

        # Unidades críticas simuladas
        unidades_criticas = {
            'PATOS DE MINAS': random.randint(3, 6),
            'ARAGUARI': random.randint(2, 4),
            'VALINHOS': random.randint(1, 3),
            'MATRIZ': random.randint(2, 5)
        }

        return {
            'vencidos': vencidos,
            'hoje': hoje,
            'semana': semana,
            'mes': mes,
            'total': total_boletos,
            'valor_vencidos': valor_vencidos,
            'valor_semana': valor_semana,
            'valor_mes': valor_mes,
            'fornecedores_criticos': fornecedores_criticos,
            'unidades_criticas': unidades_criticas,
            'periodo_analisado': 30,
            'filtros': {
                'unidade': 'TODAS',
                'fornecedor': 'TODOS'
            },
            'exemplo': True  # Indicar que são dados de exemplo
        }

    def get_vencimentos_periodo(self, unidade=None, dias=30):
        """📅 Análise de vencimentos de pagamentos para período específico"""
        if self.df_produtos is None:
            return {'quantidade': 0, 'valor': 0}

        df_temp = self.df_produtos.copy()

        # Filtrar por unidade se especificado
        if unidade and unidade != 'TODAS':
            df_temp = df_temp[df_temp['Unidade'] == unidade]

        if len(df_temp) == 0:
            return {'quantidade': 0, 'valor': 0}

        # Converter datas e calcular vencimentos de pagamento
        df_temp['Data_Emissao_Dt'] = pd.to_datetime(df_temp['Data Emissao'], format='%d/%m/%Y', errors='coerce')
        df_temp['Data_Vencimento_Pagamento'] = df_temp['Data_Emissao_Dt'] + timedelta(days=30)
        hoje = datetime.now()

        # Filtrar por período
        periodo_df = df_temp[(df_temp['Data_Vencimento_Pagamento'] >= hoje) &
                            (df_temp['Data_Vencimento_Pagamento'] <= hoje + timedelta(days=dias))]

        quantidade = len(periodo_df)
        valor = periodo_df['Valor_Total_Numerico'].sum() if len(periodo_df) > 0 else 0

        return {
            'quantidade': quantidade,
            'valor': float(valor),
            'periodo_dias': dias
        }

    def get_cruzamento_compras_vencimentos(self, unidade=None):
        """📊 Cruzamento entre dados de compras e vencimentos de pagamentos"""
        resultado = {
            'compras': {'total': 0, 'valor': 0},
            'pagamentos_vencendo': {'total': 0, 'valor': 0},
            'correlacao': {},
            'insights': []
        }

        # Dados de compras
        if self.df_produtos is not None:
            df_compras = self.df_produtos.copy()
            if unidade and unidade != 'TODAS':
                df_compras = df_compras[df_compras['Unidade'] == unidade]

            resultado['compras']['total'] = len(df_compras)
            resultado['compras']['valor'] = float(df_compras['Valor_Total_Numerico'].sum()) if len(df_compras) > 0 else 0

            # Calcular vencimentos de pagamento baseados nas compras
            df_compras['Data_Emissao_Dt'] = pd.to_datetime(df_compras['Data Emissao'], format='%d/%m/%Y', errors='coerce')
            df_compras['Data_Vencimento_Pagamento'] = df_compras['Data_Emissao_Dt'] + timedelta(days=30)
            hoje = datetime.now()

            # Pagamentos que vencem nos próximos 30 dias
            pagamentos_vencendo = df_compras[(df_compras['Data_Vencimento_Pagamento'] >= hoje) &
                                            (df_compras['Data_Vencimento_Pagamento'] <= hoje + timedelta(days=30))]

            resultado['pagamentos_vencendo']['total'] = len(pagamentos_vencendo)
            resultado['pagamentos_vencendo']['valor'] = float(pagamentos_vencendo['Valor_Total_Numerico'].sum()) if len(pagamentos_vencendo) > 0 else 0

        # Insights baseados nos dados reais
        if resultado['compras']['valor'] > 0:
            ratio = resultado['pagamentos_vencendo']['valor'] / resultado['compras']['valor']
            resultado['insights'].append(f"📊 {ratio:.1%} do valor total de compras vence nos próximos 30 dias")

            if ratio > 0.5:
                resultado['insights'].append("⚠️ Alto volume de pagamentos concentrado no próximo mês")
            elif ratio < 0.2:
                resultado['insights'].append("✅ Fluxo de pagamentos bem distribuído")

            # Análise por fornecedor
            if len(df_compras) > 0:
                top_fornecedor = df_compras.groupby('Fornecedor')['Valor_Total_Numerico'].sum().idxmax()
                valor_top = df_compras.groupby('Fornecedor')['Valor_Total_Numerico'].sum().max()
                resultado['insights'].append(f"🚚 Maior fornecedor: {top_fornecedor} (R$ {valor_top:,.2f})")

        return resultado

    def get_analise_oscilacao_precos(self, produto_padronizado=None, unidade=None):
        """Analisa oscilação de preços de produtos padronizados ao longo do tempo"""
        if self.df_produtos is None or 'Produto_Padronizado' not in self.df_produtos.columns:
            return {'erro': 'Dados de produtos não disponíveis'}

        df_temp = self.df_produtos.copy()

        # Filtrar por unidade se especificado
        if unidade and unidade != 'TODAS':
            df_temp = df_temp[df_temp['Unidade'] == unidade]

        # Filtrar por produto se especificado
        if produto_padronizado:
            df_temp = df_temp[df_temp['Produto_Padronizado'] == produto_padronizado]

        if len(df_temp) == 0:
            return {'erro': 'Nenhum dado encontrado para os filtros especificados'}

        # Análise temporal de preços
        analise_temporal = df_temp.groupby(['Ano_Mes', 'Produto_Padronizado']).agg({
            'Valor_Unitario': ['mean', 'min', 'max', 'count'],
            'Quantidade_Numerica': 'sum'
        }).round(2)

        # Top produtos com maior oscilação
        oscilacao_produtos = df_temp.groupby('Produto_Padronizado')['Valor_Unitario'].agg(['min', 'max', 'std']).round(2)
        oscilacao_produtos['Oscilacao_Percentual'] = ((oscilacao_produtos['max'] - oscilacao_produtos['min']) / oscilacao_produtos['min'] * 100).round(2)
        oscilacao_produtos = oscilacao_produtos.sort_values('Oscilacao_Percentual', ascending=False).head(10)

        # Converter para formato JSON-serializável
        analise_temporal_dict = {}
        for key, value in analise_temporal.to_dict('index').items():
            # Converter tuplas em strings para chaves
            str_key = str(key) if isinstance(key, tuple) else key
            analise_temporal_dict[str_key] = value

        return {
            'analise_temporal': analise_temporal_dict,
            'top_oscilacao': oscilacao_produtos.to_dict('index'),
            'produto_selecionado': produto_padronizado
        }

    def get_resumo_executivo(self, unidade=None):
        """Retorna resumo executivo avançado baseado em produtos E faturamento"""
        resumo = {}

        # Dados de PRODUTOS (detalhado por item)
        if self.df_produtos is not None:
            df_prod = self.df_produtos.copy()
            if unidade and unidade != 'TODAS':
                df_prod = df_prod[df_prod['Unidade'] == unidade]

            resumo.update({
                'total_produtos': len(df_prod),
                'total_nfs_produtos': df_prod['Numero Nota'].nunique() if len(df_prod) > 0 else 0,
                'valor_total_produtos': df_prod['Valor_Total_Numerico'].sum() if len(df_prod) > 0 else 0,
                'fornecedores_produtos': df_prod['Fornecedor'].nunique() if len(df_prod) > 0 else 0,
            })

        # Dados de FATURAMENTO (consolidado por NF)
        if self.df_faturamento is not None:
            df_fat = self.df_faturamento.copy()
            if unidade and unidade != 'TODAS':
                df_fat = df_fat[df_fat['Unidade'] == unidade]

            resumo.update({
                'total_nfs_faturamento': len(df_fat),
                'total_nfs_unicas_faturamento': df_fat['Numero Nota'].nunique() if len(df_fat) > 0 else 0,
                'valor_total_faturamento': df_fat['Valor_Numerico'].sum() if len(df_fat) > 0 else 0,
                'fornecedores_faturamento': df_fat['Fornecedor'].nunique() if len(df_fat) > 0 else 0,
            })

        # Métricas consolidadas (usar o maior valor entre produtos e faturamento)
        total_nfs = max(
            resumo.get('total_nfs_produtos', 0),
            resumo.get('total_nfs_unicas_faturamento', 0)
        )
        valor_total = max(
            resumo.get('valor_total_produtos', 0),
            resumo.get('valor_total_faturamento', 0)
        )
        fornecedores_unicos = max(
            resumo.get('fornecedores_produtos', 0),
            resumo.get('fornecedores_faturamento', 0)
        )

        # Unidades ativas (de qualquer dataset disponível)
        unidades_ativas = 1 if unidade and unidade != 'TODAS' else len(self.unidades_info)

        # Métricas avançadas (usar dataset com mais dados)
        df_principal = None
        if self.df_faturamento is not None and len(self.df_faturamento) > 0:
            df_principal = self.df_faturamento.copy()
            if unidade and unidade != 'TODAS':
                df_principal = df_principal[df_principal['Unidade'] == unidade]
            valor_medio = df_principal['Valor_Numerico'].mean() if len(df_principal) > 0 else 0
            maior_valor = df_principal['Valor_Numerico'].max() if len(df_principal) > 0 else 0
            menor_valor = df_principal['Valor_Numerico'].min() if len(df_principal) > 0 else 0
        elif self.df_produtos is not None and len(self.df_produtos) > 0:
            df_principal = self.df_produtos.copy()
            if unidade and unidade != 'TODAS':
                df_principal = df_principal[df_principal['Unidade'] == unidade]
            valor_medio = df_principal['Valor_Total_Numerico'].mean() if len(df_principal) > 0 else 0
            maior_valor = df_principal['Valor_Total_Numerico'].max() if len(df_principal) > 0 else 0
            menor_valor = df_principal['Valor_Total_Numerico'].min() if len(df_principal) > 0 else 0
        else:
            valor_medio = maior_valor = menor_valor = 0

        # Análise temporal (últimos 30 dias vs anterior)
        variacao_percentual = 0
        valor_30_dias = 0
        if df_principal is not None and len(df_principal) > 0:
            hoje = datetime.now()
            ultimos_30_dias = df_principal[df_principal['Data_Emissao_Dt'] >= (hoje - timedelta(days=30))]
            periodo_anterior = df_principal[
                (df_principal['Data_Emissao_Dt'] >= (hoje - timedelta(days=60))) &
                (df_principal['Data_Emissao_Dt'] < (hoje - timedelta(days=30)))
            ]

            valor_col = 'Valor_Numerico' if 'Valor_Numerico' in df_principal.columns else 'Valor_Total_Numerico'
            valor_30_dias = ultimos_30_dias[valor_col].sum()
            valor_anterior = periodo_anterior[valor_col].sum()

            # Calcular variação percentual
            if valor_anterior > 0:
                variacao_percentual = ((valor_30_dias - valor_anterior) / valor_anterior) * 100

        # Análise de vencimentos (se disponível no faturamento)
        vencimentos_criticos = 0
        vencimentos_proximos = 0
        if self.df_faturamento is not None and 'Status_Vencimento' in self.df_faturamento.columns:
            df_venc = self.df_faturamento.copy()
            if unidade and unidade != 'TODAS':
                df_venc = df_venc[df_venc['Unidade'] == unidade]
            vencimentos_criticos = len(df_venc[df_venc['Status_Vencimento'].isin(['Vencido', 'Vence Hoje'])])
            vencimentos_proximos = len(df_venc[df_venc['Status_Vencimento'] == 'Próximo (7 dias)'])

        return {
            'total_nfs': total_nfs,
            'total_produtos': resumo.get('total_produtos', 0),
            'valor_total': valor_total,
            'unidades_ativas': unidades_ativas,
            'fornecedores_unicos': fornecedores_unicos,
            'valor_total_formatado': f"R$ {valor_total:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'valor_medio_nf': valor_medio,
            'valor_medio_formatado': f"R$ {valor_medio:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'maior_nf': maior_valor,
            'maior_nf_formatado': f"R$ {maior_valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'menor_nf': menor_valor,
            'menor_nf_formatado': f"R$ {menor_valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'valor_30_dias': valor_30_dias,
            'valor_30_dias_formatado': f"R$ {valor_30_dias:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'variacao_percentual': round(variacao_percentual, 2),
            'vencimentos_criticos': vencimentos_criticos,
            'vencimentos_proximos': vencimentos_proximos,
            'unidade_selecionada': unidade or 'TODAS'
        }
    
    def get_lista_unidades(self):
        """Retorna lista de unidades disponíveis"""
        if self.df_produtos is None:
            return [{'value': 'TODAS', 'label': 'TODAS AS UNIDADES'}]

        unidades = sorted([u for u in self.df_produtos['Unidade'].unique() if pd.notna(u)])
        return [{'value': 'TODAS', 'label': 'TODAS AS UNIDADES'}] + [
            {'value': unidade, 'label': unidade} for unidade in unidades
        ]

    def get_analise_detalhada_unidade(self, unidade):
        """Retorna análise detalhada específica de uma unidade baseada nos produtos"""
        if self.df_produtos is None or not unidade or unidade == 'TODAS':
            return {}

        df_unidade = self.df_produtos[self.df_produtos['Unidade'] == unidade].copy()

        if len(df_unidade) == 0:
            return {'erro': 'Unidade não encontrada ou sem dados'}

        # Informações básicas da unidade
        info_unidade = self.unidades_info.get(unidade, {})

        # Análise temporal detalhada
        analise_mensal = df_unidade.groupby('Ano_Mes').agg({
            'Valor_Total_Numerico': ['sum', 'count', 'mean'],
            'Fornecedor': 'nunique',
            'Numero Nota': 'nunique'
        }).round(2)

        # Top fornecedores da unidade
        top_fornecedores = df_unidade.groupby('Fornecedor')['Valor_Total_Numerico'].agg(['sum', 'count']).sort_values('sum', ascending=False).head(10)

        # Análise de sazonalidade
        df_unidade['Mes'] = df_unidade['Data_Emissao_Dt'].dt.month
        sazonalidade = df_unidade.groupby('Mes')['Valor_Total_Numerico'].mean()

        # Crescimento mensal
        valores_mensais = df_unidade.groupby('Ano_Mes')['Valor_Total_Numerico'].sum().sort_index()
        if len(valores_mensais) >= 2:
            crescimento = ((valores_mensais.iloc[-1] - valores_mensais.iloc[-2]) / valores_mensais.iloc[-2] * 100) if valores_mensais.iloc[-2] > 0 else 0
        else:
            crescimento = 0

        return {
            'unidade': unidade,
            'info_basica': info_unidade,
            'total_produtos': len(df_unidade),
            'total_nfs': df_unidade['Numero Nota'].nunique(),
            'valor_total': df_unidade['Valor_Total_Numerico'].sum(),
            'valor_medio': df_unidade['Valor_Total_Numerico'].mean(),
            'periodo_analise': f"{df_unidade['Data_Emissao_Dt'].min().strftime('%d/%m/%Y')} a {df_unidade['Data_Emissao_Dt'].max().strftime('%d/%m/%Y')}",
            'crescimento_mensal': round(crescimento, 2),
            'top_fornecedores': top_fornecedores.to_dict('index'),
            'sazonalidade': sazonalidade.to_dict(),
            'analise_mensal': analise_mensal.to_dict()
        }

    def get_fluxo_mensal(self, unidade=None):
        """Retorna dados de fluxo mensal baseado nos produtos"""
        if self.df_produtos is None:
            return {'labels': [], 'data': []}

        df_temp = self.df_produtos.copy()

        # Filtrar por unidade se especificado
        if unidade and unidade != 'TODAS':
            df_temp = df_temp[df_temp['Unidade'] == unidade]

        if len(df_temp) == 0:
            return {'labels': [], 'data': []}

        df_temp['Mes_Ano'] = df_temp['Data_Emissao_Dt'].dt.to_period('M')

        fluxo_mensal = df_temp.groupby('Mes_Ano')['Valor_Total_Numerico'].sum().sort_index()

        labels = [str(periodo) for periodo in fluxo_mensal.index[-12:]]  # Últimos 12 meses
        data = [float(valor) for valor in fluxo_mensal.values[-12:]]

        return {'labels': labels, 'data': data}
    
    def get_top_unidades(self, limit=10):
        """Retorna top unidades por valor baseado nos produtos"""
        if self.df_produtos is None:
            return {'labels': [], 'data': [], 'detalhes': []}

        # Análise por unidade com métricas adicionais
        analise_unidades = self.df_produtos.groupby('Unidade').agg({
            'Valor_Total_Numerico': ['sum', 'count', 'mean'],
            'Fornecedor': 'nunique',
            'Numero Nota': 'nunique'
        }).round(2)

        analise_unidades.columns = ['Valor_Total', 'Qtd_Produtos', 'Valor_Medio', 'Qtd_Fornecedores', 'Qtd_NFes']
        analise_unidades = analise_unidades.sort_values('Valor_Total', ascending=False).head(limit)

        # Preparar dados para gráfico
        labels = list(analise_unidades.index)
        data = [float(valor) for valor in analise_unidades['Valor_Total'].values]

        # Detalhes para tooltips
        detalhes = []
        for unidade in labels:
            info = analise_unidades.loc[unidade]
            detalhes.append({
                'unidade': unidade,
                'valor_total': info['Valor_Total'],
                'qtd_produtos': int(info['Qtd_Produtos']),
                'qtd_nfes': int(info['Qtd_NFes']),
                'valor_medio': info['Valor_Medio'],
                'qtd_fornecedores': int(info['Qtd_Fornecedores'])
            })

        return {
            'labels': labels,
            'data': data,
            'detalhes': detalhes
        }

    def get_top_fornecedores(self, limit=10, unidade=None):
        """Retorna top fornecedores por valor baseado nos produtos"""
        if self.df_produtos is None:
            return {'labels': [], 'data': [], 'detalhes': []}

        df_temp = self.df_produtos.copy()

        # Filtrar por unidade se especificado
        if unidade and unidade != 'TODAS':
            df_temp = df_temp[df_temp['Unidade'] == unidade]

        if len(df_temp) == 0:
            return {'labels': [], 'data': [], 'detalhes': []}

        # Análise por fornecedor
        analise_fornecedores = df_temp.groupby('Fornecedor').agg({
            'Valor_Total_Numerico': ['sum', 'count', 'mean'],
            'Unidade': 'nunique',
            'Numero Nota': 'nunique'
        }).round(2)

        analise_fornecedores.columns = ['Valor_Total', 'Qtd_Produtos', 'Valor_Medio', 'Qtd_Unidades', 'Qtd_NFes']
        analise_fornecedores = analise_fornecedores.sort_values('Valor_Total', ascending=False).head(limit)

        # Truncar nomes longos para labels
        labels = [nome[:25] + "..." if len(nome) > 25 else nome for nome in analise_fornecedores.index]
        data = [float(valor) for valor in analise_fornecedores['Valor_Total'].values]

        # Detalhes completos
        detalhes = []
        for fornecedor in analise_fornecedores.index:
            info = analise_fornecedores.loc[fornecedor]
            detalhes.append({
                'fornecedor': fornecedor,
                'valor_total': info['Valor_Total'],
                'qtd_produtos': int(info['Qtd_Produtos']),
                'qtd_nfes': int(info['Qtd_NFes']),
                'valor_medio': info['Valor_Medio'],
                'qtd_unidades': int(info['Qtd_Unidades'])
            })

        return {
            'labels': labels,
            'data': data,
            'detalhes': detalhes
        }
    
    # REMOVIDO: Método get_analise_vencimentos baseado em boletos (não servem para nada)

    # REMOVIDO: Método get_fluxo_caixa_projetado baseado em boletos (não servem para nada)
    
    def get_alertas_ativos(self):
        """Retorna alertas ativos do sistema"""
        try:
            if os.path.exists('alertas_sistema.json'):
                with open('alertas_sistema.json', 'r', encoding='utf-8') as f:
                    alertas = json.load(f)
                
                # Contar por prioridade
                alta = len([a for a in alertas if a.get('prioridade') == 'ALTA'])
                media = len([a for a in alertas if a.get('prioridade') == 'MEDIA'])
                baixa = len([a for a in alertas if a.get('prioridade') == 'BAIXA'])
                
                return {'alta': alta, 'media': media, 'baixa': baixa, 'total': len(alertas)}
            
            return {'alta': 0, 'media': 0, 'baixa': 0, 'total': 0}
        except:
            return {'alta': 0, 'media': 0, 'baixa': 0, 'total': 0}

# Instância global dos dados
dashboard_data = DashboardFinanceiroAvancado()

@app.route('/')
def index():
    """Página principal do dashboard SantaClara com produtos padronizados"""
    return render_template('dashboard_santaclara.html')

@app.route('/santaclara')
def dashboard_santaclara():
    """Dashboard com identidade visual SantaClara"""
    return render_template('dashboard_santaclara.html')

@app.route('/api/unidades')
def api_unidades():
    """API para lista de unidades"""
    return jsonify(dashboard_data.get_lista_unidades())

@app.route('/api/resumo')
def api_resumo():
    """API para dados do resumo executivo com filtro por unidade"""
    unidade = request.args.get('unidade', 'TODAS')
    dashboard_data.carregar_dados()  # Recarregar dados
    return jsonify(dashboard_data.get_resumo_executivo(unidade))

@app.route('/api/analise-unidade/<unidade>')
def api_analise_unidade(unidade):
    """API para análise detalhada de uma unidade específica"""
    return jsonify(dashboard_data.get_analise_detalhada_unidade(unidade))

@app.route('/api/fluxo-mensal')
def api_fluxo_mensal():
    """API para dados de fluxo mensal com filtro por unidade"""
    unidade = request.args.get('unidade', 'TODAS')
    return jsonify(dashboard_data.get_fluxo_mensal(unidade))

@app.route('/api/top-unidades')
def api_top_unidades():
    """API para top unidades com análise comparativa"""
    limit = request.args.get('limit', 10, type=int)
    return jsonify(dashboard_data.get_top_unidades(limit))

@app.route('/api/top-fornecedores')
def api_top_fornecedores():
    """API para top fornecedores com filtro por unidade"""
    limit = request.args.get('limit', 10, type=int)
    unidade = request.args.get('unidade', 'TODAS')
    return jsonify(dashboard_data.get_top_fornecedores(limit, unidade))

    # REMOVIDO: APIs baseadas em boletos (não servem para nada)

@app.route('/api/alertas')
def api_alertas():
    """API para alertas ativos"""
    return jsonify(dashboard_data.get_alertas_ativos())

@app.route('/api/refresh')
def api_refresh():
    """API para recarregar todos os dados"""
    dashboard_data.carregar_dados()
    return jsonify({'status': 'success', 'timestamp': datetime.now().isoformat()})

@app.route('/api/status')
def api_status():
    """API para status do sistema"""
    status = {
        'timestamp': datetime.now().isoformat(),
        'arquivos': {
            'produtos': os.path.exists('controle_produtos.xlsx'),
            'faturamento': os.path.exists('controle_faturamento_geral.xlsx'),
            # REMOVIDO: boletos (não servem para nada)
            'alertas': os.path.exists('alertas_sistema.json')
        },
        'registros': {
            'produtos': len(dashboard_data.df_produtos) if dashboard_data.df_produtos is not None else 0,
            'faturamento': len(dashboard_data.df_faturamento) if dashboard_data.df_faturamento is not None else 0,
            # REMOVIDO: boletos (não servem para nada)
        }
    }
    return jsonify(status)

# APIs Avançadas para Dashboard SantaClara

@app.route('/api/top-produtos')
def api_top_produtos():
    """Top produtos por valor e quantidade"""
    unidade = request.args.get('unidade', 'TODAS')
    limit = int(request.args.get('limit', 10))

    if dashboard_data.df_produtos is None:
        return jsonify({'labels': [], 'data': [], 'detalhes': []})

    df_temp = dashboard_data.df_produtos.copy()

    if unidade and unidade != 'TODAS':
        df_temp = df_temp[df_temp['Unidade'] == unidade]

    if len(df_temp) == 0:
        return jsonify({'labels': [], 'data': [], 'detalhes': []})

    # Análise por produto
    analise_produtos = df_temp.groupby('Descricao Produto').agg({
        'Valor_Total_Numerico': ['sum', 'count', 'mean'],
        'Quantidade_Numerica': 'sum',
        'Unidade': 'nunique'
    }).round(2)

    analise_produtos.columns = ['Valor_Total', 'Qtd_Compras', 'Valor_Medio', 'Qtd_Total', 'Qtd_Unidades']
    analise_produtos = analise_produtos.sort_values('Valor_Total', ascending=False).head(limit)

    labels = list(analise_produtos.index)
    data = [float(valor) for valor in analise_produtos['Valor_Total'].values]

    # Detalhes para tooltips
    detalhes = []
    for produto in labels:
        info = analise_produtos.loc[produto]
        detalhes.append({
            'produto': produto,
            'valor_total': info['Valor_Total'],
            'qtd_compras': int(info['Qtd_Compras']),
            'qtd_total': info['Qtd_Total'],
            'valor_medio': info['Valor_Medio'],
            'qtd_unidades': int(info['Qtd_Unidades'])
        })

    return jsonify({
        'labels': labels,
        'data': data,
        'detalhes': detalhes
    })

    # REMOVIDO: API de boletos-vencimento (não servem para nada)

# 🎯 NOVAS APIs PARA PRODUTOS PADRONIZADOS

@app.route('/api/top-produtos-padronizados')
def api_top_produtos_padronizados():
    """🎯 API para análise de produtos padronizados SantaClara"""
    unidade = request.args.get('unidade', 'TODAS')
    limit = request.args.get('limit', 10, type=int)
    return jsonify(dashboard_data.get_top_produtos_padronizados(limit=limit, unidade=unidade))

@app.route('/api/analise-oscilacao-precos')
def api_analise_oscilacao_precos():
    """🎯 API para análise de oscilação de preços de produtos padronizados"""
    unidade = request.args.get('unidade', 'TODAS')
    produto = request.args.get('produto', None)
    return jsonify(dashboard_data.get_analise_oscilacao_precos(produto_padronizado=produto, unidade=unidade))

@app.route('/api/analise-vacinas-compradas')
def api_analise_vacinas_compradas():
    """💉 API para análise específica de VACINAS compradas"""
    unidade = request.args.get('unidade', 'TODAS')
    return jsonify(dashboard_data.get_analise_vacinas_compradas(unidade=unidade))

# 🚨 NOVAS APIs PARA DASHBOARD DE VENCIMENTOS

@app.route('/vencimentos')
def dashboard_vencimentos():
    """Dashboard específico de vencimentos"""
    return render_template('dashboard_vencimentos.html')

@app.route('/api/analise-vencimentos-detalhada')
def api_analise_vencimentos_detalhada():
    """🚨 API para análise detalhada de vencimentos com cruzamento de dados"""
    unidade = request.args.get('unidade', 'TODAS')
    periodo = request.args.get('periodo', '30', type=int)
    fornecedor = request.args.get('fornecedor', 'TODOS')

    return jsonify(dashboard_data.get_analise_vencimentos_avancada(
        unidade=unidade,
        periodo_dias=periodo,
        fornecedor=fornecedor
    ))

@app.route('/api/vencimentos-por-periodo')
def api_vencimentos_por_periodo():
    """📅 API para análise de vencimentos por período específico"""
    unidade = request.args.get('unidade', 'TODAS')
    periodos = [30, 60, 90, 120, 150]

    resultado = {}
    for periodo in periodos:
        resultado[f'proximos_{periodo}_dias'] = dashboard_data.get_vencimentos_periodo(
            unidade=unidade,
            dias=periodo
        )

    return jsonify(resultado)

@app.route('/api/cruzamento-compras-vencimentos')
def api_cruzamento_compras_vencimentos():
    """📊 API para cruzamento entre dados de compras e vencimentos"""
    unidade = request.args.get('unidade', 'TODAS')

    return jsonify(dashboard_data.get_cruzamento_compras_vencimentos(unidade=unidade))

# 🎯 APIs PARA DASHBOARD DE ENTRADA DE PRODUTOS

@app.route('/entrada-produtos')
def dashboard_entrada_produtos():
    """Dashboard de entrada de produtos com fabricante, lote e validade"""
    return render_template('dashboard_entrada_produtos.html')

@app.route('/api/opcoes-filtros')
def api_opcoes_filtros():
    """API para opções de filtros do dashboard de entrada de produtos"""
    try:
        if dashboard_data.df_produtos is None or dashboard_data.df_produtos.empty:
            return jsonify({
                'unidades': ['TODAS'],
                'produtos': ['TODOS'],
                'fabricantes': []
            })

        df = dashboard_data.df_produtos

        # Obter listas únicas
        unidades = ['TODAS'] + sorted(df['Unidade'].dropna().unique().tolist())
        produtos = ['TODOS'] + sorted(df['Descricao Produto'].dropna().unique().tolist())

        # Fabricantes (nova coluna)
        fabricantes = []
        if 'Fabricante' in df.columns:
            fabricantes = sorted(df['Fabricante'].dropna().unique().tolist())

        return jsonify({
            'unidades': unidades,
            'produtos': produtos,
            'fabricantes': fabricantes
        })
    except Exception as e:
        print(f"❌ Erro em opcoes-filtros: {e}")
        return jsonify({'erro': str(e)})

@app.route('/api/kpis-produtos')
def api_kpis_produtos():
    """API para KPIs do dashboard de entrada de produtos"""
    try:
        unidade = request.args.get('unidade', 'TODAS')

        if dashboard_data.df_produtos is None or dashboard_data.df_produtos.empty:
            return jsonify({
                'total_produtos': 0,
                'produtos_unicos': 0,
                'valor_total': 0,
                'valor_total_formatado': 'R$ 0,00',
                'fornecedores_unicos': 0,
                'unidades_ativas': 0,
                'ticket_medio': 0,
                'ticket_medio_formatado': 'R$ 0,00'
            })

        df = dashboard_data.df_produtos.copy()

        # Aplicar filtro de unidade
        if unidade != 'TODAS':
            df = df[df['Unidade'] == unidade]

        # Calcular KPIs
        total_produtos = len(df)
        produtos_unicos = df['Descricao Produto'].nunique() if 'Descricao Produto' in df.columns else 0

        # Valor total
        valor_total = 0
        if 'Valor Total Item' in df.columns:
            valor_total = pd.to_numeric(df['Valor Total Item'], errors='coerce').sum()

        fornecedores_unicos = df['Fornecedor'].nunique() if 'Fornecedor' in df.columns else 0
        unidades_ativas = df['Unidade'].nunique() if 'Unidade' in df.columns else 0

        # Ticket médio
        ticket_medio = valor_total / total_produtos if total_produtos > 0 else 0

        return jsonify({
            'total_produtos': total_produtos,
            'produtos_unicos': produtos_unicos,
            'valor_total': valor_total,
            'valor_total_formatado': f'R$ {valor_total:,.2f}'.replace(',', 'X').replace('.', ',').replace('X', '.'),
            'fornecedores_unicos': fornecedores_unicos,
            'unidades_ativas': unidades_ativas,
            'ticket_medio': ticket_medio,
            'ticket_medio_formatado': f'R$ {ticket_medio:,.2f}'.replace(',', 'X').replace('.', ',').replace('X', '.')
        })
    except Exception as e:
        print(f"❌ Erro em kpis-produtos: {e}")
        return jsonify({'erro': str(e)})

@app.route('/api/lista-produtos')
def api_lista_produtos():
    """API para lista de produtos com fabricante, lote e validade"""
    try:
        if dashboard_data.df_produtos is None or dashboard_data.df_produtos.empty:
            return jsonify([])

        df = dashboard_data.df_produtos.copy()

        # Aplicar filtros
        unidade = request.args.get('unidade')
        if unidade and unidade != 'TODAS':
            df = df[df['Unidade'] == unidade]

        data_inicio = request.args.get('data_inicio')
        data_fim = request.args.get('data_fim')
        if data_inicio and data_fim:
            df['Data Emissao'] = pd.to_datetime(df['Data Emissao'], errors='coerce')
            df = df[(df['Data Emissao'] >= data_inicio) & (df['Data Emissao'] <= data_fim)]

        produto = request.args.get('produto')
        if produto:
            df = df[df['Descricao Produto'].str.contains(produto, case=False, na=False)]

        fornecedor = request.args.get('fornecedor')
        if fornecedor:
            df = df[df['Fornecedor'].str.contains(fornecedor, case=False, na=False)]

        numero_nf = request.args.get('numero_nf')
        if numero_nf:
            df = df[df['Numero NF'].astype(str).str.contains(numero_nf, case=False, na=False)]

        # 🆕 NOVOS FILTROS
        fabricante = request.args.get('fabricante')
        if fabricante and 'Fabricante' in df.columns:
            df = df[df['Fabricante'] == fabricante]

        lote = request.args.get('lote')
        if lote and 'Lote' in df.columns:
            df = df[df['Lote'].str.contains(lote, case=False, na=False)]

        validade = request.args.get('validade')
        if validade and 'Data Validade' in df.columns:
            hoje = datetime.now()
            df['Data Validade'] = pd.to_datetime(df['Data Validade'], format='%d/%m/%Y', errors='coerce')

            if validade == 'vencido':
                df = df[df['Data Validade'] < hoje]
            elif validade == '30dias':
                df = df[(df['Data Validade'] >= hoje) & (df['Data Validade'] <= hoje + timedelta(days=30))]
            elif validade == '90dias':
                df = df[(df['Data Validade'] >= hoje) & (df['Data Validade'] <= hoje + timedelta(days=90))]
            elif validade == '6meses':
                df = df[(df['Data Validade'] >= hoje) & (df['Data Validade'] <= hoje + timedelta(days=180))]
            elif validade == 'futuro':
                df = df[df['Data Validade'] > hoje + timedelta(days=180)]

        # Preparar dados para retorno
        produtos = []
        for _, row in df.head(1000).iterrows():  # Limitar a 1000 registros
            # Formatar data de emissão
            data_emissao = row.get('Data Emissao', '')
            if pd.notna(data_emissao):
                if hasattr(data_emissao, 'strftime'):
                    data_formatada = data_emissao.strftime('%d/%m/%Y')
                else:
                    data_formatada = str(data_emissao)
            else:
                data_formatada = ''

            # Formatar data de validade
            data_validade = row.get('Data Validade', '')
            if pd.notna(data_validade):
                if hasattr(data_validade, 'strftime'):
                    validade_formatada = data_validade.strftime('%d/%m/%Y')
                else:
                    validade_formatada = str(data_validade)
            else:
                validade_formatada = ''

            produto_data = {
                'unidade': str(row.get('Unidade', '')),
                'data': data_formatada,
                'numero_nf': str(row.get('Numero NF', '')),
                'fornecedor': str(row.get('Fornecedor', '')),
                'produto': str(row.get('Descricao Produto', '')),
                'produto_original': str(row.get('Descricao Produto', '')),
                'fabricante': str(row.get('Fabricante', '')),
                'lote': str(row.get('Lote', '')),
                'data_validade': validade_formatada,
                'quantidade': f"{pd.to_numeric(row.get('Quantidade', 0), errors='coerce'):,.0f}".replace(',', '.'),
                'valor_unitario_formatado': f"R$ {pd.to_numeric(row.get('Valor Unitario', 0), errors='coerce'):,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
                'valor_total_formatado': f"R$ {pd.to_numeric(row.get('Valor Total Item', 0), errors='coerce'):,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
            }

            produtos.append(produto_data)

        return jsonify(produtos)
    except Exception as e:
        print(f"❌ Erro em lista-produtos: {e}")
        return jsonify({'erro': str(e)})

@app.route('/api/top-produtos-quantidade')
def api_top_produtos_quantidade():
    """API para top produtos por quantidade"""
    try:
        unidade = request.args.get('unidade', 'TODAS')
        limit = request.args.get('limit', 10, type=int)

        if dashboard_data.df_produtos is None or dashboard_data.df_produtos.empty:
            return jsonify({'labels': [], 'data': []})

        df = dashboard_data.df_produtos.copy()

        if unidade != 'TODAS':
            df = df[df['Unidade'] == unidade]

        if 'Quantidade' in df.columns and 'Descricao Produto' in df.columns:
            top_produtos = df.groupby('Descricao Produto')['Quantidade'].sum().sort_values(ascending=False).head(limit)
            return jsonify({
                'labels': top_produtos.index.tolist(),
                'data': top_produtos.values.tolist()
            })

        return jsonify({'labels': [], 'data': []})
    except Exception as e:
        print(f"❌ Erro em top-produtos-quantidade: {e}")
        return jsonify({'erro': str(e)})

@app.route('/api/top-produtos-valor')
def api_top_produtos_valor():
    """API para top produtos por valor"""
    try:
        unidade = request.args.get('unidade', 'TODAS')
        limit = request.args.get('limit', 10, type=int)

        if dashboard_data.df_produtos is None or dashboard_data.df_produtos.empty:
            return jsonify({'labels': [], 'data': []})

        df = dashboard_data.df_produtos.copy()

        if unidade != 'TODAS':
            df = df[df['Unidade'] == unidade]

        if 'Valor Total Item' in df.columns and 'Descricao Produto' in df.columns:
            df['Valor Total Item'] = pd.to_numeric(df['Valor Total Item'], errors='coerce')
            top_produtos = df.groupby('Descricao Produto')['Valor Total Item'].sum().sort_values(ascending=False).head(limit)
            return jsonify({
                'labels': top_produtos.index.tolist(),
                'data': top_produtos.values.tolist()
            })

        return jsonify({'labels': [], 'data': []})
    except Exception as e:
        print(f"❌ Erro em top-produtos-valor: {e}")
        return jsonify({'erro': str(e)})

@app.route('/api/analise-precos')
def api_analise_precos():
    """API para análise de preços histórica"""
    try:
        produto = request.args.get('produto', 'TODOS')
        periodo = request.args.get('periodo', 12, type=int)

        if dashboard_data.df_produtos is None or dashboard_data.df_produtos.empty:
            return jsonify({'erro': 'Dados não disponíveis'})

        df = dashboard_data.df_produtos.copy()

        # Filtrar por produto se especificado
        if produto != 'TODOS':
            df = df[df['Descricao Produto'] == produto]

        # Filtrar por período
        if 'Data Emissao' in df.columns:
            df['Data Emissao'] = pd.to_datetime(df['Data Emissao'], errors='coerce')
            data_limite = datetime.now() - timedelta(days=periodo*30)
            df = df[df['Data Emissao'] >= data_limite]

        # Agrupar por mês e produto
        if 'Valor Unitario' in df.columns:
            df['Periodo'] = df['Data Emissao'].dt.to_period('M').astype(str)
            df['Valor Unitario'] = pd.to_numeric(df['Valor Unitario'], errors='coerce')

            dados_mensais = df.groupby(['Periodo', 'Descricao Produto'])['Valor Unitario'].mean().reset_index()
            dados_mensais.columns = ['Periodo', 'Produto_Padronizado', 'Preco_Medio']

            return jsonify({
                'dados_mensais': dados_mensais.to_dict('records')
            })

        return jsonify({'erro': 'Colunas necessárias não encontradas'})
    except Exception as e:
        print(f"❌ Erro em analise-precos: {e}")
        return jsonify({'erro': str(e)})

@app.route('/api/produtos-por-mes')
def api_produtos_por_mes():
    """API para produtos agrupados por mês"""
    try:
        ano = request.args.get('ano')

        if dashboard_data.df_produtos is None or dashboard_data.df_produtos.empty:
            return jsonify({})

        df = dashboard_data.df_produtos.copy()

        # Filtrar por ano se especificado
        if ano:
            df['Data Emissao'] = pd.to_datetime(df['Data Emissao'], errors='coerce')
            df = df[df['Data Emissao'].dt.year == int(ano)]

        # Agrupar por mês
        df['Mes'] = df['Data Emissao'].dt.to_period('M').astype(str)

        resultado = {}
        for mes in df['Mes'].unique():
            df_mes = df[df['Mes'] == mes]

            if 'Descricao Produto' in df_mes.columns:
                produtos_mes = df_mes.groupby('Descricao Produto').agg({
                    'Valor Unitario': 'mean',
                    'Quantidade': 'sum',
                    'Valor Total Item': 'sum',
                    'Descricao Produto': 'count'
                }).reset_index()

                produtos_mes.columns = ['produto', 'preco_medio', 'quantidade_total', 'valor_total', 'compras']

                # Formatar valores
                produtos_mes['preco_medio_formatado'] = produtos_mes['preco_medio'].apply(
                    lambda x: f"R$ {x:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
                )
                produtos_mes['valor_total_formatado'] = produtos_mes['valor_total'].apply(
                    lambda x: f"R$ {x:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
                )

                resultado[mes] = produtos_mes.to_dict('records')

        return jsonify(resultado)
    except Exception as e:
        print(f"❌ Erro em produtos-por-mes: {e}")
        return jsonify({'erro': str(e)})

if __name__ == '__main__':
    print("🌐 INICIANDO DASHBOARD WEB INTERATIVO")
    print("=" * 50)
    print("📊 Dashboard disponível em: http://localhost:5000")
    print("🔄 Atualização automática a cada 30 segundos")
    print("📱 Interface responsiva (desktop + mobile)")
    print("🎯 Dashboard Entrada Produtos: http://localhost:5000/entrada-produtos")
    print("=" * 50)

    app.run(debug=True, host='0.0.0.0', port=5000)
