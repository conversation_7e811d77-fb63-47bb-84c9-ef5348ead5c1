#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DASHBOARD FINANCEIRO AVANÇADO
Sistema web profissional para análise financeira detalhada por unidade
Desenvolvido para analistas financeiros com necessidades avançadas
"""

from flask import Flask, render_template, jsonify, request
import pandas as pd
import json
import os
from datetime import datetime, timedelta
import numpy as np
from collections import defaultdict

app = Flask(__name__)

class DashboardFinanceiroAvancado:
    def __init__(self):
        self.df_produtos = None
        self.df_boletos = None
        self.df_impostos = None
        self.unidades_info = {}
        self.carregar_dados()
        self.carregar_unidades_info()

    def carregar_dados(self):
        """Carrega dados das planilhas com caminhos corretos"""
        try:
            # Tentar carregar dos dados organizados primeiro
            base_paths = ['../dados/', 'dados/', './']

            for base_path in base_paths:
                try:
                    if os.path.exists(f'{base_path}controle_produtos.xlsx'):
                        self.df_produtos = pd.read_excel(f'{base_path}controle_produtos.xlsx')
                        self.processar_dados_produtos()
                        print(f"✅ Produtos carregados: {len(self.df_produtos)} registros")

                    if os.path.exists(f'{base_path}controle_faturamento_geral.xlsx'):
                        self.df_faturamento = pd.read_excel(f'{base_path}controle_faturamento_geral.xlsx')
                        self.processar_dados_faturamento()
                        print(f"✅ Faturamento carregado: {len(self.df_faturamento)} registros")

                    # Ignorar boletos - não são úteis para análise
                    # if os.path.exists(f'{base_path}controle_boletos.xlsx'):
                    #     self.df_boletos = pd.read_excel(f'{base_path}controle_boletos.xlsx')
                    #     self.processar_dados_boletos()
                    #     print(f"✅ Boletos carregados: {len(self.df_boletos)} registros")

                    if os.path.exists(f'{base_path}controle_impostos.xlsx'):
                        self.df_impostos = pd.read_excel(f'{base_path}controle_impostos.xlsx')
                        self.processar_dados_impostos()
                        print(f"✅ Impostos carregados: {len(self.df_impostos)} registros")

                    break

                except Exception as e:
                    continue



        except Exception as e:
            print(f"Erro ao carregar dados: {e}")



    def carregar_unidades_info(self):
        """Carrega informações das unidades"""
        try:
            base_paths = ['../dados/', 'dados/', './']

            for base_path in base_paths:
                if os.path.exists(f'{base_path}unidades_cnpjs.csv'):
                    # O arquivo tem formato UNIDADE;CNPJ em uma coluna
                    df_unidades = pd.read_csv(f'{base_path}unidades_cnpjs.csv', sep=';', header=None, names=['Unidade', 'CNPJ'])

                    # Remover linhas vazias ou inválidas
                    df_unidades = df_unidades.dropna()

                    for _, row in df_unidades.iterrows():
                        unidade = str(row['Unidade']).strip()
                        cnpj = str(row['CNPJ']).strip()

                        if unidade and cnpj and unidade != 'nan':
                            self.unidades_info[unidade] = {
                                'cnpj': cnpj,
                                'cidade': 'N/A',
                                'estado': 'N/A'
                            }

                    print(f"✅ Unidades carregadas: {len(self.unidades_info)} unidades")
                    break
        except Exception as e:
            print(f"Aviso: Não foi possível carregar informações das unidades: {e}")

            # Se falhou, extrair unidades dos dados de produtos
            if self.df_produtos is not None:
                unidades_produtos = self.df_produtos['Unidade'].unique()
                for unidade in unidades_produtos:
                    if pd.notna(unidade):
                        self.unidades_info[unidade] = {
                            'cnpj': 'N/A',
                            'cidade': 'N/A',
                            'estado': 'N/A'
                        }
                print(f"✅ Unidades extraídas dos produtos: {len(self.unidades_info)} unidades")
    
    def processar_dados_produtos(self):
        """Processa dados de produtos com análises avançadas"""
        if self.df_produtos is not None:
            self.df_produtos['Valor_Total_Numerico'] = self.df_produtos['Valor Total Item'].apply(self.converter_valor_brasileiro)
            self.df_produtos['Quantidade_Numerica'] = self.df_produtos['Quantidade'].apply(self.converter_quantidade_brasileira)
            self.df_produtos['Data_Emissao_Dt'] = pd.to_datetime(self.df_produtos['Data Emissao'], format='%d/%m/%Y', errors='coerce')

            # Calcular valor unitário
            self.df_produtos['Valor_Unitario'] = np.where(
                self.df_produtos['Quantidade_Numerica'] > 0,
                self.df_produtos['Valor_Total_Numerico'] / self.df_produtos['Quantidade_Numerica'],
                0
            )

            # Adicionar período para análises temporais
            self.df_produtos['Ano_Mes'] = self.df_produtos['Data_Emissao_Dt'].dt.to_period('M')

    def processar_dados_faturamento(self):
        """Processa dados de faturamento geral com conversões necessárias"""
        if self.df_faturamento is not None:
            # Converter valores monetários
            if 'Valor Total NF' in self.df_faturamento.columns:
                self.df_faturamento['Valor_Numerico'] = self.df_faturamento['Valor Total NF'].apply(self.converter_valor_brasileiro)
            elif 'Valor Produtos' in self.df_faturamento.columns:
                self.df_faturamento['Valor_Numerico'] = self.df_faturamento['Valor Produtos'].apply(self.converter_valor_brasileiro)

            # Converter datas
            self.df_faturamento['Data_Emissao_Dt'] = pd.to_datetime(self.df_faturamento['Data Emissao'], format='%d/%m/%Y', errors='coerce')

            # Adicionar análises temporais
            self.df_faturamento['Ano_Mes'] = self.df_faturamento['Data_Emissao_Dt'].dt.to_period('M')
            self.df_faturamento['Trimestre'] = self.df_faturamento['Data_Emissao_Dt'].dt.quarter
            self.df_faturamento['Ano'] = self.df_faturamento['Data_Emissao_Dt'].dt.year

            # Calcular dias até vencimento se houver data de vencimento
            if 'Data Vencimento' in self.df_faturamento.columns:
                self.df_faturamento['Data_Vencimento_Dt'] = pd.to_datetime(self.df_faturamento['Data Vencimento'], format='%d/%m/%Y', errors='coerce')
                hoje = datetime.now()
                self.df_faturamento['Dias_Vencimento'] = (self.df_faturamento['Data_Vencimento_Dt'] - hoje).dt.days

                # Classificar status de vencimento
                self.df_faturamento['Status_Vencimento'] = self.df_faturamento['Dias_Vencimento'].apply(
                    lambda x: 'Vencido' if pd.notna(x) and x < 0 else
                             'Vence Hoje' if pd.notna(x) and x == 0 else
                             'Próximo (7 dias)' if pd.notna(x) and x <= 7 else
                             'Próximo (30 dias)' if pd.notna(x) and x <= 30 else
                             'Futuro' if pd.notna(x) else 'Sem Data'
                )

    def processar_dados_boletos(self):
        """Processa dados de boletos com análises de fluxo de caixa"""
        if self.df_boletos is not None:
            self.df_boletos['Valor_Numerico'] = self.df_boletos['Valor Documento'].apply(self.converter_valor_brasileiro)
            self.df_boletos['Data_Vencimento_Dt'] = pd.to_datetime(self.df_boletos['Data Vencimento'], format='%d/%m/%Y', errors='coerce')

            # Adicionar análises temporais
            self.df_boletos['Ano_Mes'] = self.df_boletos['Data_Vencimento_Dt'].dt.to_period('M')

            # Calcular dias até vencimento
            hoje = datetime.now()
            self.df_boletos['Dias_Vencimento'] = (self.df_boletos['Data_Vencimento_Dt'] - hoje).dt.days

            # Classificar urgência
            self.df_boletos['Urgencia'] = self.df_boletos['Dias_Vencimento'].apply(
                lambda x: 'Crítico' if x <= 3 else
                         'Urgente' if x <= 7 else
                         'Atenção' if x <= 15 else
                         'Normal'
            )

    def processar_dados_impostos(self):
        """Processa dados de impostos"""
        if self.df_impostos is not None:
            # Assumindo estrutura similar aos outros DataFrames
            if 'Valor' in self.df_impostos.columns:
                self.df_impostos['Valor_Numerico'] = self.df_impostos['Valor'].apply(self.converter_valor_brasileiro)
            if 'Data Vencimento' in self.df_impostos.columns:
                self.df_impostos['Data_Vencimento_Dt'] = pd.to_datetime(self.df_impostos['Data Vencimento'], format='%d/%m/%Y', errors='coerce')

    def converter_quantidade_brasileira(self, quantidade):
        """Converte quantidade brasileira para float"""
        try:
            if pd.isna(quantidade) or quantidade == '':
                return 0.0
            quantidade_str = str(quantidade).replace('.', '').replace(',', '.')
            return float(quantidade_str)
        except:
            return 0.0
    
    def converter_valor_brasileiro(self, valor):
        """Converte valor brasileiro para float"""
        try:
            if pd.isna(valor) or valor == '':
                return 0.0
            valor_str = str(valor).replace('R$', '').replace(' ', '').replace('.', '').replace(',', '.')
            return float(valor_str)
        except:
            return 0.0
    
    def get_resumo_executivo(self, unidade=None):
        """Retorna resumo executivo avançado baseado em produtos E faturamento"""
        resumo = {}

        # Dados de PRODUTOS (detalhado por item)
        if self.df_produtos is not None:
            df_prod = self.df_produtos.copy()
            if unidade and unidade != 'TODAS':
                df_prod = df_prod[df_prod['Unidade'] == unidade]

            resumo.update({
                'total_produtos': len(df_prod),
                'total_nfs_produtos': df_prod['Numero Nota'].nunique() if len(df_prod) > 0 else 0,
                'valor_total_produtos': df_prod['Valor_Total_Numerico'].sum() if len(df_prod) > 0 else 0,
                'fornecedores_produtos': df_prod['Fornecedor'].nunique() if len(df_prod) > 0 else 0,
            })

        # Dados de FATURAMENTO (consolidado por NF)
        if self.df_faturamento is not None:
            df_fat = self.df_faturamento.copy()
            if unidade and unidade != 'TODAS':
                df_fat = df_fat[df_fat['Unidade'] == unidade]

            resumo.update({
                'total_nfs_faturamento': len(df_fat),
                'total_nfs_unicas_faturamento': df_fat['Numero Nota'].nunique() if len(df_fat) > 0 else 0,
                'valor_total_faturamento': df_fat['Valor_Numerico'].sum() if len(df_fat) > 0 else 0,
                'fornecedores_faturamento': df_fat['Fornecedor'].nunique() if len(df_fat) > 0 else 0,
            })

        # Métricas consolidadas (usar o maior valor entre produtos e faturamento)
        total_nfs = max(
            resumo.get('total_nfs_produtos', 0),
            resumo.get('total_nfs_unicas_faturamento', 0)
        )
        valor_total = max(
            resumo.get('valor_total_produtos', 0),
            resumo.get('valor_total_faturamento', 0)
        )
        fornecedores_unicos = max(
            resumo.get('fornecedores_produtos', 0),
            resumo.get('fornecedores_faturamento', 0)
        )

        # Unidades ativas (de qualquer dataset disponível)
        unidades_ativas = 1 if unidade and unidade != 'TODAS' else len(self.unidades_info)

        # Métricas avançadas (usar dataset com mais dados)
        df_principal = None
        if self.df_faturamento is not None and len(self.df_faturamento) > 0:
            df_principal = self.df_faturamento.copy()
            if unidade and unidade != 'TODAS':
                df_principal = df_principal[df_principal['Unidade'] == unidade]
            valor_medio = df_principal['Valor_Numerico'].mean() if len(df_principal) > 0 else 0
            maior_valor = df_principal['Valor_Numerico'].max() if len(df_principal) > 0 else 0
            menor_valor = df_principal['Valor_Numerico'].min() if len(df_principal) > 0 else 0
        elif self.df_produtos is not None and len(self.df_produtos) > 0:
            df_principal = self.df_produtos.copy()
            if unidade and unidade != 'TODAS':
                df_principal = df_principal[df_principal['Unidade'] == unidade]
            valor_medio = df_principal['Valor_Total_Numerico'].mean() if len(df_principal) > 0 else 0
            maior_valor = df_principal['Valor_Total_Numerico'].max() if len(df_principal) > 0 else 0
            menor_valor = df_principal['Valor_Total_Numerico'].min() if len(df_principal) > 0 else 0
        else:
            valor_medio = maior_valor = menor_valor = 0

        # Análise temporal (últimos 30 dias vs anterior)
        variacao_percentual = 0
        valor_30_dias = 0
        if df_principal is not None and len(df_principal) > 0:
            hoje = datetime.now()
            ultimos_30_dias = df_principal[df_principal['Data_Emissao_Dt'] >= (hoje - timedelta(days=30))]
            periodo_anterior = df_principal[
                (df_principal['Data_Emissao_Dt'] >= (hoje - timedelta(days=60))) &
                (df_principal['Data_Emissao_Dt'] < (hoje - timedelta(days=30)))
            ]

            valor_col = 'Valor_Numerico' if 'Valor_Numerico' in df_principal.columns else 'Valor_Total_Numerico'
            valor_30_dias = ultimos_30_dias[valor_col].sum()
            valor_anterior = periodo_anterior[valor_col].sum()

            # Calcular variação percentual
            if valor_anterior > 0:
                variacao_percentual = ((valor_30_dias - valor_anterior) / valor_anterior) * 100

        # Análise de vencimentos (se disponível no faturamento)
        vencimentos_criticos = 0
        vencimentos_proximos = 0
        if self.df_faturamento is not None and 'Status_Vencimento' in self.df_faturamento.columns:
            df_venc = self.df_faturamento.copy()
            if unidade and unidade != 'TODAS':
                df_venc = df_venc[df_venc['Unidade'] == unidade]
            vencimentos_criticos = len(df_venc[df_venc['Status_Vencimento'].isin(['Vencido', 'Vence Hoje'])])
            vencimentos_proximos = len(df_venc[df_venc['Status_Vencimento'] == 'Próximo (7 dias)'])

        return {
            'total_nfs': total_nfs,
            'total_produtos': total_produtos,
            'valor_total': valor_total,
            'unidades_ativas': unidades_ativas,
            'fornecedores_unicos': fornecedores_unicos,
            'valor_total_formatado': f"R$ {valor_total:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'valor_medio_nf': valor_medio_produto,
            'valor_medio_formatado': f"R$ {valor_medio_produto:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'maior_nf': maior_produto,
            'maior_nf_formatado': f"R$ {maior_produto:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'menor_nf': menor_produto,
            'menor_nf_formatado': f"R$ {menor_produto:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'valor_30_dias': valor_30_dias,
            'valor_30_dias_formatado': f"R$ {valor_30_dias:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
            'variacao_percentual': round(variacao_percentual, 2),
            'vencimentos_criticos': vencimentos_criticos,
            'vencimentos_proximos': vencimentos_proximos,
            'unidade_selecionada': unidade or 'TODAS'
        }
    
    def get_lista_unidades(self):
        """Retorna lista de unidades disponíveis"""
        if self.df_produtos is None:
            return [{'value': 'TODAS', 'label': 'TODAS AS UNIDADES'}]

        unidades = sorted([u for u in self.df_produtos['Unidade'].unique() if pd.notna(u)])
        return [{'value': 'TODAS', 'label': 'TODAS AS UNIDADES'}] + [
            {'value': unidade, 'label': unidade} for unidade in unidades
        ]

    def get_analise_detalhada_unidade(self, unidade):
        """Retorna análise detalhada específica de uma unidade baseada nos produtos"""
        if self.df_produtos is None or not unidade or unidade == 'TODAS':
            return {}

        df_unidade = self.df_produtos[self.df_produtos['Unidade'] == unidade].copy()

        if len(df_unidade) == 0:
            return {'erro': 'Unidade não encontrada ou sem dados'}

        # Informações básicas da unidade
        info_unidade = self.unidades_info.get(unidade, {})

        # Análise temporal detalhada
        analise_mensal = df_unidade.groupby('Ano_Mes').agg({
            'Valor_Total_Numerico': ['sum', 'count', 'mean'],
            'Fornecedor': 'nunique',
            'Numero Nota': 'nunique'
        }).round(2)

        # Top fornecedores da unidade
        top_fornecedores = df_unidade.groupby('Fornecedor')['Valor_Total_Numerico'].agg(['sum', 'count']).sort_values('sum', ascending=False).head(10)

        # Análise de sazonalidade
        df_unidade['Mes'] = df_unidade['Data_Emissao_Dt'].dt.month
        sazonalidade = df_unidade.groupby('Mes')['Valor_Total_Numerico'].mean()

        # Crescimento mensal
        valores_mensais = df_unidade.groupby('Ano_Mes')['Valor_Total_Numerico'].sum().sort_index()
        if len(valores_mensais) >= 2:
            crescimento = ((valores_mensais.iloc[-1] - valores_mensais.iloc[-2]) / valores_mensais.iloc[-2] * 100) if valores_mensais.iloc[-2] > 0 else 0
        else:
            crescimento = 0

        return {
            'unidade': unidade,
            'info_basica': info_unidade,
            'total_produtos': len(df_unidade),
            'total_nfs': df_unidade['Numero Nota'].nunique(),
            'valor_total': df_unidade['Valor_Total_Numerico'].sum(),
            'valor_medio': df_unidade['Valor_Total_Numerico'].mean(),
            'periodo_analise': f"{df_unidade['Data_Emissao_Dt'].min().strftime('%d/%m/%Y')} a {df_unidade['Data_Emissao_Dt'].max().strftime('%d/%m/%Y')}",
            'crescimento_mensal': round(crescimento, 2),
            'top_fornecedores': top_fornecedores.to_dict('index'),
            'sazonalidade': sazonalidade.to_dict(),
            'analise_mensal': analise_mensal.to_dict()
        }

    def get_fluxo_mensal(self, unidade=None):
        """Retorna dados de fluxo mensal baseado nos produtos"""
        if self.df_produtos is None:
            return {'labels': [], 'data': []}

        df_temp = self.df_produtos.copy()

        # Filtrar por unidade se especificado
        if unidade and unidade != 'TODAS':
            df_temp = df_temp[df_temp['Unidade'] == unidade]

        if len(df_temp) == 0:
            return {'labels': [], 'data': []}

        df_temp['Mes_Ano'] = df_temp['Data_Emissao_Dt'].dt.to_period('M')

        fluxo_mensal = df_temp.groupby('Mes_Ano')['Valor_Total_Numerico'].sum().sort_index()

        labels = [str(periodo) for periodo in fluxo_mensal.index[-12:]]  # Últimos 12 meses
        data = [float(valor) for valor in fluxo_mensal.values[-12:]]

        return {'labels': labels, 'data': data}
    
    def get_top_unidades(self, limit=10):
        """Retorna top unidades por valor baseado nos produtos"""
        if self.df_produtos is None:
            return {'labels': [], 'data': [], 'detalhes': []}

        # Análise por unidade com métricas adicionais
        analise_unidades = self.df_produtos.groupby('Unidade').agg({
            'Valor_Total_Numerico': ['sum', 'count', 'mean'],
            'Fornecedor': 'nunique',
            'Numero Nota': 'nunique'
        }).round(2)

        analise_unidades.columns = ['Valor_Total', 'Qtd_Produtos', 'Valor_Medio', 'Qtd_Fornecedores', 'Qtd_NFes']
        analise_unidades = analise_unidades.sort_values('Valor_Total', ascending=False).head(limit)

        # Preparar dados para gráfico
        labels = list(analise_unidades.index)
        data = [float(valor) for valor in analise_unidades['Valor_Total'].values]

        # Detalhes para tooltips
        detalhes = []
        for unidade in labels:
            info = analise_unidades.loc[unidade]
            detalhes.append({
                'unidade': unidade,
                'valor_total': info['Valor_Total'],
                'qtd_produtos': int(info['Qtd_Produtos']),
                'qtd_nfes': int(info['Qtd_NFes']),
                'valor_medio': info['Valor_Medio'],
                'qtd_fornecedores': int(info['Qtd_Fornecedores'])
            })

        return {
            'labels': labels,
            'data': data,
            'detalhes': detalhes
        }

    def get_top_fornecedores(self, limit=10, unidade=None):
        """Retorna top fornecedores por valor baseado nos produtos"""
        if self.df_produtos is None:
            return {'labels': [], 'data': [], 'detalhes': []}

        df_temp = self.df_produtos.copy()

        # Filtrar por unidade se especificado
        if unidade and unidade != 'TODAS':
            df_temp = df_temp[df_temp['Unidade'] == unidade]

        if len(df_temp) == 0:
            return {'labels': [], 'data': [], 'detalhes': []}

        # Análise por fornecedor
        analise_fornecedores = df_temp.groupby('Fornecedor').agg({
            'Valor_Total_Numerico': ['sum', 'count', 'mean'],
            'Unidade': 'nunique',
            'Numero Nota': 'nunique'
        }).round(2)

        analise_fornecedores.columns = ['Valor_Total', 'Qtd_Produtos', 'Valor_Medio', 'Qtd_Unidades', 'Qtd_NFes']
        analise_fornecedores = analise_fornecedores.sort_values('Valor_Total', ascending=False).head(limit)

        # Truncar nomes longos para labels
        labels = [nome[:25] + "..." if len(nome) > 25 else nome for nome in analise_fornecedores.index]
        data = [float(valor) for valor in analise_fornecedores['Valor_Total'].values]

        # Detalhes completos
        detalhes = []
        for fornecedor in analise_fornecedores.index:
            info = analise_fornecedores.loc[fornecedor]
            detalhes.append({
                'fornecedor': fornecedor,
                'valor_total': info['Valor_Total'],
                'qtd_produtos': int(info['Qtd_Produtos']),
                'qtd_nfes': int(info['Qtd_NFes']),
                'valor_medio': info['Valor_Medio'],
                'qtd_unidades': int(info['Qtd_Unidades'])
            })

        return {
            'labels': labels,
            'data': data,
            'detalhes': detalhes
        }
    
    def get_analise_vencimentos(self, unidade=None):
        """Retorna análise detalhada de vencimentos baseada nos boletos"""
        if self.df_boletos is None:
            return {'labels': [], 'data': [], 'valores': [], 'detalhes': {}}

        df_temp = self.df_boletos.copy()

        # Filtrar por unidade se especificado (se a coluna existir)
        if unidade and unidade != 'TODAS' and 'Unidade' in df_temp.columns:
            df_temp = df_temp[df_temp['Unidade'] == unidade]

        if len(df_temp) == 0:
            return {'labels': [], 'data': [], 'valores': [], 'detalhes': {}}

        df_venc = df_temp[df_temp['Data_Vencimento_Dt'].notna()].copy()

        # Análise por status de vencimento
        analise_status = df_venc.groupby('Urgencia').agg({
            'Valor_Numerico': ['sum', 'count']
        }).round(2)

        analise_status.columns = ['Valor_Total', 'Quantidade']

        # Preparar dados para gráfico
        labels = list(analise_status.index)
        data = [int(qtd) for qtd in analise_status['Quantidade'].values]
        valores = [float(valor) for valor in analise_status['Valor_Total'].values]

        # Análise detalhada por períodos específicos
        hoje = datetime.now()
        detalhes = {}

        periodos = {
            'vencido': df_venc[df_venc['Dias_Vencimento'] < 0],
            'hoje': df_venc[df_venc['Dias_Vencimento'] == 0],
            '7_dias': df_venc[(df_venc['Dias_Vencimento'] > 0) & (df_venc['Dias_Vencimento'] <= 7)],
            '30_dias': df_venc[(df_venc['Dias_Vencimento'] > 7) & (df_venc['Dias_Vencimento'] <= 30)],
            'futuro': df_venc[df_venc['Dias_Vencimento'] > 30]
        }

        for periodo, df_periodo in periodos.items():
            if len(df_periodo) > 0:
                detalhes[periodo] = {
                    'quantidade': len(df_periodo),
                    'valor_total': df_periodo['Valor_Numerico'].sum(),
                    'valor_formatado': f"R$ {df_periodo['Valor_Numerico'].sum():,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'),
                    'maior_valor': df_periodo['Valor_Numerico'].max(),
                    'fornecedores': df_periodo['Fornecedor'].nunique()
                }
            else:
                detalhes[periodo] = {
                    'quantidade': 0,
                    'valor_total': 0,
                    'valor_formatado': 'R$ 0,00',
                    'maior_valor': 0,
                    'fornecedores': 0
                }

        return {
            'labels': labels,
            'data': data,
            'valores': valores,
            'detalhes': detalhes
        }

    def get_fluxo_caixa_projetado(self, unidade=None, dias=90):
        """Retorna projeção de fluxo de caixa baseada apenas nos boletos"""
        if self.df_boletos is None:
            return {'labels': [], 'entradas': [], 'saidas': []}

        hoje = datetime.now()
        data_limite = hoje + timedelta(days=dias)

        # Preparar dados de entradas (simuladas baseadas no histórico de produtos)
        entradas_por_dia = defaultdict(float)
        if self.df_produtos is not None:
            df_prod = self.df_produtos.copy()
            if unidade and unidade != 'TODAS':
                df_prod = df_prod[df_prod['Unidade'] == unidade]

            # Calcular média diária de produtos dos últimos 30 dias
            ultimos_30_dias = df_prod[df_prod['Data_Emissao_Dt'] >= (hoje - timedelta(days=30))]
            if len(ultimos_30_dias) > 0:
                valor_medio_diario = ultimos_30_dias['Valor_Total_Numerico'].sum() / 30

                # Projetar entradas futuras baseadas na média
                for i in range(dias):
                    data_atual = hoje + timedelta(days=i)
                    data_str = data_atual.strftime('%Y-%m-%d')
                    entradas_por_dia[data_str] = valor_medio_diario

        # Preparar dados de saídas (boletos)
        saidas_por_dia = defaultdict(float)
        df_bol = self.df_boletos.copy()
        if unidade and unidade != 'TODAS' and 'Unidade' in df_bol.columns:
            df_bol = df_bol[df_bol['Unidade'] == unidade]

        df_bol_futuro = df_bol[
            (df_bol['Data_Vencimento_Dt'] >= hoje) &
            (df_bol['Data_Vencimento_Dt'] <= data_limite)
        ]

        for _, row in df_bol_futuro.iterrows():
            data_str = row['Data_Vencimento_Dt'].strftime('%Y-%m-%d')
            saidas_por_dia[data_str] += row['Valor_Numerico']

        # Gerar série temporal
        labels = []
        entradas = []
        saidas = []

        for i in range(dias):
            data_atual = hoje + timedelta(days=i)
            data_str = data_atual.strftime('%Y-%m-%d')
            label = data_atual.strftime('%d/%m')

            labels.append(label)
            entradas.append(entradas_por_dia.get(data_str, 0))
            saidas.append(saidas_por_dia.get(data_str, 0))

        return {
            'labels': labels,
            'entradas': entradas,
            'saidas': saidas
        }
    
    def get_alertas_ativos(self):
        """Retorna alertas ativos do sistema"""
        try:
            if os.path.exists('alertas_sistema.json'):
                with open('alertas_sistema.json', 'r', encoding='utf-8') as f:
                    alertas = json.load(f)
                
                # Contar por prioridade
                alta = len([a for a in alertas if a.get('prioridade') == 'ALTA'])
                media = len([a for a in alertas if a.get('prioridade') == 'MEDIA'])
                baixa = len([a for a in alertas if a.get('prioridade') == 'BAIXA'])
                
                return {'alta': alta, 'media': media, 'baixa': baixa, 'total': len(alertas)}
            
            return {'alta': 0, 'media': 0, 'baixa': 0, 'total': 0}
        except:
            return {'alta': 0, 'media': 0, 'baixa': 0, 'total': 0}

# Instância global dos dados
dashboard_data = DashboardFinanceiroAvancado()

@app.route('/')
def index():
    """Página principal do dashboard avançado"""
    return render_template('dashboard_avancado.html')

@app.route('/santaclara')
def dashboard_santaclara():
    """Dashboard com identidade visual SantaClara"""
    return render_template('dashboard_santaclara.html')

@app.route('/api/unidades')
def api_unidades():
    """API para lista de unidades"""
    return jsonify(dashboard_data.get_lista_unidades())

@app.route('/api/resumo')
def api_resumo():
    """API para dados do resumo executivo com filtro por unidade"""
    unidade = request.args.get('unidade', 'TODAS')
    dashboard_data.carregar_dados()  # Recarregar dados
    return jsonify(dashboard_data.get_resumo_executivo(unidade))

@app.route('/api/analise-unidade/<unidade>')
def api_analise_unidade(unidade):
    """API para análise detalhada de uma unidade específica"""
    return jsonify(dashboard_data.get_analise_detalhada_unidade(unidade))

@app.route('/api/fluxo-mensal')
def api_fluxo_mensal():
    """API para dados de fluxo mensal com filtro por unidade"""
    unidade = request.args.get('unidade', 'TODAS')
    return jsonify(dashboard_data.get_fluxo_mensal(unidade))

@app.route('/api/top-unidades')
def api_top_unidades():
    """API para top unidades com análise comparativa"""
    limit = request.args.get('limit', 10, type=int)
    return jsonify(dashboard_data.get_top_unidades(limit))

@app.route('/api/top-fornecedores')
def api_top_fornecedores():
    """API para top fornecedores com filtro por unidade"""
    limit = request.args.get('limit', 10, type=int)
    unidade = request.args.get('unidade', 'TODAS')
    return jsonify(dashboard_data.get_top_fornecedores(limit, unidade))

@app.route('/api/analise-vencimentos')
def api_analise_vencimentos():
    """API para análise detalhada de vencimentos"""
    unidade = request.args.get('unidade', 'TODAS')
    return jsonify(dashboard_data.get_analise_vencimentos(unidade))

@app.route('/api/fluxo-caixa-projetado')
def api_fluxo_caixa():
    """API para projeção de fluxo de caixa"""
    unidade = request.args.get('unidade', 'TODAS')
    dias = request.args.get('dias', 90, type=int)
    return jsonify(dashboard_data.get_fluxo_caixa_projetado(unidade, dias))

@app.route('/api/alertas')
def api_alertas():
    """API para alertas ativos"""
    return jsonify(dashboard_data.get_alertas_ativos())

@app.route('/api/refresh')
def api_refresh():
    """API para recarregar todos os dados"""
    dashboard_data.carregar_dados()
    return jsonify({'status': 'success', 'timestamp': datetime.now().isoformat()})

@app.route('/api/status')
def api_status():
    """API para status do sistema"""
    status = {
        'timestamp': datetime.now().isoformat(),
        'arquivos': {
            'produtos': os.path.exists('controle_produtos.xlsx'),
            'faturamento': os.path.exists('controle_faturamento_geral.xlsx'),
            'boletos': os.path.exists('controle_boletos.xlsx'),
            'alertas': os.path.exists('alertas_sistema.json')
        },
        'registros': {
            'produtos': len(dashboard_data.df_produtos) if dashboard_data.df_produtos is not None else 0,
            'faturamento': len(dashboard_data.df_faturamento) if dashboard_data.df_faturamento is not None else 0,
            'boletos': len(dashboard_data.df_boletos) if dashboard_data.df_boletos is not None else 0
        }
    }
    return jsonify(status)

# APIs Avançadas para Dashboard SantaClara

@app.route('/api/top-produtos')
def api_top_produtos():
    """Top produtos por valor e quantidade"""
    unidade = request.args.get('unidade', 'TODAS')
    limit = int(request.args.get('limit', 10))

    if dashboard_data.df_produtos is None:
        return jsonify({'labels': [], 'data': [], 'detalhes': []})

    df_temp = dashboard_data.df_produtos.copy()

    if unidade and unidade != 'TODAS':
        df_temp = df_temp[df_temp['Unidade'] == unidade]

    if len(df_temp) == 0:
        return jsonify({'labels': [], 'data': [], 'detalhes': []})

    # Análise por produto
    analise_produtos = df_temp.groupby('Descricao').agg({
        'Valor_Total_Numerico': ['sum', 'count', 'mean'],
        'Quantidade_Numerica': 'sum',
        'Unidade': 'nunique'
    }).round(2)

    analise_produtos.columns = ['Valor_Total', 'Qtd_Compras', 'Valor_Medio', 'Qtd_Total', 'Qtd_Unidades']
    analise_produtos = analise_produtos.sort_values('Valor_Total', ascending=False).head(limit)

    labels = list(analise_produtos.index)
    data = [float(valor) for valor in analise_produtos['Valor_Total'].values]

    # Detalhes para tooltips
    detalhes = []
    for produto in labels:
        info = analise_produtos.loc[produto]
        detalhes.append({
            'produto': produto,
            'valor_total': info['Valor_Total'],
            'qtd_compras': int(info['Qtd_Compras']),
            'qtd_total': info['Qtd_Total'],
            'valor_medio': info['Valor_Medio'],
            'qtd_unidades': int(info['Qtd_Unidades'])
        })

    return jsonify({
        'labels': labels,
        'data': data,
        'detalhes': detalhes
    })

@app.route('/api/boletos-vencimento')
def api_boletos_vencimento():
    """Análise de boletos por período de vencimento"""
    if dashboard_data.df_boletos is None:
        return jsonify({'hoje': 0, 'semana': 0, 'mes': 0, 'vencidos': 0})

    hoje = datetime.now()
    df_boletos = dashboard_data.df_boletos.copy()

    # Calcular dias até vencimento
    df_boletos['Dias_Vencimento'] = (df_boletos['Data_Vencimento_Dt'] - hoje).dt.days

    # Categorizar vencimentos
    vencidos = len(df_boletos[df_boletos['Dias_Vencimento'] < 0])
    hoje_vence = len(df_boletos[df_boletos['Dias_Vencimento'] == 0])
    semana = len(df_boletos[(df_boletos['Dias_Vencimento'] > 0) & (df_boletos['Dias_Vencimento'] <= 7)])
    mes = len(df_boletos[(df_boletos['Dias_Vencimento'] > 7) & (df_boletos['Dias_Vencimento'] <= 30)])

    return jsonify({
        'vencidos': vencidos,
        'hoje': hoje_vence,
        'semana': semana,
        'mes': mes,
        'total': len(df_boletos)
    })

if __name__ == '__main__':
    print("🌐 INICIANDO DASHBOARD WEB INTERATIVO")
    print("=" * 50)
    print("📊 Dashboard disponível em: http://localhost:5000")
    print("🔄 Atualização automática a cada 30 segundos")
    print("📱 Interface responsiva (desktop + mobile)")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
