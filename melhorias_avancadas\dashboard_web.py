#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DASHBOARD WEB INTERATIVO
Sistema web para visualização de dados financeiros em tempo real
"""

from flask import Flask, render_template, jsonify, request
import pandas as pd
import json
import os
from datetime import datetime, timedelta
import numpy as np

app = Flask(__name__)

class DashboardWebData:
    def __init__(self):
        self.df_produtos = None
        self.df_faturamento = None
        self.df_boletos = None
        self.carregar_dados()
    
    def carregar_dados(self):
        """Carrega dados das planilhas"""
        try:
            if os.path.exists('controle_produtos.xlsx'):
                self.df_produtos = pd.read_excel('controle_produtos.xlsx')
                self.processar_dados_produtos()
            
            if os.path.exists('controle_faturamento_geral.xlsx'):
                self.df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
                self.processar_dados_faturamento()
            
            if os.path.exists('controle_boletos.xlsx'):
                self.df_boletos = pd.read_excel('controle_boletos.xlsx')
                self.processar_dados_boletos()
                
        except Exception as e:
            print(f"Erro ao carregar dados: {e}")
    
    def processar_dados_produtos(self):
        """Processa dados de produtos"""
        if self.df_produtos is not None:
            self.df_produtos['Valor_Total_Numerico'] = self.df_produtos['Valor Total Item'].apply(self.converter_valor_brasileiro)
            self.df_produtos['Data_Emissao_Dt'] = pd.to_datetime(self.df_produtos['Data Emissao'], format='%d/%m/%Y', errors='coerce')
    
    def processar_dados_faturamento(self):
        """Processa dados de faturamento"""
        if self.df_faturamento is not None:
            self.df_faturamento['Valor_Numerico'] = self.df_faturamento['Valor Total NF'].apply(self.converter_valor_brasileiro)
            self.df_faturamento['Data_Emissao_Dt'] = pd.to_datetime(self.df_faturamento['Data Emissao'], format='%d/%m/%Y', errors='coerce')
            self.df_faturamento['Data_Vencimento_Dt'] = pd.to_datetime(self.df_faturamento['Data Vencimento'], format='%d/%m/%Y', errors='coerce')
    
    def processar_dados_boletos(self):
        """Processa dados de boletos"""
        if self.df_boletos is not None:
            self.df_boletos['Valor_Numerico'] = self.df_boletos['Valor Documento'].apply(self.converter_valor_brasileiro)
            self.df_boletos['Data_Vencimento_Dt'] = pd.to_datetime(self.df_boletos['Data Vencimento'], format='%d/%m/%Y', errors='coerce')
    
    def converter_valor_brasileiro(self, valor):
        """Converte valor brasileiro para float"""
        try:
            if pd.isna(valor) or valor == '':
                return 0.0
            valor_str = str(valor).replace('R$', '').replace(' ', '').replace('.', '').replace(',', '.')
            return float(valor_str)
        except:
            return 0.0
    
    def get_resumo_executivo(self):
        """Retorna resumo executivo para o dashboard"""
        if self.df_faturamento is None:
            return {}
        
        total_nfs = len(self.df_faturamento)
        valor_total = self.df_faturamento['Valor_Numerico'].sum()
        unidades_ativas = self.df_faturamento['Unidade'].nunique()
        fornecedores_unicos = self.df_faturamento['Fornecedor'].nunique()
        
        return {
            'total_nfs': total_nfs,
            'valor_total': valor_total,
            'unidades_ativas': unidades_ativas,
            'fornecedores_unicos': fornecedores_unicos,
            'valor_total_formatado': f"R$ {valor_total:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
        }
    
    def get_fluxo_mensal(self):
        """Retorna dados de fluxo mensal para gráfico"""
        if self.df_faturamento is None:
            return {'labels': [], 'data': []}
        
        df_temp = self.df_faturamento.copy()
        df_temp['Mes_Ano'] = df_temp['Data_Emissao_Dt'].dt.to_period('M')
        
        fluxo_mensal = df_temp.groupby('Mes_Ano')['Valor_Numerico'].sum().sort_index()
        
        labels = [str(periodo) for periodo in fluxo_mensal.index[-12:]]  # Últimos 12 meses
        data = [float(valor) for valor in fluxo_mensal.values[-12:]]
        
        return {'labels': labels, 'data': data}
    
    def get_top_unidades(self, limit=10):
        """Retorna top unidades por valor"""
        if self.df_faturamento is None:
            return {'labels': [], 'data': []}
        
        top_unidades = self.df_faturamento.groupby('Unidade')['Valor_Numerico'].sum().sort_values(ascending=False).head(limit)
        
        return {
            'labels': list(top_unidades.index),
            'data': [float(valor) for valor in top_unidades.values]
        }
    
    def get_top_fornecedores(self, limit=10):
        """Retorna top fornecedores por valor"""
        if self.df_faturamento is None:
            return {'labels': [], 'data': []}
        
        top_fornecedores = self.df_faturamento.groupby('Fornecedor')['Valor_Numerico'].sum().sort_values(ascending=False).head(limit)
        
        # Truncar nomes longos
        labels = [nome[:30] + "..." if len(nome) > 30 else nome for nome in top_fornecedores.index]
        
        return {
            'labels': labels,
            'data': [float(valor) for valor in top_fornecedores.values]
        }
    
    def get_vencimentos_proximos(self):
        """Retorna vencimentos próximos"""
        if self.df_faturamento is None:
            return {'7_dias': 0, '15_dias': 0, '30_dias': 0}
        
        hoje = datetime.now()
        df_venc = self.df_faturamento[self.df_faturamento['Data_Vencimento_Dt'].notna()].copy()
        
        vencimentos = {}
        for dias in [7, 15, 30]:
            data_limite = hoje + timedelta(days=dias)
            venc_periodo = df_venc[
                (df_venc['Data_Vencimento_Dt'] >= hoje) & 
                (df_venc['Data_Vencimento_Dt'] <= data_limite)
            ]
            vencimentos[f'{dias}_dias'] = len(venc_periodo)
        
        return vencimentos
    
    def get_alertas_ativos(self):
        """Retorna alertas ativos do sistema"""
        try:
            if os.path.exists('alertas_sistema.json'):
                with open('alertas_sistema.json', 'r', encoding='utf-8') as f:
                    alertas = json.load(f)
                
                # Contar por prioridade
                alta = len([a for a in alertas if a.get('prioridade') == 'ALTA'])
                media = len([a for a in alertas if a.get('prioridade') == 'MEDIA'])
                baixa = len([a for a in alertas if a.get('prioridade') == 'BAIXA'])
                
                return {'alta': alta, 'media': media, 'baixa': baixa, 'total': len(alertas)}
            
            return {'alta': 0, 'media': 0, 'baixa': 0, 'total': 0}
        except:
            return {'alta': 0, 'media': 0, 'baixa': 0, 'total': 0}

# Instância global dos dados
dashboard_data = DashboardWebData()

@app.route('/')
def index():
    """Página principal do dashboard"""
    return render_template('dashboard.html')

@app.route('/api/resumo')
def api_resumo():
    """API para dados do resumo executivo"""
    dashboard_data.carregar_dados()  # Recarregar dados
    return jsonify(dashboard_data.get_resumo_executivo())

@app.route('/api/fluxo-mensal')
def api_fluxo_mensal():
    """API para dados de fluxo mensal"""
    return jsonify(dashboard_data.get_fluxo_mensal())

@app.route('/api/top-unidades')
def api_top_unidades():
    """API para top unidades"""
    limit = request.args.get('limit', 10, type=int)
    return jsonify(dashboard_data.get_top_unidades(limit))

@app.route('/api/top-fornecedores')
def api_top_fornecedores():
    """API para top fornecedores"""
    limit = request.args.get('limit', 10, type=int)
    return jsonify(dashboard_data.get_top_fornecedores(limit))

@app.route('/api/vencimentos')
def api_vencimentos():
    """API para vencimentos próximos"""
    return jsonify(dashboard_data.get_vencimentos_proximos())

@app.route('/api/alertas')
def api_alertas():
    """API para alertas ativos"""
    return jsonify(dashboard_data.get_alertas_ativos())

@app.route('/api/refresh')
def api_refresh():
    """API para recarregar todos os dados"""
    dashboard_data.carregar_dados()
    return jsonify({'status': 'success', 'timestamp': datetime.now().isoformat()})

@app.route('/api/status')
def api_status():
    """API para status do sistema"""
    status = {
        'timestamp': datetime.now().isoformat(),
        'arquivos': {
            'produtos': os.path.exists('controle_produtos.xlsx'),
            'faturamento': os.path.exists('controle_faturamento_geral.xlsx'),
            'boletos': os.path.exists('controle_boletos.xlsx'),
            'alertas': os.path.exists('alertas_sistema.json')
        },
        'registros': {
            'produtos': len(dashboard_data.df_produtos) if dashboard_data.df_produtos is not None else 0,
            'faturamento': len(dashboard_data.df_faturamento) if dashboard_data.df_faturamento is not None else 0,
            'boletos': len(dashboard_data.df_boletos) if dashboard_data.df_boletos is not None else 0
        }
    }
    return jsonify(status)

if __name__ == '__main__':
    print("🌐 INICIANDO DASHBOARD WEB INTERATIVO")
    print("=" * 50)
    print("📊 Dashboard disponível em: http://localhost:5000")
    print("🔄 Atualização automática a cada 30 segundos")
    print("📱 Interface responsiva (desktop + mobile)")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
