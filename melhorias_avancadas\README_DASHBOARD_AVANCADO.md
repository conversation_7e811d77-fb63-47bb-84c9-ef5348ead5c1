# 📊 DASHBOARD FINANCEIRO AVANÇADO

## 🎯 VISÃO GERAL

Dashboard web profissional desenvolvido especificamente para **analistas financeiros** que precisam de análises detalhadas por unidade e relatórios específicos para proprietários.

## 🚀 RECURSOS IMPLEMENTADOS

### 📈 **Análises Financeiras Avançadas**
- ✅ **Resumo Executivo** com KPIs principais
- ✅ **Evolução Mensal** do faturamento
- ✅ **Análise Comparativa** entre unidades
- ✅ **Top Fornecedores** por valor e quantidade
- ✅ **Análise de Concentração** (Pareto)
- ✅ **Projeção de Fluxo de Caixa** (90 dias)

### 🔍 **Filtros Avançados**
- ✅ **Filtro por Unidade** específica
- ✅ **Período de Análise** configurável
- ✅ **Drill-down** para análises detalhadas
- ✅ **Comparação** entre períodos

### ⚠️ **Alertas Inteligentes**
- ✅ **Vencimentos Críticos** (vencido, hoje, 7 dias)
- ✅ **Concentração de Fornecedores** (análise de risco)
- ✅ **Variação Percentual** mensal
- ✅ **Insights Automáticos** baseados nos dados

### 📱 **Interface Moderna**
- ✅ **Design Responsivo** (desktop, tablet, mobile)
- ✅ **Gráficos Interativos** com Chart.js
- ✅ **Tabelas Avançadas** com DataTables
- ✅ **Atualização Automática** dos dados

## 🎯 PARA ANALISTAS FINANCEIROS

### **Análise por Unidade**
```
1. Selecione uma unidade específica
2. Veja métricas detalhadas apenas dessa unidade
3. Compare performance com outras unidades
4. Analise concentração de fornecedores
5. Monitore vencimentos críticos
```

### **Relatórios para Proprietários**
```
1. Filtre pela unidade do proprietário
2. Gere relatório detalhado de fornecedores
3. Analise distribuição de gastos
4. Identifique oportunidades de economia
5. Monitore fluxo de caixa projetado
```

### **Métricas Disponíveis**
- 📊 **Total de NFes** por período
- 💰 **Valor Total** faturado
- 📈 **Valor Médio** por NF
- 🏢 **Unidades Ativas**
- 📉 **Variação Percentual** mensal
- ⚠️ **Vencimentos Críticos**

## 🛠️ COMO USAR

### **1. Iniciar o Dashboard**
```bash
cd melhorias_avancadas
python testar_dashboard_avancado.py
```

### **2. Acessar Interface**
```
🌐 URL: http://localhost:5000
📱 Compatível com todos os dispositivos
🔄 Atualização automática a cada 2 minutos
```

### **3. Filtros Principais**

#### **Seletor de Unidade**
- `TODAS AS UNIDADES` - Visão geral consolidada
- `UNIDADE ESPECÍFICA` - Análise detalhada da unidade

#### **Período de Análise**
- `30 dias` - Análise mensal
- `60 dias` - Análise bimestral
- `90 dias` - Análise trimestral (padrão)
- `180 dias` - Análise semestral
- `365 dias` - Análise anual

## 📊 SEÇÕES DO DASHBOARD

### **1. Métricas Principais**
- Cards com KPIs principais
- Indicadores de variação percentual
- Comparação com período anterior

### **2. Alertas de Vencimento**
- NFes vencidas (crítico)
- Vencimentos hoje (urgente)
- Próximos 7 dias (atenção)
- Valores por categoria

### **3. Projeção Fluxo de Caixa**
- Entradas previstas (faturamento)
- Saídas previstas (boletos)
- Gráfico de linha temporal
- Período configurável

### **4. Evolução Mensal**
- Gráfico de linha do faturamento
- Últimos 12 meses
- Filtro por unidade

### **5. Performance por Unidade**
- Gráfico de rosca (doughnut)
- Distribuição percentual
- Top 10 unidades

### **6. Top Fornecedores**
- Gráfico de barras horizontais
- Filtro por unidade
- Top 10 fornecedores

### **7. Análise de Concentração**
- Análise de Pareto
- % dos top 5 e top 10 fornecedores
- Indicador de risco de concentração

### **8. Detalhes da Unidade** (quando selecionada)
- Tabela detalhada de fornecedores
- Quantidade de NFes por fornecedor
- Valor total e médio
- Percentual do total
- Busca e ordenação

### **9. Insights e Recomendações**
- Análises automáticas
- Alertas baseados nos dados
- Recomendações de ação

## 🔧 APIS DISPONÍVEIS

### **Dados Gerais**
- `GET /api/unidades` - Lista de unidades
- `GET /api/resumo?unidade=X` - Resumo executivo
- `GET /api/fluxo-mensal?unidade=X` - Evolução mensal

### **Análises Específicas**
- `GET /api/top-unidades?limit=10` - Top unidades
- `GET /api/top-fornecedores?unidade=X&limit=10` - Top fornecedores
- `GET /api/analise-vencimentos?unidade=X` - Análise de vencimentos
- `GET /api/fluxo-caixa-projetado?unidade=X&dias=90` - Projeção fluxo

### **Detalhamento**
- `GET /api/analise-unidade/{unidade}` - Análise detalhada da unidade

## 📱 RESPONSIVIDADE

### **Desktop (1200px+)**
- Layout completo com 4 colunas
- Gráficos em tamanho full
- Todas as funcionalidades visíveis

### **Tablet (768px - 1199px)**
- Layout adaptado para 2 colunas
- Gráficos redimensionados
- Navegação otimizada

### **Mobile (< 768px)**
- Layout em coluna única
- Cards empilhados
- Gráficos responsivos
- Touch-friendly

## 🎨 DESIGN SYSTEM

### **Cores Principais**
- `Primary`: #2c3e50 (azul escuro)
- `Secondary`: #3498db (azul)
- `Success`: #27ae60 (verde)
- `Warning`: #f39c12 (laranja)
- `Danger`: #e74c3c (vermelho)

### **Componentes**
- Cards com sombra e hover effects
- Gradientes nos headers
- Botões com animações
- Loading states
- Alertas contextuais

## 🔄 ATUALIZAÇÕES AUTOMÁTICAS

- **Intervalo**: 2 minutos
- **Dados**: Recarregamento automático
- **Gráficos**: Atualização dinâmica
- **Timestamp**: Exibição da última atualização

## 📋 DEPENDÊNCIAS

```python
flask>=2.3.0
pandas>=2.0.0
numpy>=1.24.0
```

## 🚀 PRÓXIMAS MELHORIAS

- [ ] Exportação para Excel/PDF
- [ ] Filtros de data personalizados
- [ ] Comparação entre unidades
- [ ] Alertas por email
- [ ] Dashboard mobile app
- [ ] Integração com BI tools

## 📞 SUPORTE

Para dúvidas ou melhorias, consulte a documentação completa ou entre em contato com a equipe de desenvolvimento.

---

**🎯 Dashboard desenvolvido especificamente para analistas financeiros que precisam de análises detalhadas por unidade e relatórios profissionais para proprietários.**
