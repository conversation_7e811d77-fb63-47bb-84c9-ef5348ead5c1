#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DASHBOARD FINANCEIRO INTERATIVO
Sistema de análise e visualização de dados financeiros
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Configuração para gráficos em português
plt.rcParams['font.size'] = 10
plt.rcParams['figure.figsize'] = (12, 8)
sns.set_style("whitegrid")
sns.set_palette("husl")

class DashboardFinanceiro:
    def __init__(self):
        self.df_produtos = None
        self.df_faturamento = None
        self.df_boletos = None
        self.carregar_dados()
    
    def carregar_dados(self):
        """Carrega dados das planilhas"""
        try:
            print("📊 Carregando dados financeiros...")
            
            # Carregar planilhas
            if pd.io.common.file_exists('controle_produtos.xlsx'):
                self.df_produtos = pd.read_excel('controle_produtos.xlsx')
                print(f"   ✅ Produtos: {len(self.df_produtos)} registros")
            
            if pd.io.common.file_exists('controle_faturamento_geral.xlsx'):
                self.df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
                print(f"   ✅ Faturamento: {len(self.df_faturamento)} registros")
            
            if pd.io.common.file_exists('controle_boletos.xlsx'):
                self.df_boletos = pd.read_excel('controle_boletos.xlsx')
                print(f"   ✅ Boletos: {len(self.df_boletos)} registros")
            
            self.processar_dados()
            
        except Exception as e:
            print(f"❌ Erro ao carregar dados: {e}")
    
    def processar_dados(self):
        """Processa e limpa os dados para análise"""
        if self.df_faturamento is not None:
            # Converter valores monetários
            self.df_faturamento['Valor_Numerico'] = self.df_faturamento['Valor Total NF'].apply(self.converter_valor_brasileiro)
            self.df_faturamento['Valor_Parcela_Numerico'] = self.df_faturamento['Valor Parcela'].apply(self.converter_valor_brasileiro)
            
            # Converter datas
            self.df_faturamento['Data_Emissao_Dt'] = pd.to_datetime(self.df_faturamento['Data Emissao'], format='%d/%m/%Y', errors='coerce')
            
        if self.df_produtos is not None:
            # Converter valores de produtos
            self.df_produtos['Valor_Total_Numerico'] = self.df_produtos['Valor Total Item'].apply(self.converter_valor_brasileiro)
            self.df_produtos['Valor_Unitario_Numerico'] = self.df_produtos['Valor Unitario'].apply(self.converter_valor_brasileiro)
            self.df_produtos['Quantidade_Numerica'] = self.df_produtos['Quantidade'].apply(self.converter_quantidade_brasileira)
    
    def converter_valor_brasileiro(self, valor):
        """Converte valor brasileiro (R$ 1.234,56) para float"""
        try:
            if pd.isna(valor) or valor == '':
                return 0.0
            # Remove R$, espaços e converte vírgula para ponto
            valor_str = str(valor).replace('R$', '').replace(' ', '').replace('.', '').replace(',', '.')
            return float(valor_str)
        except:
            return 0.0
    
    def converter_quantidade_brasileira(self, quantidade):
        """Converte quantidade brasileira (1.234,567) para float"""
        try:
            if pd.isna(quantidade) or quantidade == '':
                return 0.0
            # Converte vírgula para ponto
            quantidade_str = str(quantidade).replace(',', '.')
            return float(quantidade_str)
        except:
            return 0.0
    
    def gerar_resumo_executivo(self):
        """Gera resumo executivo dos dados"""
        print("\n" + "="*60)
        print("💼 RESUMO EXECUTIVO FINANCEIRO")
        print("="*60)
        
        if self.df_faturamento is not None:
            total_nfs = len(self.df_faturamento)
            valor_total = self.df_faturamento['Valor_Numerico'].sum()
            unidades_ativas = self.df_faturamento['Unidade'].nunique()
            fornecedores_unicos = self.df_faturamento['Fornecedor'].nunique()
            
            print(f"📊 VISÃO GERAL:")
            print(f"   💰 Valor Total Processado: R$ {valor_total:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.'))
            print(f"   📄 Total de Notas Fiscais: {total_nfs:,}")
            print(f"   🏪 Unidades Ativas: {unidades_ativas}")
            print(f"   🏭 Fornecedores Únicos: {fornecedores_unicos}")
            
            # Top 5 unidades por valor
            print(f"\n🏆 TOP 5 UNIDADES POR VALOR:")
            top_unidades = self.df_faturamento.groupby('Unidade')['Valor_Numerico'].sum().sort_values(ascending=False).head(5)
            for i, (unidade, valor) in enumerate(top_unidades.items(), 1):
                valor_formatado = f"R$ {valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
                print(f"   {i}. {unidade}: {valor_formatado}")
            
            # Top 5 fornecedores por valor
            print(f"\n🏭 TOP 5 FORNECEDORES POR VALOR:")
            top_fornecedores = self.df_faturamento.groupby('Fornecedor')['Valor_Numerico'].sum().sort_values(ascending=False).head(5)
            for i, (fornecedor, valor) in enumerate(top_fornecedores.items(), 1):
                valor_formatado = f"R$ {valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
                fornecedor_nome = fornecedor[:40] + "..." if len(fornecedor) > 40 else fornecedor
                print(f"   {i}. {fornecedor_nome}: {valor_formatado}")
        
        if self.df_produtos is not None:
            total_produtos = len(self.df_produtos)
            produtos_unicos = self.df_produtos['Descricao Produto'].nunique()
            
            print(f"\n📦 PRODUTOS:")
            print(f"   📋 Total de Itens Processados: {total_produtos:,}")
            print(f"   🎯 Produtos Únicos: {produtos_unicos:,}")
    
    def analise_fluxo_caixa(self):
        """Análise de fluxo de caixa por período"""
        if self.df_faturamento is None:
            print("❌ Dados de faturamento não disponíveis")
            return
        
        print("\n" + "="*60)
        print("💵 ANÁLISE DE FLUXO DE CAIXA")
        print("="*60)
        
        # Análise mensal
        df_temp = self.df_faturamento.copy()
        df_temp['Mes_Ano'] = df_temp['Data_Emissao_Dt'].dt.to_period('M')
        
        fluxo_mensal = df_temp.groupby('Mes_Ano')['Valor_Numerico'].sum().sort_index()
        
        print("📈 FLUXO MENSAL:")
        for periodo, valor in fluxo_mensal.tail(6).items():
            valor_formatado = f"R$ {valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
            print(f"   {periodo}: {valor_formatado}")
        
        # Análise por unidade no último mês
        ultimo_mes = fluxo_mensal.index[-1]
        df_ultimo_mes = df_temp[df_temp['Mes_Ano'] == ultimo_mes]
        
        print(f"\n🏪 PERFORMANCE POR UNIDADE ({ultimo_mes}):")
        performance_unidades = df_ultimo_mes.groupby('Unidade')['Valor_Numerico'].sum().sort_values(ascending=False)
        
        for unidade, valor in performance_unidades.head(10).items():
            valor_formatado = f"R$ {valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
            print(f"   {unidade}: {valor_formatado}")
    
    def detectar_anomalias(self):
        """Detecta anomalias nos gastos"""
        if self.df_faturamento is None:
            return
        
        print("\n" + "="*60)
        print("🚨 DETECÇÃO DE ANOMALIAS")
        print("="*60)
        
        # Análise de valores anômalos
        valores = self.df_faturamento['Valor_Numerico']
        q75, q25 = np.percentile(valores, [75, 25])
        iqr = q75 - q25
        limite_superior = q75 + (1.5 * iqr)
        limite_inferior = q25 - (1.5 * iqr)
        
        anomalias_altas = self.df_faturamento[self.df_faturamento['Valor_Numerico'] > limite_superior]
        
        if len(anomalias_altas) > 0:
            print(f"⚠️ VALORES ANÔMALOS DETECTADOS ({len(anomalias_altas)} casos):")
            for _, row in anomalias_altas.head(5).iterrows():
                valor_formatado = f"R$ {row['Valor_Numerico']:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
                print(f"   {row['Unidade']} - {row['Fornecedor'][:30]}... - {valor_formatado}")
        else:
            print("✅ Nenhuma anomalia detectada nos valores")
    
    def gerar_dashboard_completo(self):
        """Gera dashboard completo"""
        print("🚀 GERANDO DASHBOARD FINANCEIRO COMPLETO...")
        
        self.gerar_resumo_executivo()
        self.analise_fluxo_caixa()
        self.detectar_anomalias()
        
        print("\n" + "="*60)
        print("✅ DASHBOARD FINANCEIRO CONCLUÍDO!")
        print("="*60)

def main():
    dashboard = DashboardFinanceiro()
    dashboard.gerar_dashboard_completo()

if __name__ == '__main__':
    main()
