#!/usr/bin/env python3
"""
🔍 VERIFICAR COLUNAS DOS PRODUTOS
Verifica se temos fabricante, lote e validade
"""

import pandas as pd
import os

def verificar_colunas():
    print("🔍 VERIFICANDO COLUNAS DOS PRODUTOS")
    print("=" * 50)
    
    if os.path.exists('controle_produtos.xlsx'):
        df = pd.read_excel('controle_produtos.xlsx')
        
        print(f"📊 Total de registros: {len(df):,}")
        print(f"📊 Total de colunas: {len(df.columns)}")
        print()
        print("📋 COLUNAS DISPONÍVEIS:")
        for i, col in enumerate(df.columns):
            print(f"   {i+1:2d}. {col}")
        
        # Verificar se temos as colunas necessárias
        colunas_necessarias = ['Fabricante', 'Lote', 'Validade', 'Data Validade']
        colunas_encontradas = []
        colunas_faltando = []
        
        print()
        print("🎯 VERIFICAÇÃO DE COLUNAS NECESSÁRIAS:")
        for col_necessaria in colunas_necessarias:
            encontrada = False
            for col_existente in df.columns:
                if col_necessaria.lower() in col_existente.lower():
                    colunas_encontradas.append(col_existente)
                    print(f"   ✅ {col_necessaria} -> {col_existente}")
                    encontrada = True
                    break
            if not encontrada:
                colunas_faltando.append(col_necessaria)
                print(f"   ❌ {col_necessaria} -> NÃO ENCONTRADA")
        
        print()
        if colunas_faltando:
            print("🚨 AÇÃO NECESSÁRIA:")
            print("   As seguintes colunas estão faltando:")
            for col in colunas_faltando:
                print(f"   - {col}")
            print()
            print("   📧 SERÁ NECESSÁRIO REPROCESSAR OS EMAILS")
            print("   para extrair essas informações dos XMLs das NFes")
        else:
            print("✅ TODAS AS COLUNAS NECESSÁRIAS ESTÃO DISPONÍVEIS!")
        
        # Mostrar amostra dos dados
        print()
        print("📋 AMOSTRA DOS DADOS (primeiras 3 linhas):")
        print(df.head(3).to_string())
        
    else:
        print("❌ Arquivo controle_produtos.xlsx não encontrado")

if __name__ == "__main__":
    verificar_colunas()
