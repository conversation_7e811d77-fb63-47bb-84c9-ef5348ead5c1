#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TESTE DO REPROCESSAMENTO DE 5000 E-MAILS
Verifica se o sistema está buscando realmente 5000 e-mails
"""

import os
import sys
from datetime import datetime
from automacao_nf import autenticar_gmail, buscar_emails, carregar_unidades, ARQUIVO_UNIDADES_CNPJ

def testar_reprocessamento_5000():
    """Testa especificamente a busca de 5000 e-mails"""
    print("🔄 TESTE DO REPROCESSAMENTO DE 5000 E-MAILS")
    print("=" * 60)
    
    # Carregar unidades
    print("📋 Carregando unidades...")
    df_unidades = carregar_unidades(ARQUIVO_UNIDADES_CNPJ)
    if df_unidades is None:
        print("❌ ERRO: Não foi possível carregar arquivo de unidades!")
        return
    
    print("✅ Unidades carregadas com sucesso!")
    
    # Autenticar com Gmail
    print("🔐 Autenticando com Gmail...")
    servico_gmail = autenticar_gmail()
    if not servico_gmail:
        print("❌ ERRO: Falha na autenticação com Gmail!")
        return
    
    print("✅ Autenticação bem-sucedida!")
    
    # Buscar 5000 e-mails
    print("\n🔍 BUSCANDO 5000 E-MAILS COM PAGINAÇÃO...")
    print("📄 Este teste verifica se a paginação está funcionando corretamente")
    print("⏱️ Pode levar alguns segundos...")
    
    inicio = datetime.now()
    
    try:
        emails = buscar_emails(servico_gmail, incluir_lidos=True, max_results=5000)
        
        fim = datetime.now()
        tempo_execucao = (fim - inicio).total_seconds()
        
        print(f"\n✅ BUSCA CONCLUÍDA!")
        print("=" * 60)
        print(f"📧 E-mails encontrados: {len(emails)}")
        print(f"⏱️ Tempo de execução: {tempo_execucao:.2f} segundos")
        print(f"📊 E-mails por segundo: {len(emails)/tempo_execucao:.1f}")
        
        # Análise dos resultados
        print(f"\n📈 ANÁLISE DOS RESULTADOS:")
        
        if len(emails) >= 5000:
            print("🎉 EXCELENTE! Sistema encontrou 5000+ e-mails")
            print("✅ Paginação funcionando perfeitamente")
        elif len(emails) >= 4000:
            print("✅ MUITO BOM! Sistema encontrou 4000+ e-mails")
            print("ℹ️ Pode não haver mais e-mails disponíveis na caixa")
        elif len(emails) >= 1000:
            print("✅ BOM! Sistema encontrou 1000+ e-mails")
            print("ℹ️ Paginação funcionando, mas há menos e-mails disponíveis")
        elif len(emails) > 500:
            print("✅ Paginação funcionando - mais de 500 e-mails")
            print("⚠️ Poucos e-mails disponíveis na caixa de entrada")
        elif len(emails) == 500:
            print("⚠️ ATENÇÃO! Exatamente 500 e-mails encontrados")
            print("❌ Pode indicar que a paginação não está funcionando")
        else:
            print("⚠️ Poucos e-mails encontrados")
            print("ℹ️ Verifique os critérios de busca")
        
        # Verificação de performance
        print(f"\n⚡ VERIFICAÇÃO DE PERFORMANCE:")
        
        if tempo_execucao < 10:
            print("✅ Excelente performance - menos de 10 segundos")
        elif tempo_execucao < 30:
            print("✅ Boa performance - menos de 30 segundos")
        elif tempo_execucao < 60:
            print("⚠️ Performance aceitável - menos de 1 minuto")
        else:
            print("⚠️ Performance lenta - mais de 1 minuto")
            print("💡 Considere processar em lotes menores")
        
        # Estimativa para processamento completo
        if len(emails) > 0:
            print(f"\n🔮 ESTIMATIVA PARA PROCESSAMENTO COMPLETO:")
            
            # Estimar tempo baseado em 2 segundos por e-mail (conservador)
            tempo_estimado_segundos = len(emails) * 2
            tempo_estimado_minutos = tempo_estimado_segundos / 60
            tempo_estimado_horas = tempo_estimado_minutos / 60
            
            if tempo_estimado_horas >= 1:
                print(f"⏱️ Tempo estimado: {tempo_estimado_horas:.1f} horas")
            elif tempo_estimado_minutos >= 1:
                print(f"⏱️ Tempo estimado: {tempo_estimado_minutos:.1f} minutos")
            else:
                print(f"⏱️ Tempo estimado: {tempo_estimado_segundos:.0f} segundos")
            
            print("ℹ️ Esta é uma estimativa conservadora (2s por e-mail)")
        
        # Recomendações
        print(f"\n💡 RECOMENDAÇÕES:")
        
        if len(emails) >= 3000:
            print("✅ Sistema pronto para reprocessamento de 5000 e-mails")
            print("🚀 Execute: python reprocessar_5000_emails.py")
        elif len(emails) >= 1000:
            print("✅ Sistema funcionando bem")
            print(f"💡 Considere ajustar o limite para {len(emails)} e-mails")
        else:
            print("⚠️ Poucos e-mails disponíveis")
            print("💡 Verifique se há mais e-mails na caixa de entrada")
        
        return len(emails)
        
    except Exception as e:
        print(f"❌ ERRO durante a busca: {e}")
        import traceback
        traceback.print_exc()
        return 0

def main():
    """Função principal"""
    print("🧪 INICIANDO TESTE DE REPROCESSAMENTO DE 5000 E-MAILS")
    print("=" * 60)
    
    confirmacao = input("Este teste pode levar alguns minutos. Continuar? (S/N): ").strip().upper()
    
    if confirmacao != 'S':
        print("❌ Teste cancelado pelo usuário")
        return
    
    emails_encontrados = testar_reprocessamento_5000()
    
    print(f"\n🏁 TESTE CONCLUÍDO!")
    print("=" * 60)
    
    if emails_encontrados >= 1000:
        print("🎉 SISTEMA PRONTO PARA REPROCESSAMENTO!")
        print("✅ A paginação está funcionando corretamente")
        print("🚀 Você pode executar com segurança:")
        print("   python reprocessar_5000_emails.py")
    else:
        print("⚠️ Sistema funcionando, mas poucos e-mails disponíveis")
        print("💡 Verifique a caixa de entrada ou ajuste os critérios")

if __name__ == '__main__':
    main()
