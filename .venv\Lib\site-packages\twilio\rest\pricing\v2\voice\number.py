r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Pricing
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Any, Dict, List, Optional, Union
from twilio.base import values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version


class NumberInstance(InstanceResource):
    """
    :ivar destination_number: The destination phone number in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, which consists of a + followed by the country code and subscriber number.
    :ivar origination_number: The origination phone number in [[E.164](https://www.twilio.com/docs/glossary/what-e164) format, which consists of a + followed by the country code and subscriber number.
    :ivar country: The name of the country.
    :ivar iso_country: The [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2)
    :ivar outbound_call_prices: The list of [OutboundCallPriceWithOrigin](https://www.twilio.com/docs/voice/pricing#outbound-call-price-with-origin) records.
    :ivar inbound_call_price:
    :ivar price_unit: The currency in which prices are measured, specified in [ISO 4127](https://www.iso.org/iso/home/<USER>/currency_codes.htm) format (e.g. `usd`, `eur`, `jpy`).
    :ivar url: The absolute URL of the resource.
    """

    def __init__(
        self,
        version: Version,
        payload: Dict[str, Any],
        destination_number: Optional[str] = None,
    ):
        super().__init__(version)

        self.destination_number: Optional[str] = payload.get("destination_number")
        self.origination_number: Optional[str] = payload.get("origination_number")
        self.country: Optional[str] = payload.get("country")
        self.iso_country: Optional[str] = payload.get("iso_country")
        self.outbound_call_prices: Optional[List[str]] = payload.get(
            "outbound_call_prices"
        )
        self.inbound_call_price: Optional[str] = payload.get("inbound_call_price")
        self.price_unit: Optional[str] = payload.get("price_unit")
        self.url: Optional[str] = payload.get("url")

        self._solution = {
            "destination_number": destination_number or self.destination_number,
        }
        self._context: Optional[NumberContext] = None

    @property
    def _proxy(self) -> "NumberContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: NumberContext for this NumberInstance
        """
        if self._context is None:
            self._context = NumberContext(
                self._version,
                destination_number=self._solution["destination_number"],
            )
        return self._context

    def fetch(
        self, origination_number: Union[str, object] = values.unset
    ) -> "NumberInstance":
        """
        Fetch the NumberInstance

        :param origination_number: The origination phone number, in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, for which to fetch the origin-based voice pricing information. E.164 format consists of a + followed by the country code and subscriber number.

        :returns: The fetched NumberInstance
        """
        return self._proxy.fetch(
            origination_number=origination_number,
        )

    async def fetch_async(
        self, origination_number: Union[str, object] = values.unset
    ) -> "NumberInstance":
        """
        Asynchronous coroutine to fetch the NumberInstance

        :param origination_number: The origination phone number, in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, for which to fetch the origin-based voice pricing information. E.164 format consists of a + followed by the country code and subscriber number.

        :returns: The fetched NumberInstance
        """
        return await self._proxy.fetch_async(
            origination_number=origination_number,
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Pricing.V2.NumberInstance {}>".format(context)


class NumberContext(InstanceContext):

    def __init__(self, version: Version, destination_number: str):
        """
        Initialize the NumberContext

        :param version: Version that contains the resource
        :param destination_number: The destination phone number, in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, for which to fetch the origin-based voice pricing information. E.164 format consists of a + followed by the country code and subscriber number.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "destination_number": destination_number,
        }
        self._uri = "/Voice/Numbers/{destination_number}".format(**self._solution)

    def fetch(
        self, origination_number: Union[str, object] = values.unset
    ) -> NumberInstance:
        """
        Fetch the NumberInstance

        :param origination_number: The origination phone number, in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, for which to fetch the origin-based voice pricing information. E.164 format consists of a + followed by the country code and subscriber number.

        :returns: The fetched NumberInstance
        """

        data = values.of(
            {
                "OriginationNumber": origination_number,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return NumberInstance(
            self._version,
            payload,
            destination_number=self._solution["destination_number"],
        )

    async def fetch_async(
        self, origination_number: Union[str, object] = values.unset
    ) -> NumberInstance:
        """
        Asynchronous coroutine to fetch the NumberInstance

        :param origination_number: The origination phone number, in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, for which to fetch the origin-based voice pricing information. E.164 format consists of a + followed by the country code and subscriber number.

        :returns: The fetched NumberInstance
        """

        data = values.of(
            {
                "OriginationNumber": origination_number,
            }
        )

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )

        return NumberInstance(
            self._version,
            payload,
            destination_number=self._solution["destination_number"],
        )

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Pricing.V2.NumberContext {}>".format(context)


class NumberList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the NumberList

        :param version: Version that contains the resource

        """
        super().__init__(version)

    def get(self, destination_number: str) -> NumberContext:
        """
        Constructs a NumberContext

        :param destination_number: The destination phone number, in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, for which to fetch the origin-based voice pricing information. E.164 format consists of a + followed by the country code and subscriber number.
        """
        return NumberContext(self._version, destination_number=destination_number)

    def __call__(self, destination_number: str) -> NumberContext:
        """
        Constructs a NumberContext

        :param destination_number: The destination phone number, in [E.164](https://www.twilio.com/docs/glossary/what-e164) format, for which to fetch the origin-based voice pricing information. E.164 format consists of a + followed by the country code and subscriber number.
        """
        return NumberContext(self._version, destination_number=destination_number)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Pricing.V2.NumberList>"
