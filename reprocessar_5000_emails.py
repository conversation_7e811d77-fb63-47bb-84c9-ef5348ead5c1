#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCRIPT PARA REPROCESSAR OS ÚLTIMOS 5000 E-MAILS
Executa automaticamente o reprocessamento sem interação do usuário
"""

import os
import sys
import time
import traceback
from datetime import datetime

# Importar todas as funções do script principal
from automacao_nf import *

def main_reprocessamento():
    """Função principal para reprocessamento automático dos últimos 5000 e-mails"""
    print("🔄 INICIANDO REPROCESSAMENTO DOS ÚLTIMOS 5000 E-MAILS...")
    print("⚠️  MODO AUTOMÁTICO - SEM INTERAÇÃO DO USUÁRIO")
    print("📧 Incluindo e-mails LIDOS e NÃO LIDOS")
    print("=" * 60)
    
    # Carregar unidades
    os.makedirs(PASTA_INCONSISTENCIAS, exist_ok=True)
    df_unidades = carregar_unidades(ARQUIVO_UNIDADES_CNPJ)
    if df_unidades is None: 
        print("❌ ERRO: Não foi possível carregar arquivo de unidades!")
        exit()
    
    # Autenticar com Gmail
    servico_gmail = autenticar_gmail()
    if not servico_gmail:
        print("❌ ERRO: Falha na autenticação com Gmail!")
        exit()
    
    print("✅ Autenticação com Gmail bem-sucedida!")
    
    # Buscar TODOS os e-mails (incluindo lidos) - últimos 5000
    print("\n🔍 BUSCANDO ÚLTIMOS 5000 E-MAILS...")
    emails = buscar_emails(servico_gmail, incluir_lidos=True, max_results=5000)
    
    if not emails:
        print("❌ Nenhum e-mail encontrado.")
        return
    
    total_emails = len(emails)
    emails_processados = 0
    emails_com_erro = 0
    emails_sem_anexo = 0
    
    print(f"✅ Encontrados {total_emails} e-mails para reprocessar.")
    print("🚀 INICIANDO REPROCESSAMENTO COMPLETO...")
    print("=" * 60)
    
    # Processar cada e-mail
    for i, email_info in enumerate(emails, 1):
        msg_id = email_info['id']
        try:
            print(f"\n[{i}/{total_emails}] Reprocessando e-mail ID: {msg_id} | Progresso: {(i/total_emails)*100:.1f}%")
            
            message = servico_gmail.users().messages().get(userId='me', id=msg_id, format='full').execute()
            payload = message.get('payload', {})
            headers = payload.get('headers', [])
            assunto = next((h['value'] for h in headers if h['name'].lower() == 'subject'), '')
            
            print(f"  Assunto: '{assunto[:80]}{'...' if len(assunto) > 80 else ''}'")

            parts_a_verificar = [payload]
            anexos_encontrados_neste_email = False
            
            while parts_a_verificar:
                part = parts_a_verificar.pop(0)
                if 'parts' in part: 
                    parts_a_verificar.extend(part.get('parts', []))

                filename = part.get('filename', '').lower()
                # Processa XMLs e PDFs
                if filename and (filename.endswith('.xml') or filename.endswith('.pdf')):
                    anexos_encontrados_neste_email = True
                    tipo_arquivo = "XML" if filename.endswith('.xml') else "PDF"
                    print(f"  -> Anexo {tipo_arquivo} encontrado: {part.get('filename')}")
                    
                    try:
                        dados_anexo = baixar_anexo_temporario(servico_gmail, msg_id, part)
                        if dados_anexo:
                            # Modo reprocessamento = True para permitir reprocessar arquivos já processados
                            processar_anexo(dados_anexo, part.get('filename'), assunto, df_unidades, modo_reprocessamento=True)
                    except Exception as e:
                        print(f"    -> ERRO ao processar anexo {part.get('filename')}: {e}")
                        continue

            if not anexos_encontrados_neste_email:
                print("  -> Nenhum anexo XML/PDF encontrado neste e-mail.")
                emails_sem_anexo += 1
            else:
                emails_processados += 1
            
            # NÃO marcar como processado para não alterar status dos e-mails
            # marcar_email_como_processado(servico_gmail, msg_id)
            
            # Pausa para evitar rate limiting
            time.sleep(0.2)
            
            # A cada 100 e-mails, mostrar estatísticas parciais
            if i % 100 == 0:
                print(f"\n📊 ESTATÍSTICAS PARCIAIS ({i}/{total_emails}):")
                print(f"   ✅ Processados: {emails_processados}")
                print(f"   📎 Sem anexos: {emails_sem_anexo}")
                print(f"   ❌ Com erro: {emails_com_erro}")
                print("=" * 40)
            
        except Exception as e:
            emails_com_erro += 1
            print(f"❌ ERRO GERAL ao processar e-mail ID {msg_id}: {e}")
            print(f"   Continuando com próximo e-mail...")
            continue
    
    # Estatísticas finais
    print(f"\n🎉 REPROCESSAMENTO CONCLUÍDO!")
    print("=" * 60)
    print(f"📊 ESTATÍSTICAS FINAIS:")
    print(f"   ✅ E-mails processados com sucesso: {emails_processados}")
    print(f"   📎 E-mails sem anexos relevantes: {emails_sem_anexo}")
    print(f"   ❌ E-mails com erro: {emails_com_erro}")
    print(f"   📧 Total de e-mails analisados: {total_emails}")
    print(f"   📈 Taxa de sucesso: {((emails_processados + emails_sem_anexo)/total_emails)*100:.1f}%")
    print("=" * 60)
    print("🏁 FIM DO REPROCESSAMENTO.")

if __name__ == '__main__':
    main_reprocessamento()
