r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Voice
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union, Iterator, AsyncIterator
from twilio.base import deserialize, values
from twilio.base.instance_context import InstanceContext
from twilio.base.instance_resource import InstanceResource
from twilio.base.list_resource import ListResource
from twilio.base.version import Version
from twilio.base.page import Page
from twilio.rest.voice.v1.connection_policy.connection_policy_target import (
    ConnectionPolicyTargetList,
)


class ConnectionPolicyInstance(InstanceResource):
    """
    :ivar account_sid: The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that created the Connection Policy resource.
    :ivar sid: The unique string that we created to identify the Connection Policy resource.
    :ivar friendly_name: The string that you assigned to describe the resource.
    :ivar date_created: The date and time in GMT when the resource was created specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar date_updated: The date and time in GMT when the resource was last updated specified in [RFC 2822](https://www.ietf.org/rfc/rfc2822.txt) format.
    :ivar url: The absolute URL of the resource.
    :ivar links: The URLs of related resources.
    """

    def __init__(
        self, version: Version, payload: Dict[str, Any], sid: Optional[str] = None
    ):
        super().__init__(version)

        self.account_sid: Optional[str] = payload.get("account_sid")
        self.sid: Optional[str] = payload.get("sid")
        self.friendly_name: Optional[str] = payload.get("friendly_name")
        self.date_created: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_created")
        )
        self.date_updated: Optional[datetime] = deserialize.iso8601_datetime(
            payload.get("date_updated")
        )
        self.url: Optional[str] = payload.get("url")
        self.links: Optional[Dict[str, object]] = payload.get("links")

        self._solution = {
            "sid": sid or self.sid,
        }
        self._context: Optional[ConnectionPolicyContext] = None

    @property
    def _proxy(self) -> "ConnectionPolicyContext":
        """
        Generate an instance context for the instance, the context is capable of
        performing various actions. All instance actions are proxied to the context

        :returns: ConnectionPolicyContext for this ConnectionPolicyInstance
        """
        if self._context is None:
            self._context = ConnectionPolicyContext(
                self._version,
                sid=self._solution["sid"],
            )
        return self._context

    def delete(self) -> bool:
        """
        Deletes the ConnectionPolicyInstance


        :returns: True if delete succeeds, False otherwise
        """
        return self._proxy.delete()

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ConnectionPolicyInstance


        :returns: True if delete succeeds, False otherwise
        """
        return await self._proxy.delete_async()

    def fetch(self) -> "ConnectionPolicyInstance":
        """
        Fetch the ConnectionPolicyInstance


        :returns: The fetched ConnectionPolicyInstance
        """
        return self._proxy.fetch()

    async def fetch_async(self) -> "ConnectionPolicyInstance":
        """
        Asynchronous coroutine to fetch the ConnectionPolicyInstance


        :returns: The fetched ConnectionPolicyInstance
        """
        return await self._proxy.fetch_async()

    def update(
        self, friendly_name: Union[str, object] = values.unset
    ) -> "ConnectionPolicyInstance":
        """
        Update the ConnectionPolicyInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It is not unique and can be up to 255 characters long.

        :returns: The updated ConnectionPolicyInstance
        """
        return self._proxy.update(
            friendly_name=friendly_name,
        )

    async def update_async(
        self, friendly_name: Union[str, object] = values.unset
    ) -> "ConnectionPolicyInstance":
        """
        Asynchronous coroutine to update the ConnectionPolicyInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It is not unique and can be up to 255 characters long.

        :returns: The updated ConnectionPolicyInstance
        """
        return await self._proxy.update_async(
            friendly_name=friendly_name,
        )

    @property
    def targets(self) -> ConnectionPolicyTargetList:
        """
        Access the targets
        """
        return self._proxy.targets

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Voice.V1.ConnectionPolicyInstance {}>".format(context)


class ConnectionPolicyContext(InstanceContext):

    def __init__(self, version: Version, sid: str):
        """
        Initialize the ConnectionPolicyContext

        :param version: Version that contains the resource
        :param sid: The unique string that we created to identify the Connection Policy resource to update.
        """
        super().__init__(version)

        # Path Solution
        self._solution = {
            "sid": sid,
        }
        self._uri = "/ConnectionPolicies/{sid}".format(**self._solution)

        self._targets: Optional[ConnectionPolicyTargetList] = None

    def delete(self) -> bool:
        """
        Deletes the ConnectionPolicyInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return self._version.delete(method="DELETE", uri=self._uri, headers=headers)

    async def delete_async(self) -> bool:
        """
        Asynchronous coroutine that deletes the ConnectionPolicyInstance


        :returns: True if delete succeeds, False otherwise
        """

        headers = values.of({})

        return await self._version.delete_async(
            method="DELETE", uri=self._uri, headers=headers
        )

    def fetch(self) -> ConnectionPolicyInstance:
        """
        Fetch the ConnectionPolicyInstance


        :returns: The fetched ConnectionPolicyInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = self._version.fetch(method="GET", uri=self._uri, headers=headers)

        return ConnectionPolicyInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    async def fetch_async(self) -> ConnectionPolicyInstance:
        """
        Asynchronous coroutine to fetch the ConnectionPolicyInstance


        :returns: The fetched ConnectionPolicyInstance
        """

        headers = values.of({})

        headers["Accept"] = "application/json"

        payload = await self._version.fetch_async(
            method="GET", uri=self._uri, headers=headers
        )

        return ConnectionPolicyInstance(
            self._version,
            payload,
            sid=self._solution["sid"],
        )

    def update(
        self, friendly_name: Union[str, object] = values.unset
    ) -> ConnectionPolicyInstance:
        """
        Update the ConnectionPolicyInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It is not unique and can be up to 255 characters long.

        :returns: The updated ConnectionPolicyInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.update(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ConnectionPolicyInstance(
            self._version, payload, sid=self._solution["sid"]
        )

    async def update_async(
        self, friendly_name: Union[str, object] = values.unset
    ) -> ConnectionPolicyInstance:
        """
        Asynchronous coroutine to update the ConnectionPolicyInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It is not unique and can be up to 255 characters long.

        :returns: The updated ConnectionPolicyInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
            }
        )
        headers = values.of({})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.update_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ConnectionPolicyInstance(
            self._version, payload, sid=self._solution["sid"]
        )

    @property
    def targets(self) -> ConnectionPolicyTargetList:
        """
        Access the targets
        """
        if self._targets is None:
            self._targets = ConnectionPolicyTargetList(
                self._version,
                self._solution["sid"],
            )
        return self._targets

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        context = " ".join("{}={}".format(k, v) for k, v in self._solution.items())
        return "<Twilio.Voice.V1.ConnectionPolicyContext {}>".format(context)


class ConnectionPolicyPage(Page):

    def get_instance(self, payload: Dict[str, Any]) -> ConnectionPolicyInstance:
        """
        Build an instance of ConnectionPolicyInstance

        :param payload: Payload response from the API
        """
        return ConnectionPolicyInstance(self._version, payload)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Voice.V1.ConnectionPolicyPage>"


class ConnectionPolicyList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the ConnectionPolicyList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/ConnectionPolicies"

    def create(
        self, friendly_name: Union[str, object] = values.unset
    ) -> ConnectionPolicyInstance:
        """
        Create the ConnectionPolicyInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It is not unique and can be up to 255 characters long.

        :returns: The created ConnectionPolicyInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = self._version.create(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ConnectionPolicyInstance(self._version, payload)

    async def create_async(
        self, friendly_name: Union[str, object] = values.unset
    ) -> ConnectionPolicyInstance:
        """
        Asynchronously create the ConnectionPolicyInstance

        :param friendly_name: A descriptive string that you create to describe the resource. It is not unique and can be up to 255 characters long.

        :returns: The created ConnectionPolicyInstance
        """

        data = values.of(
            {
                "FriendlyName": friendly_name,
            }
        )
        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Content-Type"] = "application/x-www-form-urlencoded"

        headers["Accept"] = "application/json"

        payload = await self._version.create_async(
            method="POST", uri=self._uri, data=data, headers=headers
        )

        return ConnectionPolicyInstance(self._version, payload)

    def stream(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> Iterator[ConnectionPolicyInstance]:
        """
        Streams ConnectionPolicyInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = self.page(page_size=limits["page_size"])

        return self._version.stream(page, limits["limit"])

    async def stream_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> AsyncIterator[ConnectionPolicyInstance]:
        """
        Asynchronously streams ConnectionPolicyInstance records from the API as a generator stream.
        This operation lazily loads records as efficiently as possible until the limit
        is reached.
        The results are returned as a generator, so this operation is memory efficient.

        :param limit: Upper limit for the number of records to return. stream()
                      guarantees to never return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, stream() will attempt to read the
                          limit with the most efficient page size, i.e. min(limit, 1000)

        :returns: Generator that will yield up to limit results
        """
        limits = self._version.read_limits(limit, page_size)
        page = await self.page_async(page_size=limits["page_size"])

        return self._version.stream_async(page, limits["limit"])

    def list(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ConnectionPolicyInstance]:
        """
        Lists ConnectionPolicyInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return list(
            self.stream(
                limit=limit,
                page_size=page_size,
            )
        )

    async def list_async(
        self,
        limit: Optional[int] = None,
        page_size: Optional[int] = None,
    ) -> List[ConnectionPolicyInstance]:
        """
        Asynchronously lists ConnectionPolicyInstance records from the API as a list.
        Unlike stream(), this operation is eager and will load `limit` records into
        memory before returning.

        :param limit: Upper limit for the number of records to return. list() guarantees
                      never to return more than limit.  Default is no limit
        :param page_size: Number of records to fetch per request, when not set will use
                          the default value of 50 records.  If no page_size is defined
                          but a limit is defined, list() will attempt to read the limit
                          with the most efficient page size, i.e. min(limit, 1000)

        :returns: list that will contain up to limit results
        """
        return [
            record
            async for record in await self.stream_async(
                limit=limit,
                page_size=page_size,
            )
        ]

    def page(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ConnectionPolicyPage:
        """
        Retrieve a single page of ConnectionPolicyInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ConnectionPolicyInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = self._version.page(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ConnectionPolicyPage(self._version, response)

    async def page_async(
        self,
        page_token: Union[str, object] = values.unset,
        page_number: Union[int, object] = values.unset,
        page_size: Union[int, object] = values.unset,
    ) -> ConnectionPolicyPage:
        """
        Asynchronously retrieve a single page of ConnectionPolicyInstance records from the API.
        Request is executed immediately

        :param page_token: PageToken provided by the API
        :param page_number: Page Number, this value is simply for client state
        :param page_size: Number of records to return, defaults to 50

        :returns: Page of ConnectionPolicyInstance
        """
        data = values.of(
            {
                "PageToken": page_token,
                "Page": page_number,
                "PageSize": page_size,
            }
        )

        headers = values.of({"Content-Type": "application/x-www-form-urlencoded"})

        headers["Accept"] = "application/json"

        response = await self._version.page_async(
            method="GET", uri=self._uri, params=data, headers=headers
        )
        return ConnectionPolicyPage(self._version, response)

    def get_page(self, target_url: str) -> ConnectionPolicyPage:
        """
        Retrieve a specific page of ConnectionPolicyInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ConnectionPolicyInstance
        """
        response = self._version.domain.twilio.request("GET", target_url)
        return ConnectionPolicyPage(self._version, response)

    async def get_page_async(self, target_url: str) -> ConnectionPolicyPage:
        """
        Asynchronously retrieve a specific page of ConnectionPolicyInstance records from the API.
        Request is executed immediately

        :param target_url: API-generated URL for the requested results page

        :returns: Page of ConnectionPolicyInstance
        """
        response = await self._version.domain.twilio.request_async("GET", target_url)
        return ConnectionPolicyPage(self._version, response)

    def get(self, sid: str) -> ConnectionPolicyContext:
        """
        Constructs a ConnectionPolicyContext

        :param sid: The unique string that we created to identify the Connection Policy resource to update.
        """
        return ConnectionPolicyContext(self._version, sid=sid)

    def __call__(self, sid: str) -> ConnectionPolicyContext:
        """
        Constructs a ConnectionPolicyContext

        :param sid: The unique string that we created to identify the Connection Policy resource to update.
        """
        return ConnectionPolicyContext(self._version, sid=sid)

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Voice.V1.ConnectionPolicyList>"
