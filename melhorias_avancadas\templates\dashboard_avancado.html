<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Financeiro Avançado - Análise por Unidade</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --dark-bg: #343a40;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-bottom: 20px;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
            padding: 15px 20px;
        }

        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .metric-change {
            font-size: 0.8rem;
            margin-top: 5px;
        }

        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--secondary-color), #2980b9);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .status-critico { background-color: var(--danger-color); color: white; }
        .status-urgente { background-color: var(--warning-color); color: white; }
        .status-atencao { background-color: var(--info-color); color: white; }
        .status-normal { background-color: var(--success-color); color: white; }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .spinner-border {
            width: 3rem;
            height: 3rem;
        }

        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }

        .table th {
            background-color: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
        }

        .table td {
            border-color: #dee2e6;
            vertical-align: middle;
        }

        .alert {
            border: none;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .progress {
            height: 8px;
            border-radius: 10px;
        }

        @media (max-width: 768px) {
            .metric-value {
                font-size: 1.8rem;
            }
            
            .chart-container {
                height: 300px;
            }
            
            .card {
                margin-bottom: 15px;
            }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .unit-selector {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 10px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .unit-selector:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                Dashboard Financeiro Avançado
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-clock me-1"></i>
                    <span id="last-update">Carregando...</span>
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="refreshData()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Atualizar
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- Filtros Avançados -->
        <div class="filter-section fade-in">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <label for="unidade-select" class="form-label fw-bold">
                        <i class="fas fa-building me-2"></i>Selecionar Unidade:
                    </label>
                    <select id="unidade-select" class="form-select unit-selector" onchange="filtrarPorUnidade()">
                        <option value="TODAS">Carregando unidades...</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-bold">
                        <i class="fas fa-calendar me-2"></i>Período de Análise:
                    </label>
                    <select class="form-select unit-selector" id="periodo-select" onchange="aplicarFiltros()">
                        <option value="30">Últimos 30 dias</option>
                        <option value="60">Últimos 60 dias</option>
                        <option value="90" selected>Últimos 90 dias</option>
                        <option value="180">Últimos 6 meses</option>
                        <option value="365">Último ano</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label fw-bold">
                        <i class="fas fa-cog me-2"></i>Ações Rápidas:
                    </label>
                    <div class="d-flex gap-2">
                        <button class="btn btn-primary btn-sm" onclick="exportarRelatorio()">
                            <i class="fas fa-download me-1"></i>Exportar
                        </button>
                        <button class="btn btn-info btn-sm" onclick="mostrarAnaliseDetalhada()">
                            <i class="fas fa-chart-pie me-1"></i>Análise
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Métricas Principais -->
        <div class="row" id="metricas-principais">
            <div class="col-lg-3 col-md-6">
                <div class="metric-card fade-in">
                    <div class="metric-value" id="total-nfs">-</div>
                    <div class="metric-label">Total de NFes</div>
                    <div class="metric-change" id="change-nfs">-</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card fade-in">
                    <div class="metric-value" id="valor-total">-</div>
                    <div class="metric-label">Valor Total</div>
                    <div class="metric-change" id="change-valor">-</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card fade-in">
                    <div class="metric-value" id="valor-medio">-</div>
                    <div class="metric-label">Valor Médio por NF</div>
                    <div class="metric-change" id="change-medio">-</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card fade-in">
                    <div class="metric-value" id="unidades-ativas">-</div>
                    <div class="metric-label">Unidades Ativas</div>
                    <div class="metric-change" id="change-unidades">-</div>
                </div>
            </div>
        </div>

        <!-- Alertas e Vencimentos -->
        <div class="row">
            <div class="col-md-6">
                <div class="card fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Alertas de Vencimento
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="alertas-vencimento">
                            <div class="loading">
                                <div class="spinner-border text-primary" role="status"></div>
                                <p class="mt-2">Carregando alertas...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            Projeção Fluxo de Caixa
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="fluxoCaixaChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Análises Avançadas -->
        <div class="row mt-4">
            <div class="col-lg-8">
                <div class="card fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            Evolução Mensal do Faturamento
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="evolucaoMensalChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-building me-2"></i>
                            Performance por Unidade
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="performanceUnidadesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Fornecedores e Análise Detalhada -->
        <div class="row mt-4">
            <div class="col-lg-6">
                <div class="card fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-truck me-2"></i>
                            Top Fornecedores
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="topFornecedoresChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="card fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-analytics me-2"></i>
                            Análise de Concentração
                        </h5>
                    </div>
                    <div class="card-body" id="analise-concentracao">
                        <div class="loading">
                            <div class="spinner-border text-primary" role="status"></div>
                            <p class="mt-2">Carregando análise...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabela Detalhada (apenas para unidade específica) -->
        <div class="row mt-4" id="secao-detalhes" style="display: none;">
            <div class="col-12">
                <div class="card fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            Detalhes da Unidade: <span id="nome-unidade-detalhes"></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="tabela-detalhes" class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Fornecedor</th>
                                        <th>Qtd NFes</th>
                                        <th>Valor Total</th>
                                        <th>Valor Médio</th>
                                        <th>% do Total</th>
                                        <th>Última NF</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Insights e Recomendações -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card fade-in">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-lightbulb me-2"></i>
                            Insights e Recomendações
                        </h5>
                    </div>
                    <div class="card-body" id="insights-recomendacoes">
                        <div class="loading">
                            <div class="spinner-border text-primary" role="status"></div>
                            <p class="mt-2">Gerando insights...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>

    <script>
        // Variáveis globais
        let unidadeSelecionada = 'TODAS';
        let charts = {};
        let dataCache = {};

        // Inicialização
        $(document).ready(function() {
            console.log('🚀 Iniciando Dashboard Financeiro Avançado');
            carregarUnidades();
            carregarDadosIniciais();

            // Auto-refresh a cada 2 minutos
            setInterval(refreshData, 120000);

            // Atualizar timestamp
            atualizarTimestamp();
            setInterval(atualizarTimestamp, 1000);
        });

        // Carregar lista de unidades
        async function carregarUnidades() {
            try {
                const response = await fetch('/api/unidades');
                const unidades = await response.json();

                const select = document.getElementById('unidade-select');
                select.innerHTML = '';

                unidades.forEach(unidade => {
                    const option = document.createElement('option');
                    option.value = unidade.value;
                    option.textContent = unidade.label;
                    select.appendChild(option);
                });

                console.log('✅ Unidades carregadas:', unidades.length);
            } catch (error) {
                console.error('❌ Erro ao carregar unidades:', error);
                mostrarAlerta('Erro ao carregar lista de unidades', 'danger');
            }
        }

        // Carregar dados iniciais
        async function carregarDadosIniciais() {
            mostrarLoading(true);

            try {
                await Promise.all([
                    carregarResumoExecutivo(),
                    carregarAnaliseVencimentos(),
                    carregarFluxoCaixa(),
                    carregarEvolucaoMensal(),
                    carregarTopFornecedores(),
                    carregarPerformanceUnidades(),
                    carregarAnaliseConcentracao(),
                    carregarInsights()
                ]);

                console.log('✅ Dados iniciais carregados');
            } catch (error) {
                console.error('❌ Erro ao carregar dados iniciais:', error);
                mostrarAlerta('Erro ao carregar dados do dashboard', 'danger');
            } finally {
                mostrarLoading(false);
            }
        }

        // Carregar resumo executivo
        async function carregarResumoExecutivo() {
            try {
                const url = `/api/resumo?unidade=${encodeURIComponent(unidadeSelecionada)}`;
                const response = await fetch(url);
                const data = await response.json();

                // Atualizar métricas principais
                document.getElementById('total-nfs').textContent = data.total_nfs?.toLocaleString('pt-BR') || '0';
                document.getElementById('valor-total').textContent = data.valor_total_formatado || 'R$ 0,00';
                document.getElementById('valor-medio').textContent = data.valor_medio_formatado || 'R$ 0,00';
                document.getElementById('unidades-ativas').textContent = data.unidades_ativas || '0';

                // Atualizar indicadores de mudança
                const variacao = data.variacao_percentual || 0;
                const changeElement = document.getElementById('change-valor');
                if (changeElement) {
                    const icon = variacao >= 0 ? '↗️' : '↘️';
                    const color = variacao >= 0 ? '#27ae60' : '#e74c3c';
                    changeElement.innerHTML = `<span style="color: ${color}">${icon} ${Math.abs(variacao).toFixed(1)}% vs mês anterior</span>`;
                }

                dataCache.resumo = data;
                console.log('✅ Resumo executivo atualizado');

            } catch (error) {
                console.error('❌ Erro ao carregar resumo:', error);
            }
        }

        // Carregar análise de vencimentos
        async function carregarAnaliseVencimentos() {
            try {
                const url = `/api/analise-vencimentos?unidade=${encodeURIComponent(unidadeSelecionada)}`;
                const response = await fetch(url);
                const data = await response.json();

                const container = document.getElementById('alertas-vencimento');

                if (!data.detalhes || Object.keys(data.detalhes).length === 0) {
                    container.innerHTML = '<p class="text-muted text-center">Nenhum dado de vencimento disponível</p>';
                    return;
                }

                let html = '';

                // Alertas críticos
                if (data.detalhes.vencido && data.detalhes.vencido.quantidade > 0) {
                    html += `
                        <div class="alert alert-danger d-flex align-items-center mb-2">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            <div>
                                <strong>${data.detalhes.vencido.quantidade} NFes VENCIDAS</strong><br>
                                <small>Valor: ${data.detalhes.vencido.valor_formatado}</small>
                            </div>
                        </div>
                    `;
                }

                if (data.detalhes.hoje && data.detalhes.hoje.quantidade > 0) {
                    html += `
                        <div class="alert alert-warning d-flex align-items-center mb-2">
                            <i class="fas fa-clock me-2"></i>
                            <div>
                                <strong>${data.detalhes.hoje.quantidade} NFes vencem HOJE</strong><br>
                                <small>Valor: ${data.detalhes.hoje.valor_formatado}</small>
                            </div>
                        </div>
                    `;
                }

                if (data.detalhes['7_dias'] && data.detalhes['7_dias'].quantidade > 0) {
                    html += `
                        <div class="alert alert-info d-flex align-items-center mb-2">
                            <i class="fas fa-calendar-alt me-2"></i>
                            <div>
                                <strong>${data.detalhes['7_dias'].quantidade} NFes vencem em 7 dias</strong><br>
                                <small>Valor: ${data.detalhes['7_dias'].valor_formatado}</small>
                            </div>
                        </div>
                    `;
                }

                // Resumo geral
                const totalVencimentos = Object.values(data.detalhes).reduce((sum, item) => sum + (item.quantidade || 0), 0);
                if (totalVencimentos === 0) {
                    html = '<div class="alert alert-success"><i class="fas fa-check-circle me-2"></i>Nenhum vencimento crítico!</div>';
                }

                container.innerHTML = html;
                console.log('✅ Análise de vencimentos atualizada');

            } catch (error) {
                console.error('❌ Erro ao carregar vencimentos:', error);
                document.getElementById('alertas-vencimento').innerHTML =
                    '<p class="text-danger">Erro ao carregar dados de vencimento</p>';
            }
        }

        // Carregar fluxo de caixa
        async function carregarFluxoCaixa() {
            try {
                const dias = document.getElementById('periodo-select')?.value || 90;
                const url = `/api/fluxo-caixa-projetado?unidade=${encodeURIComponent(unidadeSelecionada)}&dias=${dias}`;
                const response = await fetch(url);
                const data = await response.json();

                // Destruir gráfico anterior se existir
                if (charts.fluxoCaixa) {
                    charts.fluxoCaixa.destroy();
                }

                const ctx = document.getElementById('fluxoCaixaChart').getContext('2d');
                charts.fluxoCaixa = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.labels || [],
                        datasets: [
                            {
                                label: 'Entradas Previstas',
                                data: data.entradas || [],
                                borderColor: '#27ae60',
                                backgroundColor: 'rgba(39, 174, 96, 0.1)',
                                fill: true,
                                tension: 0.4
                            },
                            {
                                label: 'Saídas Previstas',
                                data: data.saidas || [],
                                borderColor: '#e74c3c',
                                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                                fill: true,
                                tension: 0.4
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `Projeção de Fluxo de Caixa - ${dias} dias`
                            },
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return 'R$ ' + value.toLocaleString('pt-BR');
                                    }
                                }
                            }
                        },
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        }
                    }
                });

                console.log('✅ Gráfico de fluxo de caixa atualizado');

            } catch (error) {
                console.error('❌ Erro ao carregar fluxo de caixa:', error);
            }
        }

        // Carregar evolução mensal
        async function carregarEvolucaoMensal() {
            try {
                const url = `/api/fluxo-mensal?unidade=${encodeURIComponent(unidadeSelecionada)}`;
                const response = await fetch(url);
                const data = await response.json();

                if (charts.evolucaoMensal) {
                    charts.evolucaoMensal.destroy();
                }

                const ctx = document.getElementById('evolucaoMensalChart').getContext('2d');
                charts.evolucaoMensal = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: data.labels || [],
                        datasets: [{
                            label: 'Faturamento Mensal',
                            data: data.data || [],
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            fill: true,
                            tension: 0.4,
                            pointBackgroundColor: '#3498db',
                            pointBorderColor: '#2980b9',
                            pointRadius: 5
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: `Evolução do Faturamento - ${unidadeSelecionada}`
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return 'R$ ' + value.toLocaleString('pt-BR');
                                    }
                                }
                            }
                        }
                    }
                });

                console.log('✅ Gráfico de evolução mensal atualizado');
            } catch (error) {
                console.error('❌ Erro ao carregar evolução mensal:', error);
            }
        }

        // Carregar top fornecedores
        async function carregarTopFornecedores() {
            try {
                const url = `/api/top-fornecedores?unidade=${encodeURIComponent(unidadeSelecionada)}&limit=10`;
                const response = await fetch(url);
                const data = await response.json();

                if (charts.topFornecedores) {
                    charts.topFornecedores.destroy();
                }

                const ctx = document.getElementById('topFornecedoresChart').getContext('2d');
                charts.topFornecedores = new Chart(ctx, {
                    type: 'horizontalBar',
                    data: {
                        labels: data.labels || [],
                        datasets: [{
                            label: 'Valor Total',
                            data: data.data || [],
                            backgroundColor: [
                                '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
                                '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#16a085'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        plugins: {
                            title: {
                                display: true,
                                text: 'Top 10 Fornecedores por Valor'
                            }
                        },
                        scales: {
                            x: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return 'R$ ' + value.toLocaleString('pt-BR');
                                    }
                                }
                            }
                        }
                    }
                });

                console.log('✅ Gráfico de top fornecedores atualizado');
            } catch (error) {
                console.error('❌ Erro ao carregar top fornecedores:', error);
            }
        }

        // Carregar performance das unidades
        async function carregarPerformanceUnidades() {
            try {
                const response = await fetch('/api/top-unidades?limit=10');
                const data = await response.json();

                if (charts.performanceUnidades) {
                    charts.performanceUnidades.destroy();
                }

                const ctx = document.getElementById('performanceUnidadesChart').getContext('2d');
                charts.performanceUnidades = new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: data.labels || [],
                        datasets: [{
                            data: data.data || [],
                            backgroundColor: [
                                '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
                                '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#16a085'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            title: {
                                display: true,
                                text: 'Distribuição por Unidade'
                            },
                            legend: {
                                position: 'bottom',
                                labels: {
                                    boxWidth: 12,
                                    font: {
                                        size: 10
                                    }
                                }
                            }
                        }
                    }
                });

                console.log('✅ Gráfico de performance das unidades atualizado');
            } catch (error) {
                console.error('❌ Erro ao carregar performance das unidades:', error);
            }
        }

        // Carregar análise de concentração
        async function carregarAnaliseConcentracao() {
            try {
                const url = `/api/top-fornecedores?unidade=${encodeURIComponent(unidadeSelecionada)}&limit=20`;
                const response = await fetch(url);
                const data = await response.json();

                const container = document.getElementById('analise-concentracao');

                if (!data.detalhes || data.detalhes.length === 0) {
                    container.innerHTML = '<p class="text-muted">Nenhum dado disponível para análise</p>';
                    return;
                }

                // Calcular concentração (Pareto)
                const totalGeral = data.detalhes.reduce((sum, item) => sum + item.valor_total, 0);
                let acumulado = 0;
                let top5Percent = 0;
                let top10Percent = 0;

                data.detalhes.forEach((item, index) => {
                    acumulado += item.valor_total;
                    const percentAcumulado = (acumulado / totalGeral) * 100;

                    if (index < 5) top5Percent = percentAcumulado;
                    if (index < 10) top10Percent = percentAcumulado;
                });

                const html = `
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-3 mb-2">
                                <h4 class="text-primary">${top5Percent.toFixed(1)}%</h4>
                                <small class="text-muted">Top 5 Fornecedores</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-3 mb-2">
                                <h4 class="text-info">${top10Percent.toFixed(1)}%</h4>
                                <small class="text-muted">Top 10 Fornecedores</small>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6>Análise de Concentração:</h6>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-primary" style="width: ${top5Percent}%"></div>
                        </div>
                        <small class="text-muted">
                            ${top5Percent > 80 ? '⚠️ Alta concentração de fornecedores' :
                              top5Percent > 60 ? '⚡ Concentração moderada' :
                              '✅ Boa diversificação de fornecedores'}
                        </small>
                    </div>
                `;

                container.innerHTML = html;
                console.log('✅ Análise de concentração atualizada');

            } catch (error) {
                console.error('❌ Erro ao carregar análise de concentração:', error);
                document.getElementById('analise-concentracao').innerHTML =
                    '<p class="text-danger">Erro ao carregar análise</p>';
            }
        }

        // Carregar insights
        async function carregarInsights() {
            try {
                const container = document.getElementById('insights-recomendacoes');

                // Gerar insights baseados nos dados carregados
                let insights = [];

                if (dataCache.resumo) {
                    const variacao = dataCache.resumo.variacao_percentual || 0;

                    if (variacao > 10) {
                        insights.push({
                            tipo: 'success',
                            icone: 'fas fa-arrow-up',
                            titulo: 'Crescimento Positivo',
                            descricao: `Faturamento cresceu ${variacao.toFixed(1)}% em relação ao mês anterior. Excelente performance!`
                        });
                    } else if (variacao < -10) {
                        insights.push({
                            tipo: 'warning',
                            icone: 'fas fa-arrow-down',
                            titulo: 'Atenção: Queda no Faturamento',
                            descricao: `Faturamento caiu ${Math.abs(variacao).toFixed(1)}% em relação ao mês anterior. Recomenda-se análise detalhada.`
                        });
                    }

                    if (dataCache.resumo.vencimentos_criticos > 0) {
                        insights.push({
                            tipo: 'danger',
                            icone: 'fas fa-exclamation-triangle',
                            titulo: 'Vencimentos Críticos',
                            descricao: `${dataCache.resumo.vencimentos_criticos} NFes com vencimento crítico. Ação imediata necessária.`
                        });
                    }
                }

                // Insights padrão se não houver dados específicos
                if (insights.length === 0) {
                    insights.push({
                        tipo: 'info',
                        icone: 'fas fa-chart-line',
                        titulo: 'Dashboard Atualizado',
                        descricao: 'Todos os dados foram carregados com sucesso. Use os filtros para análises mais detalhadas.'
                    });
                }

                const html = insights.map(insight => `
                    <div class="alert alert-${insight.tipo} d-flex align-items-center mb-2">
                        <i class="${insight.icone} me-3"></i>
                        <div>
                            <strong>${insight.titulo}</strong><br>
                            <small>${insight.descricao}</small>
                        </div>
                    </div>
                `).join('');

                container.innerHTML = html;
                console.log('✅ Insights gerados');

            } catch (error) {
                console.error('❌ Erro ao gerar insights:', error);
            }
        }

        // Filtrar por unidade
        function filtrarPorUnidade() {
            const select = document.getElementById('unidade-select');
            unidadeSelecionada = select.value;

            console.log('🔍 Filtrando por unidade:', unidadeSelecionada);

            // Mostrar/ocultar seção de detalhes
            const secaoDetalhes = document.getElementById('secao-detalhes');
            if (unidadeSelecionada !== 'TODAS') {
                secaoDetalhes.style.display = 'block';
                document.getElementById('nome-unidade-detalhes').textContent = unidadeSelecionada;
                carregarDetalhesUnidade();
            } else {
                secaoDetalhes.style.display = 'none';
            }

            // Mostrar loading
            mostrarLoading(true);

            // Recarregar todos os dados
            setTimeout(() => {
                carregarDadosIniciais();
            }, 500);
        }

        // Carregar detalhes da unidade
        async function carregarDetalhesUnidade() {
            if (unidadeSelecionada === 'TODAS') return;

            try {
                const url = `/api/analise-unidade/${encodeURIComponent(unidadeSelecionada)}`;
                const response = await fetch(url);
                const data = await response.json();

                if (data.erro) {
                    console.warn('Dados não encontrados para a unidade:', unidadeSelecionada);
                    return;
                }

                // Atualizar tabela de detalhes
                const tbody = document.querySelector('#tabela-detalhes tbody');
                tbody.innerHTML = '';

                if (data.top_fornecedores) {
                    Object.entries(data.top_fornecedores).forEach(([fornecedor, info]) => {
                        const percentual = ((info.sum / data.valor_total) * 100).toFixed(1);

                        const row = `
                            <tr>
                                <td>${fornecedor}</td>
                                <td>${info.count}</td>
                                <td>R$ ${info.sum.toLocaleString('pt-BR', {minimumFractionDigits: 2})}</td>
                                <td>R$ ${(info.sum / info.count).toLocaleString('pt-BR', {minimumFractionDigits: 2})}</td>
                                <td>${percentual}%</td>
                                <td>-</td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    });
                }

                // Inicializar DataTable se ainda não foi inicializado
                if (!$.fn.DataTable.isDataTable('#tabela-detalhes')) {
                    $('#tabela-detalhes').DataTable({
                        language: {
                            url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/pt-BR.json'
                        },
                        pageLength: 10,
                        order: [[2, 'desc']]
                    });
                }

                console.log('✅ Detalhes da unidade carregados');

            } catch (error) {
                console.error('❌ Erro ao carregar detalhes da unidade:', error);
            }
        }

        // Aplicar filtros
        function aplicarFiltros() {
            console.log('🔧 Aplicando filtros...');
            carregarFluxoCaixa();
        }

        // Refresh dos dados
        function refreshData() {
            console.log('🔄 Atualizando dados...');
            carregarDadosIniciais();

            // Mostrar feedback visual
            const btn = document.querySelector('[onclick="refreshData()"]');
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Atualizando...';
            btn.disabled = true;

            setTimeout(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }, 2000);
        }

        // Mostrar/ocultar loading
        function mostrarLoading(show) {
            const elementos = document.querySelectorAll('.loading');
            elementos.forEach(el => {
                el.style.display = show ? 'block' : 'none';
            });
        }

        // Mostrar alerta
        function mostrarAlerta(mensagem, tipo = 'info') {
            const alertaHtml = `
                <div class="alert alert-${tipo} alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    ${mensagem}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            const container = document.querySelector('.container-fluid');
            container.insertAdjacentHTML('afterbegin', alertaHtml);

            // Auto-remover após 5 segundos
            setTimeout(() => {
                const alerta = container.querySelector('.alert');
                if (alerta) alerta.remove();
            }, 5000);
        }

        // Atualizar timestamp
        function atualizarTimestamp() {
            const now = new Date();
            const timestamp = now.toLocaleString('pt-BR');
            const element = document.getElementById('last-update');
            if (element) {
                element.textContent = `Última atualização: ${timestamp}`;
            }
        }

        // Exportar relatório
        function exportarRelatorio() {
            mostrarAlerta('Funcionalidade de exportação em desenvolvimento', 'info');
        }

        // Mostrar análise detalhada
        function mostrarAnaliseDetalhada() {
            if (unidadeSelecionada === 'TODAS') {
                mostrarAlerta('Selecione uma unidade específica para análise detalhada', 'warning');
                return;
            }

            // Implementar modal com análise detalhada
            mostrarAlerta('Abrindo análise detalhada...', 'info');
        }

        // Tratamento de erros globais
        window.addEventListener('error', function(e) {
            console.error('❌ Erro global:', e.error);
            mostrarAlerta('Ocorreu um erro inesperado. Tente atualizar a página.', 'danger');
        });

        // Log de inicialização
        console.log('📊 Dashboard Financeiro Avançado carregado com sucesso!');
    </script>
</body>
</html>
