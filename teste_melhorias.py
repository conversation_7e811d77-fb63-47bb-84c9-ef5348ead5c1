#!/usr/bin/env python3
"""
Teste das melhorias implementadas no sistema de automação
- Processamento de XMLs de NFe
- Busca expandida de e-mails
- Extração detalhada de dados e impostos
"""

import os
import sys
import xml.etree.ElementTree as ET
from automacao_nf import extrair_dados_xml_nfe, extrair_itens_xml_nfe

def teste_xml_sample():
    """Testa o processamento de XML com dados de exemplo"""
    
    # XML de exemplo simplificado de NFe
    xml_exemplo = """<?xml version="1.0" encoding="UTF-8"?>
    <nfeProc xmlns="http://www.portalfiscal.inf.br/nfe">
        <NFe>
            <infNFe Id="NFe35200714200166000187550010000000046550000004">
                <ide>
                    <nNF>46</nNF>
                    <dhEmi>2020-07-01T10:30:00-03:00</dhEmi>
                </ide>
                <emit>
                    <CNPJ>14200166000187</CNPJ>
                    <xNome>EMPRESA EMITENTE LTDA</xNome>
                </emit>
                <dest>
                    <CNPJ>37053499000105</CNPJ>
                    <xNome>CLINICA ARAXA</xNome>
                </dest>
                <det nItem="1">
                    <prod>
                        <cProd>001</cProd>
                        <xProd>PRODUTO TESTE</xProd>
                        <NCM>12345678</NCM>
                        <CFOP>5102</CFOP>
                        <uCom>UN</uCom>
                        <qCom>10.0000</qCom>
                        <vUnCom>15.50</vUnCom>
                        <vProd>155.00</vProd>
                    </prod>
                    <imposto>
                        <ICMS>
                            <ICMS00>
                                <CST>00</CST>
                                <pICMS>18.00</pICMS>
                                <vICMS>27.90</vICMS>
                            </ICMS00>
                        </ICMS>
                        <PIS>
                            <PISAliq>
                                <CST>01</CST>
                                <pPIS>1.65</pPIS>
                                <vPIS>2.56</vPIS>
                            </PISAliq>
                        </PIS>
                        <COFINS>
                            <COFINSAliq>
                                <CST>01</CST>
                                <pCOFINS>7.60</pCOFINS>
                                <vCOFINS>11.78</vCOFINS>
                            </COFINSAliq>
                        </COFINS>
                    </imposto>
                </det>
                <total>
                    <ICMSTot>
                        <vProd>155.00</vProd>
                        <vNF>155.00</vNF>
                    </ICMSTot>
                </total>
                <cobr>
                    <dup>
                        <nDup>001</nDup>
                        <dVenc>2020-08-01</dVenc>
                        <vDup>155.00</vDup>
                    </dup>
                </cobr>
            </infNFe>
        </NFe>
    </nfeProc>"""
    
    print("=== TESTE DE PROCESSAMENTO XML ===")
    print("Testando extração de dados gerais...")
    
    dados_gerais = extrair_dados_xml_nfe(xml_exemplo)
    if dados_gerais:
        print("✅ Dados gerais extraídos com sucesso:")
        for chave, valor in dados_gerais.items():
            print(f"  {chave}: {valor}")
    else:
        print("❌ Falha na extração de dados gerais")
    
    print("\nTestando extração de itens...")
    itens = extrair_itens_xml_nfe(xml_exemplo)
    if itens:
        print(f"✅ {len(itens)} itens extraídos com sucesso:")
        for i, item in enumerate(itens, 1):
            print(f"  Item {i}:")
            for chave, valor in item.items():
                print(f"    {chave}: {valor}")
    else:
        print("❌ Falha na extração de itens")

def teste_palavras_chave():
    """Testa as novas palavras-chave de busca"""
    from automacao_nf import KW_NOTAS_FISCAIS
    
    print("\n=== TESTE DE PALAVRAS-CHAVE ===")
    print("Palavras-chave para Notas Fiscais:")
    for kw in KW_NOTAS_FISCAIS:
        print(f"  - {kw}")
    
    # Testa alguns assuntos de exemplo
    assuntos_teste = [
        "NFe - Nota Fiscal Eletrônica",
        "XML da NF 12345",
        "Danfe em anexo",
        "Nota fiscal de compra",
        "NF 67890 - Fornecedor XYZ"
    ]
    
    print("\nTestando classificação de assuntos:")
    from automacao_nf import classificar_documento
    
    for assunto in assuntos_teste:
        tipo = classificar_documento(assunto, "arquivo.pdf")
        print(f"  '{assunto}' -> {tipo}")

def verificar_estrutura_projeto():
    """Verifica se a estrutura do projeto está correta"""
    print("\n=== VERIFICAÇÃO DA ESTRUTURA ===")
    
    arquivos_necessarios = [
        'automacao_nf.py',
        'unidades_cnpjs.csv',
        'requirements.txt',
        'config.py'
    ]
    
    for arquivo in arquivos_necessarios:
        if os.path.exists(arquivo):
            print(f"✅ {arquivo}")
        else:
            print(f"❌ {arquivo} - AUSENTE")
    
    # Verifica pastas
    pastas = ['INCONSISTENCIAS', 'templates']
    for pasta in pastas:
        if os.path.exists(pasta):
            print(f"✅ Pasta {pasta}/")
        else:
            print(f"⚠️  Pasta {pasta}/ - será criada automaticamente")

if __name__ == "__main__":
    print("🚀 TESTE DAS MELHORIAS DO SISTEMA DE AUTOMAÇÃO")
    print("=" * 50)
    
    try:
        verificar_estrutura_projeto()
        teste_palavras_chave()
        teste_xml_sample()
        
        print("\n" + "=" * 50)
        print("✅ TESTES CONCLUÍDOS!")
        print("\n📋 RESUMO DAS MELHORIAS IMPLEMENTADAS:")
        print("  1. ✅ Processamento prioritário de XMLs de NFe")
        print("  2. ✅ Extração detalhada de impostos (ICMS, PIS, COFINS)")
        print("  3. ✅ Busca expandida de e-mails (XML, NF, NFE, etc.)")
        print("  4. ✅ Dados mais completos nas planilhas")
        print("  5. ✅ Fallback para PDF quando XML não disponível")
        
    except Exception as e:
        print(f"\n❌ ERRO durante os testes: {e}")
        import traceback
        traceback.print_exc()
