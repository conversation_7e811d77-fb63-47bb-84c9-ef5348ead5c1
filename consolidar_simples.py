import pandas as pd
import os

print("🚀 CO<PERSON><PERSON><PERSON><PERSON><PERSON>O FABRICANTES")
print("=" * 50)

# Fabricantes conhecidos
fabricantes = {
    'PFIZER': ['PFIZER', 'COMIRNATY', 'WYETH'],
    'GSK': ['GS<PERSON>', '<PERSON><PERSON><PERSON><PERSON>MITHKLINE', 'SHINGRI<PERSON>', 'AREXVY', '<PERSON><PERSON><PERSON><PERSON>'],
    'MERCK': ['MERCK', 'MSD', 'GARDAS<PERSON>'],
    'SANOFI': ['SANOFI', 'PASTEUR', 'AVENTIS'],
    'NOVARTIS': ['NOVARTIS', 'SANDOZ'],
    'ROCHE': ['ROCHE', 'GENENTECH'],
    'BAYER': ['BAYER'],
    'J<PERSON><PERSON><PERSON> & JOHNSON': ['J<PERSON><PERSON><PERSON>', 'J<PERSON><PERSON><PERSON>', 'J&<PERSON>'],
    'ASTRAZENECA': ['ASTRAZENECA', '<PERSON><PERSON>EC<PERSON>'],
    'M<PERSON>ER<PERSON>': ['MODERNA'],
    'ABBOTT': ['ABBOTT'],
    'BUTANTAN': ['BUT<PERSON><PERSON><PERSON>', 'INSTITUTO BUTANTAN'],
    'FIOCRUZ': ['FIOCRUZ', 'BIO-MANGUINHOS', 'BIOMANGUINHOS'],
    'SINOVAC': ['SINOVAC', 'CORONAVAC'],
    'EUROFARMA': ['EUROFARMA'],
    'EMS': ['EMS'],
    'HYPERA': ['HYPERA'],
    'CRISTÁLIA': ['CRISTALIA', 'CRISTÁLIA'],
    'UNIÃO QUÍMICA': ['UNIAO QUIMICA', 'UNIÃO QUÍMICA'],
    'MEDLEY': ['MEDLEY'],
    'GERMED': ['GERMED'],
    'BIOSINTÉTICA': ['BIOSINTETICA', 'BIOSINTÉTICA'],
    'BLAU': ['BLAU'],
    'APSEN': ['APSEN'],
    'GENÉRICO': ['GENERICO', 'GENÉRICO']
}

# Carregar planilha
df = pd.read_excel('controle_produtos.xlsx')
print(f"📋 Total: {len(df):,} registros")

# Verificar coluna Fabricante
if 'Fabricante' not in df.columns:
    df['Fabricante'] = ''

# Estatísticas antes
antes = df['Fabricante'].notna().sum() - (df['Fabricante'] == '').sum()
print(f"📊 ANTES: {antes:,} preenchidos ({antes/len(df)*100:.1f}%)")

# Consolidar
melhorias = 0
for i, row in df.iterrows():
    if i % 1000 == 0:
        print(f"   {i:,}/{len(df):,}")
    
    fab_atual = row.get('Fabricante', '')
    desc = row.get('Descricao Produto', '')
    
    if not fab_atual or fab_atual == '':
        if desc:
            desc_upper = str(desc).upper()
            for fab_nome, variacoes in fabricantes.items():
                for var in variacoes:
                    if var in desc_upper:
                        df.at[i, 'Fabricante'] = fab_nome
                        melhorias += 1
                        break
                if df.at[i, 'Fabricante'] != fab_atual:
                    break

# Estatísticas depois
depois = df['Fabricante'].notna().sum() - (df['Fabricante'] == '').sum()
print(f"📊 DEPOIS: {depois:,} preenchidos ({depois/len(df)*100:.1f}%)")
print(f"✅ MELHORIAS: {melhorias:,}")

# Salvar
df.to_excel('controle_produtos.xlsx', index=False)
print("💾 Salvo!")

# Top fabricantes
top = df[df['Fabricante'] != '']['Fabricante'].value_counts().head(5)
print("\n🏭 TOP 5 FABRICANTES:")
for i, (fab, count) in enumerate(top.items(), 1):
    print(f"   {i}. {fab}: {count:,}")

print("\n🎯 CONCLUÍDO!")
