#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SISTEMA INTEGRADO DE MELHORIAS
Conecta Dashboard Web + WhatsApp + IA em um sistema único
"""

import os
import sys
import subprocess
import threading
import time
from datetime import datetime
import json
import webbrowser

class SistemaIntegrado:
    def __init__(self):
        self.dashboard_ativo = False
        self.whatsapp_configurado = False
        self.ia_funcionando = False
        self.verificar_componentes()
    
    def verificar_componentes(self):
        """Verifica status de todos os componentes"""
        print("🔍 VERIFICANDO COMPONENTES DO SISTEMA INTEGRADO")
        print("=" * 60)
        
        # Verificar Dashboard Web
        if os.path.exists('dashboard_web.py') and os.path.exists('templates/dashboard.html'):
            print("✅ Dashboard Web: Disponível")
            self.dashboard_ativo = True
        else:
            print("❌ Dashboard Web: Arquivos não encontrados")
        
        # Verificar WhatsApp
        if os.path.exists('sistema_notificacoes_whatsapp.py'):
            if os.path.exists('config_whatsapp.json'):
                print("✅ WhatsApp: Configurado")
                self.whatsapp_configurado = True
            else:
                print("⚠️ WhatsApp: Disponível (necessita configuração)")
        else:
            print("❌ WhatsApp: Arquivo não encontrado")
        
        # Verificar IA
        if os.path.exists('ia_previsoes_financeiras.py'):
            print("✅ IA Previsões: Disponível")
            self.ia_funcionando = True
        else:
            print("❌ IA Previsões: Arquivo não encontrado")
        
        # Verificar dados
        planilhas = ['controle_produtos.xlsx', 'controle_faturamento_geral.xlsx']
        dados_disponiveis = all(os.path.exists(p) for p in planilhas)
        
        if dados_disponiveis:
            print("✅ Dados: Planilhas disponíveis")
        else:
            print("⚠️ Dados: Algumas planilhas não encontradas")
    
    def iniciar_dashboard_web(self):
        """Inicia o dashboard web em background"""
        if not self.dashboard_ativo:
            print("❌ Dashboard Web não disponível")
            return False
        
        try:
            print("🌐 Iniciando Dashboard Web...")
            
            # Iniciar Flask em background
            subprocess.Popen([
                sys.executable, 'dashboard_web.py'
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # Aguardar alguns segundos para o servidor iniciar
            time.sleep(3)
            
            # Abrir navegador
            webbrowser.open('http://localhost:5000')
            
            print("✅ Dashboard Web iniciado em: http://localhost:5000")
            return True
            
        except Exception as e:
            print(f"❌ Erro ao iniciar Dashboard Web: {e}")
            return False
    
    def executar_ia_previsoes(self):
        """Executa análise de IA e gera previsões"""
        if not self.ia_funcionando:
            print("❌ IA não disponível")
            return False
        
        try:
            print("🤖 Executando análise de IA...")
            
            # Executar IA com opção de relatório completo
            resultado = subprocess.run([
                sys.executable, 'ia_previsoes_financeiras.py'
            ], input='5\n6\n', capture_output=True, text=True, timeout=60)
            
            if resultado.returncode == 0:
                print("✅ Análise de IA concluída")
                
                # Verificar se arquivo de previsões foi gerado
                if os.path.exists('previsoes_ia.json'):
                    print("📊 Arquivo de previsões gerado: previsoes_ia.json")
                    return True
                else:
                    print("⚠️ Análise executada, mas arquivo não gerado")
                    return False
            else:
                print(f"❌ Erro na análise de IA: {resultado.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("⏰ Timeout na análise de IA")
            return False
        except Exception as e:
            print(f"❌ Erro ao executar IA: {e}")
            return False
    
    def enviar_notificacoes_whatsapp(self):
        """Envia notificações via WhatsApp"""
        if not os.path.exists('sistema_notificacoes_whatsapp.py'):
            print("❌ Sistema WhatsApp não disponível")
            return False
        
        try:
            print("📱 Enviando notificações WhatsApp...")
            
            # Executar sistema WhatsApp com monitoramento automático
            resultado = subprocess.run([
                sys.executable, 'sistema_notificacoes_whatsapp.py'
            ], input='3\n5\n', capture_output=True, text=True, timeout=30)
            
            if resultado.returncode == 0:
                print("✅ Notificações WhatsApp processadas")
                return True
            else:
                print(f"⚠️ WhatsApp executado com avisos: {resultado.stderr}")
                return True  # Considerar sucesso mesmo com avisos
                
        except subprocess.TimeoutExpired:
            print("⏰ Timeout nas notificações WhatsApp")
            return False
        except Exception as e:
            print(f"❌ Erro ao enviar WhatsApp: {e}")
            return False
    
    def executar_sistema_completo(self):
        """Executa o sistema completo integrado"""
        print("🚀 EXECUTANDO SISTEMA INTEGRADO COMPLETO")
        print("=" * 60)
        
        resultados = {}
        
        # 1. Iniciar Dashboard Web
        print("\n1️⃣ INICIANDO DASHBOARD WEB...")
        resultados['dashboard'] = self.iniciar_dashboard_web()
        
        # 2. Executar IA
        print("\n2️⃣ EXECUTANDO ANÁLISE DE IA...")
        resultados['ia'] = self.executar_ia_previsoes()
        
        # 3. Enviar WhatsApp
        print("\n3️⃣ ENVIANDO NOTIFICAÇÕES WHATSAPP...")
        resultados['whatsapp'] = self.enviar_notificacoes_whatsapp()
        
        # 4. Resumo final
        print("\n📊 RESUMO DA EXECUÇÃO:")
        print("=" * 60)
        
        sucessos = sum(resultados.values())
        total = len(resultados)
        
        for componente, sucesso in resultados.items():
            emoji = "✅" if sucesso else "❌"
            print(f"   {emoji} {componente.upper()}: {'Sucesso' if sucesso else 'Falha'}")
        
        print(f"\n🎯 RESULTADO GERAL: {sucessos}/{total} componentes executados com sucesso")
        
        if sucessos == total:
            print("🎉 SISTEMA INTEGRADO FUNCIONANDO PERFEITAMENTE!")
        elif sucessos >= total * 0.7:
            print("✅ Sistema funcionando bem (alguns componentes com problemas)")
        else:
            print("⚠️ Sistema com problemas - verificar configurações")
        
        return resultados
    
    def monitoramento_continuo(self):
        """Executa monitoramento contínuo do sistema"""
        print("🔄 INICIANDO MONITORAMENTO CONTÍNUO")
        print("=" * 60)
        print("⏰ Execução a cada 30 minutos")
        print("🌐 Dashboard Web sempre ativo")
        print("📱 Notificações automáticas")
        print("🤖 IA executada periodicamente")
        print("\n💡 Pressione Ctrl+C para parar")
        
        # Iniciar dashboard uma vez
        self.iniciar_dashboard_web()
        
        try:
            while True:
                print(f"\n⏰ {datetime.now().strftime('%H:%M:%S')} - Executando ciclo de monitoramento...")
                
                # Executar IA e WhatsApp
                self.executar_ia_previsoes()
                self.enviar_notificacoes_whatsapp()
                
                print("✅ Ciclo concluído - próximo em 30 minutos")
                
                # Aguardar 30 minutos
                time.sleep(1800)  # 30 minutos
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoramento interrompido pelo usuário")
        except Exception as e:
            print(f"\n❌ Erro no monitoramento: {e}")
    
    def configurar_whatsapp(self):
        """Ajuda a configurar WhatsApp"""
        print("📱 CONFIGURAÇÃO DO WHATSAPP")
        print("=" * 60)
        
        if os.path.exists('config_whatsapp.json'):
            print("✅ Arquivo de configuração encontrado")
            
            try:
                with open('config_whatsapp.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                print("\n📋 Configuração atual:")
                print(f"   Account SID: {config.get('account_sid', 'Não configurado')}")
                print(f"   Auth Token: {'*' * 20 if config.get('auth_token') != 'SEU_TWILIO_AUTH_TOKEN' else 'Não configurado'}")
                print(f"   Números: {len(config.get('numeros_gestores', []))} configurados")
                
                if config.get('account_sid') == 'SEU_TWILIO_ACCOUNT_SID':
                    print("\n⚠️ CONFIGURAÇÃO NECESSÁRIA:")
                    print("1. Crie conta no Twilio: https://console.twilio.com/")
                    print("2. Obtenha Account SID e Auth Token")
                    print("3. Configure números no formato: whatsapp:+*************")
                    print("4. Edite o arquivo config_whatsapp.json")
                else:
                    print("\n✅ Configuração parece estar completa")
                
            except Exception as e:
                print(f"❌ Erro ao ler configuração: {e}")
        else:
            print("❌ Arquivo de configuração não encontrado")
            print("💡 Execute: python sistema_notificacoes_whatsapp.py para criar")
    
    def menu_principal(self):
        """Menu principal do sistema integrado"""
        while True:
            print(f"\n🚀 SISTEMA INTEGRADO DE MELHORIAS")
            print("=" * 60)
            print("1. 🌐 Iniciar Dashboard Web")
            print("2. 🤖 Executar Análise de IA")
            print("3. 📱 Enviar Notificações WhatsApp")
            print("4. 🎯 Executar Sistema Completo")
            print("5. 🔄 Monitoramento Contínuo")
            print("6. ⚙️ Configurar WhatsApp")
            print("7. 🔍 Verificar Componentes")
            print("8. ❌ Sair")
            
            opcao = input("\nEscolha uma opção (1-8): ").strip()
            
            if opcao == '1':
                self.iniciar_dashboard_web()
            elif opcao == '2':
                self.executar_ia_previsoes()
            elif opcao == '3':
                self.enviar_notificacoes_whatsapp()
            elif opcao == '4':
                self.executar_sistema_completo()
            elif opcao == '5':
                self.monitoramento_continuo()
            elif opcao == '6':
                self.configurar_whatsapp()
            elif opcao == '7':
                self.verificar_componentes()
            elif opcao == '8':
                print("👋 Encerrando sistema integrado...")
                break
            else:
                print("❌ Opção inválida!")
            
            if opcao != '5':  # Não pausar após monitoramento contínuo
                input("\nPressione ENTER para continuar...")

def main():
    """Função principal"""
    print("🚀 INICIANDO SISTEMA INTEGRADO DE MELHORIAS")
    print("=" * 60)
    print("🌐 Dashboard Web Interativo")
    print("📱 Notificações WhatsApp")
    print("🤖 Inteligência Artificial")
    print("=" * 60)
    
    sistema = SistemaIntegrado()
    
    # Verificar argumentos da linha de comando
    if len(sys.argv) > 1:
        if sys.argv[1] == '--completo':
            sistema.executar_sistema_completo()
        elif sys.argv[1] == '--dashboard':
            sistema.iniciar_dashboard_web()
            input("Pressione ENTER para encerrar...")
        elif sys.argv[1] == '--ia':
            sistema.executar_ia_previsoes()
        elif sys.argv[1] == '--whatsapp':
            sistema.enviar_notificacoes_whatsapp()
        elif sys.argv[1] == '--monitoramento':
            sistema.monitoramento_continuo()
        else:
            print(f"❌ Argumento inválido: {sys.argv[1]}")
            print("Argumentos válidos: --completo, --dashboard, --ia, --whatsapp, --monitoramento")
    else:
        # Menu interativo
        sistema.menu_principal()

if __name__ == '__main__':
    main()
