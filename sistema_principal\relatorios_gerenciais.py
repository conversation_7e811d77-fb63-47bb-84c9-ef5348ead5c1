#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GERADOR DE RELATÓRIOS GERENCIAIS
Relatórios executivos automáticos para tomada de decisão
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os
from openpyxl import Workbook
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows

class GeradorRelatorios:
    def __init__(self):
        self.df_produtos = None
        self.df_faturamento = None
        self.df_boletos = None
        self.carregar_dados()
    
    def carregar_dados(self):
        """Carrega dados das planilhas"""
        try:
            if os.path.exists('controle_produtos.xlsx'):
                self.df_produtos = pd.read_excel('controle_produtos.xlsx')
                self.processar_dados_produtos()
            
            if os.path.exists('controle_faturamento_geral.xlsx'):
                self.df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
                self.processar_dados_faturamento()
            
            if os.path.exists('controle_boletos.xlsx'):
                self.df_boletos = pd.read_excel('controle_boletos.xlsx')
                self.processar_dados_boletos()
                
        except Exception as e:
            print(f"❌ Erro ao carregar dados: {e}")
    
    def processar_dados_produtos(self):
        """Processa dados de produtos"""
        if self.df_produtos is not None:
            self.df_produtos['Valor_Total_Numerico'] = self.df_produtos['Valor Total Item'].apply(self.converter_valor_brasileiro)
            self.df_produtos['Valor_Unitario_Numerico'] = self.df_produtos['Valor Unitario'].apply(self.converter_valor_brasileiro)
            self.df_produtos['Quantidade_Numerica'] = self.df_produtos['Quantidade'].apply(self.converter_quantidade_brasileira)
            self.df_produtos['Data_Emissao_Dt'] = pd.to_datetime(self.df_produtos['Data Emissao'], format='%d/%m/%Y', errors='coerce')
    
    def processar_dados_faturamento(self):
        """Processa dados de faturamento"""
        if self.df_faturamento is not None:
            self.df_faturamento['Valor_Numerico'] = self.df_faturamento['Valor Total NF'].apply(self.converter_valor_brasileiro)
            self.df_faturamento['Valor_Parcela_Numerico'] = self.df_faturamento['Valor Parcela'].apply(self.converter_valor_brasileiro)
            self.df_faturamento['Data_Emissao_Dt'] = pd.to_datetime(self.df_faturamento['Data Emissao'], format='%d/%m/%Y', errors='coerce')
            self.df_faturamento['Data_Vencimento_Dt'] = pd.to_datetime(self.df_faturamento['Data Vencimento'], format='%d/%m/%Y', errors='coerce')
    
    def processar_dados_boletos(self):
        """Processa dados de boletos"""
        if self.df_boletos is not None:
            self.df_boletos['Valor_Numerico'] = self.df_boletos['Valor Documento'].apply(self.converter_valor_brasileiro)
            self.df_boletos['Data_Vencimento_Dt'] = pd.to_datetime(self.df_boletos['Data Vencimento'], format='%d/%m/%Y', errors='coerce')
    
    def converter_valor_brasileiro(self, valor):
        """Converte valor brasileiro para float"""
        try:
            if pd.isna(valor) or valor == '':
                return 0.0
            valor_str = str(valor).replace('R$', '').replace(' ', '').replace('.', '').replace(',', '.')
            return float(valor_str)
        except:
            return 0.0
    
    def converter_quantidade_brasileira(self, quantidade):
        """Converte quantidade brasileira para float"""
        try:
            if pd.isna(quantidade) or quantidade == '':
                return 0.0
            quantidade_str = str(quantidade).replace(',', '.')
            return float(quantidade_str)
        except:
            return 0.0
    
    def formatar_valor_relatorio(self, valor):
        """Formata valor para relatório"""
        return f"R$ {valor:,.2f}".replace(',', 'X').replace('.', ',').replace('X', '.')
    
    def gerar_relatorio_mensal(self, mes=None, ano=None):
        """Gera relatório mensal detalhado"""
        if mes is None:
            mes = datetime.now().month
        if ano is None:
            ano = datetime.now().year
        
        print(f"\n📊 GERANDO RELATÓRIO MENSAL - {mes:02d}/{ano}")
        print("="*60)
        
        # Filtrar dados do mês
        if self.df_faturamento is not None:
            df_mes = self.df_faturamento[
                (self.df_faturamento['Data_Emissao_Dt'].dt.month == mes) &
                (self.df_faturamento['Data_Emissao_Dt'].dt.year == ano)
            ]
            
            if len(df_mes) > 0:
                # Resumo geral do mês
                total_nfs = len(df_mes)
                valor_total = df_mes['Valor_Numerico'].sum()
                unidades_ativas = df_mes['Unidade'].nunique()
                
                print(f"💰 RESUMO GERAL:")
                print(f"   Total de NFs: {total_nfs}")
                print(f"   Valor Total: {self.formatar_valor_relatorio(valor_total)}")
                print(f"   Unidades Ativas: {unidades_ativas}")
                
                # Performance por unidade
                print(f"\n🏪 PERFORMANCE POR UNIDADE:")
                performance = df_mes.groupby('Unidade').agg({
                    'Valor_Numerico': ['sum', 'count', 'mean']
                }).round(2)
                
                performance.columns = ['Valor_Total', 'Qtd_NFs', 'Valor_Medio']
                performance = performance.sort_values('Valor_Total', ascending=False)
                
                for unidade, row in performance.head(10).iterrows():
                    print(f"   {unidade}:")
                    print(f"      Valor: {self.formatar_valor_relatorio(row['Valor_Total'])}")
                    print(f"      NFs: {int(row['Qtd_NFs'])}")
                    print(f"      Média: {self.formatar_valor_relatorio(row['Valor_Medio'])}")
                
                # Top fornecedores
                print(f"\n🏭 TOP FORNECEDORES:")
                top_fornecedores = df_mes.groupby('Fornecedor')['Valor_Numerico'].sum().sort_values(ascending=False).head(5)
                
                for i, (fornecedor, valor) in enumerate(top_fornecedores.items(), 1):
                    fornecedor_nome = fornecedor[:40] + "..." if len(fornecedor) > 40 else fornecedor
                    print(f"   {i}. {fornecedor_nome}: {self.formatar_valor_relatorio(valor)}")
                
                return {
                    'mes': mes,
                    'ano': ano,
                    'total_nfs': total_nfs,
                    'valor_total': valor_total,
                    'unidades_ativas': unidades_ativas,
                    'performance_unidades': performance,
                    'top_fornecedores': top_fornecedores
                }
            else:
                print("❌ Nenhum dado encontrado para o período")
                return None
    
    def gerar_relatorio_anual(self, ano=None):
        """Gera relatório anual consolidado"""
        if ano is None:
            ano = datetime.now().year
        
        print(f"\n📈 RELATÓRIO ANUAL - {ano}")
        print("="*60)
        
        if self.df_faturamento is not None:
            df_ano = self.df_faturamento[self.df_faturamento['Data_Emissao_Dt'].dt.year == ano]
            
            if len(df_ano) > 0:
                # Análise mensal
                print("📊 EVOLUÇÃO MENSAL:")
                evolucao_mensal = df_ano.groupby(df_ano['Data_Emissao_Dt'].dt.month).agg({
                    'Valor_Numerico': 'sum',
                    'Numero Nota': 'count'
                }).round(2)
                
                meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 
                        'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez']
                
                for mes_num, row in evolucao_mensal.iterrows():
                    mes_nome = meses[mes_num - 1]
                    print(f"   {mes_nome}: {self.formatar_valor_relatorio(row['Valor_Numerico'])} ({int(row['Numero Nota'])} NFs)")
                
                # Consolidado anual por unidade
                print(f"\n🏪 CONSOLIDADO ANUAL POR UNIDADE:")
                consolidado = df_ano.groupby('Unidade').agg({
                    'Valor_Numerico': 'sum',
                    'Numero Nota': 'count'
                }).sort_values('Valor_Numerico', ascending=False)
                
                for unidade, row in consolidado.head(15).iterrows():
                    print(f"   {unidade}: {self.formatar_valor_relatorio(row['Valor_Numerico'])} ({int(row['Numero Nota'])} NFs)")
    
    def gerar_relatorio_vencimentos(self):
        """Gera relatório de vencimentos"""
        print(f"\n📅 RELATÓRIO DE VENCIMENTOS")
        print("="*60)
        
        hoje = datetime.now()
        
        if self.df_faturamento is not None:
            df_venc = self.df_faturamento[self.df_faturamento['Data_Vencimento_Dt'].notna()].copy()
            
            # Vencimentos por período
            periodos = [
                (7, "Próximos 7 dias"),
                (15, "Próximos 15 dias"),
                (30, "Próximos 30 dias"),
                (60, "Próximos 60 dias")
            ]
            
            for dias, descricao in periodos:
                data_limite = hoje + timedelta(days=dias)
                vencimentos = df_venc[
                    (df_venc['Data_Vencimento_Dt'] >= hoje) & 
                    (df_venc['Data_Vencimento_Dt'] <= data_limite)
                ]
                
                if len(vencimentos) > 0:
                    valor_total = vencimentos['Valor_Parcela_Numerico'].sum()
                    print(f"\n⏰ {descricao.upper()}:")
                    print(f"   Quantidade: {len(vencimentos)} parcelas")
                    print(f"   Valor Total: {self.formatar_valor_relatorio(valor_total)}")
                    
                    # Top 5 maiores valores
                    top_vencimentos = vencimentos.nlargest(5, 'Valor_Parcela_Numerico')
                    print(f"   Maiores valores:")
                    for _, row in top_vencimentos.iterrows():
                        data_venc = row['Data_Vencimento_Dt'].strftime('%d/%m/%Y')
                        valor = self.formatar_valor_relatorio(row['Valor_Parcela_Numerico'])
                        print(f"      {row['Unidade']} - {data_venc} - {valor}")
    
    def exportar_relatorio_excel(self, dados_relatorio, nome_arquivo):
        """Exporta relatório para Excel formatado"""
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "Relatório Gerencial"
            
            # Cabeçalho
            ws['A1'] = f"RELATÓRIO GERENCIAL - {dados_relatorio['mes']:02d}/{dados_relatorio['ano']}"
            ws['A1'].font = Font(bold=True, size=14)
            ws['A1'].fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            
            # Dados resumo
            linha = 3
            ws[f'A{linha}'] = "RESUMO GERAL"
            ws[f'A{linha}'].font = Font(bold=True)
            linha += 1
            
            ws[f'A{linha}'] = "Total de NFs:"
            ws[f'B{linha}'] = dados_relatorio['total_nfs']
            linha += 1
            
            ws[f'A{linha}'] = "Valor Total:"
            ws[f'B{linha}'] = self.formatar_valor_relatorio(dados_relatorio['valor_total'])
            linha += 1
            
            ws[f'A{linha}'] = "Unidades Ativas:"
            ws[f'B{linha}'] = dados_relatorio['unidades_ativas']
            linha += 2
            
            # Performance por unidade
            ws[f'A{linha}'] = "PERFORMANCE POR UNIDADE"
            ws[f'A{linha}'].font = Font(bold=True)
            linha += 1
            
            # Cabeçalhos da tabela
            headers = ['Unidade', 'Valor Total', 'Qtd NFs', 'Valor Médio']
            for col, header in enumerate(headers, 1):
                ws.cell(row=linha, column=col, value=header).font = Font(bold=True)
            linha += 1
            
            # Dados da performance
            for unidade, row in dados_relatorio['performance_unidades'].iterrows():
                ws.cell(row=linha, column=1, value=unidade)
                ws.cell(row=linha, column=2, value=self.formatar_valor_relatorio(row['Valor_Total']))
                ws.cell(row=linha, column=3, value=int(row['Qtd_NFs']))
                ws.cell(row=linha, column=4, value=self.formatar_valor_relatorio(row['Valor_Medio']))
                linha += 1
            
            # Ajustar largura das colunas
            for col in ws.columns:
                max_length = 0
                column = col[0].column_letter
                for cell in col:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column].width = adjusted_width
            
            wb.save(nome_arquivo)
            print(f"📊 Relatório exportado para: {nome_arquivo}")
            
        except Exception as e:
            print(f"❌ Erro ao exportar relatório: {e}")
    
    def gerar_relatorio_completo(self):
        """Gera relatório completo"""
        print("📋 GERANDO RELATÓRIO GERENCIAL COMPLETO...")
        
        # Relatório mensal atual
        dados_mensal = self.gerar_relatorio_mensal()
        
        # Relatório anual
        self.gerar_relatorio_anual()
        
        # Relatório de vencimentos
        self.gerar_relatorio_vencimentos()
        
        # Exportar para Excel se há dados
        if dados_mensal:
            nome_arquivo = f"relatorio_gerencial_{dados_mensal['mes']:02d}_{dados_mensal['ano']}.xlsx"
            self.exportar_relatorio_excel(dados_mensal, nome_arquivo)
        
        print("\n✅ RELATÓRIO GERENCIAL CONCLUÍDO!")

def main():
    gerador = GeradorRelatorios()
    gerador.gerar_relatorio_completo()

if __name__ == '__main__':
    main()
