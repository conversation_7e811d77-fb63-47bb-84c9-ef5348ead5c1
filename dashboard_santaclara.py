#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import numpy as np
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Configuração da página
st.set_page_config(
    page_title="SantaClara - Dashboard Financeiro",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# CSS personalizado com cores da SantaClara
st.markdown("""
<style>
    /* Cores principais da SantaClara */
    :root {
        --santaclara-azul: #1E3A8A;
        --santaclara-verde: #059669;
        --santaclara-laranja: #EA580C;
        --santaclara-cinza: #6B7280;
        --santaclara-azul-claro: #3B82F6;
    }
    
    /* Header personalizado */
    .main-header {
        background: linear-gradient(90deg, #1E3A8A 0%, #3B82F6 100%);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
    }
    
    .main-header h1 {
        color: white !important;
        margin: 0;
        font-size: 2.5rem;
        font-weight: bold;
    }
    
    .main-header p {
        color: #E5E7EB;
        margin: 0.5rem 0 0 0;
        font-size: 1.1rem;
    }
    
    /* Cards de métricas */
    .metric-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid var(--santaclara-azul);
        margin-bottom: 1rem;
    }
    
    .metric-card.verde {
        border-left-color: var(--santaclara-verde);
    }
    
    .metric-card.laranja {
        border-left-color: var(--santaclara-laranja);
    }
    
    .metric-card.azul-claro {
        border-left-color: var(--santaclara-azul-claro);
    }
    
    /* Sidebar personalizada */
    .css-1d391kg {
        background-color: #F8FAFC;
    }
    
    /* Botões personalizados */
    .stButton > button {
        background-color: var(--santaclara-azul);
        color: white;
        border: none;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .stButton > button:hover {
        background-color: var(--santaclara-azul-claro);
        transform: translateY(-2px);
    }
    
    /* Selectbox personalizado */
    .stSelectbox > div > div {
        border-color: var(--santaclara-azul);
    }
    
    /* Gráficos com bordas */
    .plotly-graph-div {
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* Footer */
    .footer {
        text-align: center;
        padding: 2rem;
        color: var(--santaclara-cinza);
        border-top: 1px solid #E5E7EB;
        margin-top: 3rem;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def carregar_dados():
    """Carrega os dados dos arquivos Excel"""
    try:
        # Carregar dados de produtos
        df_produtos = pd.read_excel('controle_produtos.xlsx')

        # Carregar dados de faturamento (se existir)
        try:
            df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
        except:
            df_faturamento = pd.DataFrame()

        # Converter datas
        if 'Data Emissao' in df_produtos.columns:
            df_produtos['Data Emissao'] = pd.to_datetime(df_produtos['Data Emissao'], errors='coerce')

        if not df_faturamento.empty and 'Data Emissao' in df_faturamento.columns:
            df_faturamento['Data Emissao'] = pd.to_datetime(df_faturamento['Data Emissao'], errors='coerce')

        # Converter valores numéricos
        colunas_numericas = ['Quantidade', 'Valor Unitario', 'Valor Total Item']
        for col in colunas_numericas:
            if col in df_produtos.columns:
                # Limpar e converter valores
                df_produtos[col] = df_produtos[col].astype(str).str.replace(',', '.').str.replace(r'[^\d.-]', '', regex=True)
                df_produtos[col] = pd.to_numeric(df_produtos[col], errors='coerce').fillna(0)

        return df_produtos, df_faturamento
    except Exception as e:
        st.error(f"Erro ao carregar dados: {e}")
        return pd.DataFrame(), pd.DataFrame()

def criar_metricas_principais(df_produtos, df_faturamento):
    """Cria as métricas principais do dashboard"""
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        total_produtos = len(df_produtos)
        st.markdown(f"""
        <div class="metric-card azul-claro">
            <h3 style="color: #1E3A8A; margin: 0;">📦 Total de Produtos</h3>
            <h2 style="color: #1E3A8A; margin: 0.5rem 0 0 0;">{total_produtos:,}</h2>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        if 'Valor Total Item' in df_produtos.columns:
            valor_total = df_produtos['Valor Total Item'].sum()
            valor_total_formatado = f"R$ {valor_total:,.2f}"
            st.markdown(f"""
            <div class="metric-card verde">
                <h3 style="color: #059669; margin: 0;">💰 Valor Total</h3>
                <h2 style="color: #059669; margin: 0.5rem 0 0 0;">{valor_total_formatado}</h2>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown("""
            <div class="metric-card verde">
                <h3 style="color: #059669; margin: 0;">💰 Valor Total</h3>
                <h2 style="color: #059669; margin: 0.5rem 0 0 0;">R$ 0,00</h2>
            </div>
            """, unsafe_allow_html=True)
    
    with col3:
        fornecedores_unicos = df_produtos['Fornecedor'].nunique() if 'Fornecedor' in df_produtos.columns else 0
        st.markdown(f"""
        <div class="metric-card laranja">
            <h3 style="color: #EA580C; margin: 0;">🏢 Fornecedores</h3>
            <h2 style="color: #EA580C; margin: 0.5rem 0 0 0;">{fornecedores_unicos:,}</h2>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        unidades_unicas = df_produtos['Unidade'].nunique() if 'Unidade' in df_produtos.columns else 0
        st.markdown(f"""
        <div class="metric-card">
            <h3 style="color: #1E3A8A; margin: 0;">🏥 Unidades</h3>
            <h2 style="color: #1E3A8A; margin: 0.5rem 0 0 0;">{unidades_unicas:,}</h2>
        </div>
        """, unsafe_allow_html=True)

def criar_filtros_sidebar(df_produtos):
    """Cria os filtros na sidebar"""
    st.sidebar.markdown("## 🔍 Filtros")
    
    # Filtro de data
    if 'Data Emissao' in df_produtos.columns and not df_produtos['Data Emissao'].isna().all():
        data_min = df_produtos['Data Emissao'].min().date()
        data_max = df_produtos['Data Emissao'].max().date()

        periodo_selecionado = st.sidebar.date_input(
            "📅 Período",
            value=(data_min, data_max),
            min_value=data_min,
            max_value=data_max
        )

        if isinstance(periodo_selecionado, tuple) and len(periodo_selecionado) == 2:
            data_inicio, data_fim = periodo_selecionado
        elif len(periodo_selecionado) == 1:
            data_inicio = data_fim = periodo_selecionado[0]
        else:
            data_inicio = data_fim = None
    else:
        data_inicio = data_fim = None
    
    # Filtro de unidade
    unidades = ['Todas'] + sorted(df_produtos['Unidade'].dropna().unique().tolist()) if 'Unidade' in df_produtos.columns else ['Todas']
    unidade_selecionada = st.sidebar.selectbox("🏥 Unidade", unidades)
    
    # Filtro de fornecedor
    fornecedores = ['Todos'] + sorted(df_produtos['Fornecedor'].dropna().unique().tolist()) if 'Fornecedor' in df_produtos.columns else ['Todos']
    fornecedor_selecionado = st.sidebar.selectbox("🏢 Fornecedor", fornecedores)
    
    return data_inicio, data_fim, unidade_selecionada, fornecedor_selecionado

def aplicar_filtros(df, data_inicio, data_fim, unidade_selecionada, fornecedor_selecionado):
    """Aplica os filtros selecionados ao DataFrame"""
    df_filtrado = df.copy()
    
    # Filtro de data
    if data_inicio and data_fim and 'Data Emissao' in df.columns:
        df_filtrado = df_filtrado[
            (df_filtrado['Data Emissao'].dt.date >= data_inicio) &
            (df_filtrado['Data Emissao'].dt.date <= data_fim)
        ]
    
    # Filtro de unidade
    if unidade_selecionada != 'Todas' and 'Unidade' in df.columns:
        df_filtrado = df_filtrado[df_filtrado['Unidade'] == unidade_selecionada]
    
    # Filtro de fornecedor
    if fornecedor_selecionado != 'Todos' and 'Fornecedor' in df.columns:
        df_filtrado = df_filtrado[df_filtrado['Fornecedor'] == fornecedor_selecionado]
    
    return df_filtrado

def main():
    """Função principal do dashboard"""
    
    # Header principal
    st.markdown("""
    <div class="main-header">
        <h1>🏥 SantaClara - Dashboard Financeiro</h1>
        <p>Sistema de Análise de Produtos e Faturamento</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Carregar dados
    df_produtos, df_faturamento = carregar_dados()
    
    if df_produtos.empty:
        st.error("❌ Não foi possível carregar os dados. Verifique se os arquivos existem.")
        return
    
    # Criar filtros na sidebar
    data_inicio, data_fim, unidade_selecionada, fornecedor_selecionado = criar_filtros_sidebar(df_produtos)
    
    # Aplicar filtros
    df_produtos_filtrado = aplicar_filtros(df_produtos, data_inicio, data_fim, unidade_selecionada, fornecedor_selecionado)
    
    # Métricas principais
    criar_metricas_principais(df_produtos_filtrado, df_faturamento)
    
    # Continuar com mais seções...
    st.markdown("---")

    # Abas principais do dashboard
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "📊 Análise Geral",
        "💊 Produtos Detalhados",
        "🏭 Fabricantes & Lotes",
        "📈 Histórico de Preços",
        "📋 Dados Brutos"
    ])

    with tab1:
        # Seção de gráficos principais
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("### 📊 Top 10 Produtos por Valor")
            if 'Descricao Produto' in df_produtos_filtrado.columns and 'Valor Total Item' in df_produtos_filtrado.columns:
                top_produtos = df_produtos_filtrado.groupby('Descricao Produto')['Valor Total Item'].sum().sort_values(ascending=False).head(10)

                fig = px.bar(
                    x=top_produtos.values,
                    y=top_produtos.index,
                    orientation='h',
                    color=top_produtos.values,
                    color_continuous_scale=['#1E3A8A', '#3B82F6', '#059669'],
                    title="Top 10 Produtos por Valor Total"
                )
                fig.update_layout(
                    showlegend=False,
                    height=400,
                    yaxis={'categoryorder': 'total ascending'}
                )
                st.plotly_chart(fig, use_container_width=True)

        with col2:
            st.markdown("### 🏢 Distribuição por Fornecedor")
            if 'Fornecedor' in df_produtos_filtrado.columns and 'Valor Total Item' in df_produtos_filtrado.columns:
                fornecedores = df_produtos_filtrado.groupby('Fornecedor')['Valor Total Item'].sum().sort_values(ascending=False).head(10)

                fig = px.pie(
                    values=fornecedores.values,
                    names=fornecedores.index,
                    color_discrete_sequence=['#1E3A8A', '#3B82F6', '#059669', '#EA580C', '#6B7280']
                )
                fig.update_layout(height=400)
                st.plotly_chart(fig, use_container_width=True)

        # Gráfico de evolução temporal
        st.markdown("### 📈 Evolução Temporal dos Gastos")
        if 'Data Emissao' in df_produtos_filtrado.columns and 'Valor Total Item' in df_produtos_filtrado.columns:
            df_temporal = df_produtos_filtrado.groupby(df_produtos_filtrado['Data Emissao'].dt.to_period('M'))['Valor Total Item'].sum().reset_index()
            df_temporal['Data Emissao'] = df_temporal['Data Emissao'].astype(str)

            fig = px.line(
                df_temporal,
                x='Data Emissao',
                y='Valor Total Item',
                title="Evolução Mensal dos Gastos",
                color_discrete_sequence=['#1E3A8A']
            )
            fig.update_layout(height=400)
            st.plotly_chart(fig, use_container_width=True)

    with tab2:
        st.markdown("### 💊 Análise Detalhada de Produtos")

        # Filtros específicos para produtos
        col1, col2, col3 = st.columns(3)

        with col1:
            produtos_unicos = ['Todos'] + sorted(df_produtos_filtrado['Descricao Produto'].dropna().unique().tolist()) if 'Descricao Produto' in df_produtos_filtrado.columns else ['Todos']
            produto_selecionado = st.selectbox("🔍 Buscar Produto", produtos_unicos)

        with col2:
            if 'Quantidade' in df_produtos_filtrado.columns:
                min_qtd = st.number_input("📦 Quantidade Mínima", min_value=0, value=0)
            else:
                min_qtd = 0

        with col3:
            if 'Valor Unitario' in df_produtos_filtrado.columns:
                min_valor = st.number_input("💰 Valor Mínimo", min_value=0.0, value=0.0, format="%.2f")
            else:
                min_valor = 0.0

        # Aplicar filtros específicos
        df_produtos_detalhado = df_produtos_filtrado.copy()

        if produto_selecionado != 'Todos':
            df_produtos_detalhado = df_produtos_detalhado[df_produtos_detalhado['Descricao Produto'] == produto_selecionado]

        if 'Quantidade' in df_produtos_detalhado.columns and min_qtd > 0:
            df_produtos_detalhado = df_produtos_detalhado[df_produtos_detalhado['Quantidade'] >= min_qtd]

        if 'Valor Unitario' in df_produtos_detalhado.columns and min_valor > 0:
            df_produtos_detalhado = df_produtos_detalhado[df_produtos_detalhado['Valor Unitario'] >= min_valor]

        # Estatísticas do produto selecionado
        if not df_produtos_detalhado.empty:
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                total_registros = len(df_produtos_detalhado)
                st.metric("📊 Total de Registros", f"{total_registros:,}")

            with col2:
                if 'Quantidade' in df_produtos_detalhado.columns:
                    qtd_total = df_produtos_detalhado['Quantidade'].sum()
                    st.metric("📦 Quantidade Total", f"{qtd_total:,.0f}")

            with col3:
                if 'Valor Total Item' in df_produtos_detalhado.columns:
                    valor_total = df_produtos_detalhado['Valor Total Item'].sum()
                    st.metric("💰 Valor Total", f"R$ {valor_total:,.2f}")

            with col4:
                if 'Valor Unitario' in df_produtos_detalhado.columns:
                    preco_medio = df_produtos_detalhado['Valor Unitario'].mean()
                    st.metric("💵 Preço Médio", f"R$ {preco_medio:.2f}")

        # Tabela de produtos
        st.markdown("### 📋 Lista de Produtos")
        colunas_exibir = ['Data Emissao', 'Descricao Produto', 'Fornecedor', 'Quantidade', 'Valor Unitario', 'Valor Total Item']
        colunas_disponiveis = [col for col in colunas_exibir if col in df_produtos_detalhado.columns]

        if colunas_disponiveis:
            st.dataframe(
                df_produtos_detalhado[colunas_disponiveis].head(100),
                use_container_width=True,
                height=400
            )

    with tab3:
        st.markdown("### 🏭 Análise de Fabricantes, Lotes e Validades")

        # Verificar se as novas colunas existem
        tem_fabricante = 'Fabricante' in df_produtos_filtrado.columns
        tem_lote = 'Lote' in df_produtos_filtrado.columns
        tem_validade = 'Data Validade' in df_produtos_filtrado.columns

        if not any([tem_fabricante, tem_lote, tem_validade]):
            st.warning("⚠️ As colunas de Fabricante, Lote e Data de Validade ainda não foram processadas nos dados.")
            st.info("💡 Execute o processamento de NFe para extrair essas informações dos XMLs.")
        else:
            col1, col2 = st.columns(2)

            with col1:
                if tem_fabricante:
                    st.markdown("#### 🏭 Distribuição por Fabricante")
                    fabricantes = df_produtos_filtrado['Fabricante'].value_counts().head(10)

                    if not fabricantes.empty:
                        fig = px.bar(
                            x=fabricantes.values,
                            y=fabricantes.index,
                            orientation='h',
                            color=fabricantes.values,
                            color_continuous_scale=['#1E3A8A', '#059669'],
                            title="Top 10 Fabricantes"
                        )
                        fig.update_layout(height=400, showlegend=False)
                        st.plotly_chart(fig, use_container_width=True)
                    else:
                        st.info("Nenhum fabricante encontrado nos dados filtrados.")

            with col2:
                if tem_validade:
                    st.markdown("#### ⏰ Análise de Validades")

                    # Converter data de validade
                    df_validade = df_produtos_filtrado.copy()
                    if 'Data Validade' in df_validade.columns:
                        df_validade['Data Validade'] = pd.to_datetime(df_validade['Data Validade'], errors='coerce')
                        df_validade = df_validade.dropna(subset=['Data Validade'])

                        if not df_validade.empty:
                            hoje = datetime.now()

                            # Categorizar por proximidade do vencimento
                            def categorizar_validade(data_val):
                                if pd.isna(data_val):
                                    return 'Sem data'
                                dias = (data_val - hoje).days
                                if dias < 0:
                                    return 'Vencido'
                                elif dias <= 30:
                                    return 'Vence em 30 dias'
                                elif dias <= 90:
                                    return 'Vence em 90 dias'
                                elif dias <= 180:
                                    return 'Vence em 6 meses'
                                else:
                                    return 'Vence após 6 meses'

                            df_validade['Categoria_Validade'] = df_validade['Data Validade'].apply(categorizar_validade)
                            validades = df_validade['Categoria_Validade'].value_counts()

                            fig = px.pie(
                                values=validades.values,
                                names=validades.index,
                                color_discrete_sequence=['#EA580C', '#DC2626', '#F59E0B', '#059669', '#1E3A8A']
                            )
                            fig.update_layout(height=400)
                            st.plotly_chart(fig, use_container_width=True)
                        else:
                            st.info("Nenhuma data de validade encontrada nos dados filtrados.")

            # Tabela consolidada
            st.markdown("#### 📊 Resumo Consolidado")

            colunas_resumo = ['Descricao Produto']
            if tem_fabricante:
                colunas_resumo.append('Fabricante')
            if tem_lote:
                colunas_resumo.append('Lote')
            if tem_validade:
                colunas_resumo.append('Data Validade')
            colunas_resumo.extend(['Quantidade', 'Valor Total Item'])

            colunas_disponiveis_resumo = [col for col in colunas_resumo if col in df_produtos_filtrado.columns]

            if colunas_disponiveis_resumo:
                # Filtrar apenas registros que têm pelo menos uma das novas colunas preenchidas
                mask_novas_colunas = pd.Series(True, index=df_produtos_filtrado.index)
                if tem_fabricante:
                    mask_novas_colunas &= df_produtos_filtrado['Fabricante'].notna()
                if tem_lote:
                    mask_novas_colunas |= df_produtos_filtrado['Lote'].notna()
                if tem_validade:
                    mask_novas_colunas |= df_produtos_filtrado['Data Validade'].notna()

                df_resumo = df_produtos_filtrado[mask_novas_colunas][colunas_disponiveis_resumo]

                if not df_resumo.empty:
                    st.dataframe(df_resumo.head(50), use_container_width=True, height=300)

                    # Estatísticas
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        if tem_fabricante:
                            fabricantes_unicos = df_resumo['Fabricante'].nunique()
                            st.metric("🏭 Fabricantes Únicos", fabricantes_unicos)

                    with col2:
                        if tem_lote:
                            lotes_unicos = df_resumo['Lote'].nunique()
                            st.metric("📦 Lotes Únicos", lotes_unicos)

                    with col3:
                        produtos_com_info = len(df_resumo)
                        st.metric("💊 Produtos com Info", produtos_com_info)
                else:
                    st.info("Nenhum produto com informações de fabricante, lote ou validade encontrado.")

    with tab4:
        st.markdown("### 📈 Histórico de Preços e Análise Temporal")

        if 'Descricao Produto' in df_produtos_filtrado.columns and 'Valor Unitario' in df_produtos_filtrado.columns:
            # Seletor de produto para análise de preço
            produtos_para_analise = sorted(df_produtos_filtrado['Descricao Produto'].dropna().unique())
            produto_analise = st.selectbox("🔍 Selecione um produto para análise de preços", produtos_para_analise)

            if produto_analise:
                df_produto_especifico = df_produtos_filtrado[df_produtos_filtrado['Descricao Produto'] == produto_analise]

                col1, col2 = st.columns(2)

                with col1:
                    # Gráfico de evolução de preços
                    st.markdown(f"#### 💰 Evolução de Preços - {produto_analise[:50]}...")

                    if 'Data Emissao' in df_produto_especifico.columns:
                        df_preco_tempo = df_produto_especifico.sort_values('Data Emissao')

                        fig = px.scatter(
                            df_preco_tempo,
                            x='Data Emissao',
                            y='Valor Unitario',
                            size='Quantidade',
                            color='Fornecedor',
                            title=f"Evolução de Preços - {produto_analise[:30]}...",
                            color_discrete_sequence=['#1E3A8A', '#059669', '#EA580C', '#6B7280']
                        )
                        fig.update_layout(height=400)
                        st.plotly_chart(fig, use_container_width=True)

                with col2:
                    # Estatísticas do produto
                    st.markdown("#### 📊 Estatísticas do Produto")

                    preco_min = df_produto_especifico['Valor Unitario'].min()
                    preco_max = df_produto_especifico['Valor Unitario'].max()
                    preco_medio = df_produto_especifico['Valor Unitario'].mean()
                    qtd_total = df_produto_especifico['Quantidade'].sum() if 'Quantidade' in df_produto_especifico.columns else 0

                    st.metric("💵 Preço Mínimo", f"R$ {preco_min:.2f}")
                    st.metric("💰 Preço Máximo", f"R$ {preco_max:.2f}")
                    st.metric("📊 Preço Médio", f"R$ {preco_medio:.2f}")
                    st.metric("📦 Quantidade Total", f"{qtd_total:,.0f}")

                    # Variação de preço
                    variacao = ((preco_max - preco_min) / preco_min * 100) if preco_min > 0 else 0
                    st.metric("📈 Variação de Preço", f"{variacao:.1f}%")

                # Tabela de histórico
                st.markdown("#### 📋 Histórico Detalhado")
                colunas_historico = ['Data Emissao', 'Fornecedor', 'Quantidade', 'Valor Unitario', 'Valor Total Item']
                colunas_hist_disponiveis = [col for col in colunas_historico if col in df_produto_especifico.columns]

                if colunas_hist_disponiveis:
                    df_historico = df_produto_especifico[colunas_hist_disponiveis].sort_values('Data Emissao', ascending=False)
                    st.dataframe(df_historico, use_container_width=True, height=300)
        else:
            st.warning("⚠️ Dados de preços não disponíveis para análise.")

    with tab5:
        st.markdown("### 📋 Dados Brutos")

        # Opções de visualização
        col1, col2 = st.columns(2)

        with col1:
            mostrar_todas_colunas = st.checkbox("📊 Mostrar todas as colunas", value=False)

        with col2:
            limite_registros = st.selectbox("📄 Limite de registros", [50, 100, 500, 1000], index=1)

        # Exibir dados
        if mostrar_todas_colunas:
            st.dataframe(df_produtos_filtrado.head(limite_registros), use_container_width=True, height=500)
        else:
            colunas_principais = [
                'Data Emissao', 'Unidade', 'Fornecedor', 'Descricao Produto',
                'Quantidade', 'Valor Unitario', 'Valor Total Item'
            ]
            colunas_principais_disponiveis = [col for col in colunas_principais if col in df_produtos_filtrado.columns]
            st.dataframe(df_produtos_filtrado[colunas_principais_disponiveis].head(limite_registros), use_container_width=True, height=500)

        # Informações sobre os dados
        st.markdown("#### ℹ️ Informações dos Dados")
        col1, col2, col3 = st.columns(3)

        with col1:
            st.info(f"📊 **Total de registros filtrados:** {len(df_produtos_filtrado):,}")

        with col2:
            st.info(f"📋 **Total de colunas:** {len(df_produtos_filtrado.columns)}")

        with col3:
            if 'Data Emissao' in df_produtos_filtrado.columns:
                periodo = f"{df_produtos_filtrado['Data Emissao'].min().strftime('%d/%m/%Y')} a {df_produtos_filtrado['Data Emissao'].max().strftime('%d/%m/%Y')}"
                st.info(f"📅 **Período:** {periodo}")
            else:
                st.info("📅 **Período:** Não disponível")
    
    # Footer
    st.markdown("""
    <div class="footer">
        <p>© 2024 SantaClara - Sistema de Gestão Financeira | Desenvolvido com ❤️ para análise de dados</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
