#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TESTE DA PROTEÇÃO CONTRA DUPLICAÇÃO
Verifica se o sistema está protegido contra duplicatas
"""

import pandas as pd
import os
import json
from automacao_nf import (
    gerar_chave_unica, 
    verificar_duplicatas, 
    carregar_arquivos_processados,
    salvar_arquivo_processado,
    arquivo_ja_processado,
    ARQUIVO_CONTROLE_PROCESSADOS
)

def testar_protecao_duplicacao():
    print("🛡️ TESTANDO PROTEÇÃO CONTRA DUPLICAÇÃO")
    print("=" * 60)
    
    # 1. Teste de chaves únicas
    print("\n1️⃣ TESTE DE GERAÇÃO DE CHAVES ÚNICAS:")
    
    # Dados de teste para produtos
    produto_teste = {
        'Unidade': 'ARAGUARI',
        'Numero Nota': '12345',
        'Cod. Produto': 'PROD001',
        'Descricao Produto': 'Produto Teste'
    }
    
    chave_produto = gerar_chave_unica(produto_teste, 'controle_produtos.xlsx')
    print(f"   Chave produto: {chave_produto}")
    
    # Dados de teste para faturamento
    faturamento_teste = {
        'Unidade': 'ARAGUARI',
        'Numero Nota': '12345',
        'Num Parcela': '001'
    }
    
    chave_faturamento = gerar_chave_unica(faturamento_teste, 'controle_faturamento_geral.xlsx')
    print(f"   Chave faturamento: {chave_faturamento}")
    
    # 2. Teste de verificação de duplicatas
    print("\n2️⃣ TESTE DE VERIFICAÇÃO DE DUPLICATAS:")
    
    # Simular dados existentes
    dados_existentes = pd.DataFrame([produto_teste])
    
    # Novos dados (um duplicado, um único)
    novos_dados = [
        produto_teste,  # Duplicado
        {
            'Unidade': 'ARAGUARI',
            'Numero Nota': '12346',  # Diferente
            'Cod. Produto': 'PROD002',
            'Descricao Produto': 'Produto Novo'
        }
    ]
    
    dados_unicos = verificar_duplicatas(dados_existentes, novos_dados, 'controle_produtos.xlsx')
    print(f"   Dados originais: {len(novos_dados)}")
    print(f"   Dados únicos após verificação: {len(dados_unicos)}")
    
    # 3. Teste de controle de arquivos processados
    print("\n3️⃣ TESTE DE CONTROLE DE ARQUIVOS PROCESSADOS:")
    
    arquivo_teste = "teste_nfe_12345.xml"
    
    print(f"   Arquivo já processado? {arquivo_ja_processado(arquivo_teste)}")
    
    # Marcar como processado
    salvar_arquivo_processado(arquivo_teste)
    print(f"   Arquivo marcado como processado")
    
    print(f"   Arquivo já processado agora? {arquivo_ja_processado(arquivo_teste)}")
    
    # 4. Verificar arquivo de controle
    print("\n4️⃣ VERIFICAÇÃO DO ARQUIVO DE CONTROLE:")
    
    if os.path.exists(ARQUIVO_CONTROLE_PROCESSADOS):
        with open(ARQUIVO_CONTROLE_PROCESSADOS, 'r', encoding='utf-8') as f:
            arquivos_processados = json.load(f)
        print(f"   Arquivos no controle: {len(arquivos_processados)}")
        print(f"   Últimos 5 arquivos: {arquivos_processados[-5:]}")
    else:
        print("   Arquivo de controle não existe ainda")
    
    # 5. Teste com planilhas reais
    print("\n5️⃣ TESTE COM PLANILHAS REAIS:")
    
    try:
        # Planilha de produtos
        if os.path.exists('controle_produtos.xlsx'):
            df_produtos = pd.read_excel('controle_produtos.xlsx')
            print(f"   Produtos na planilha: {len(df_produtos)} registros")
            
            # Simular dados duplicados
            if len(df_produtos) > 0:
                primeiro_produto = df_produtos.iloc[0].to_dict()
                dados_teste = [primeiro_produto]  # Dados duplicados
                
                dados_unicos = verificar_duplicatas(df_produtos, dados_teste, 'controle_produtos.xlsx')
                print(f"   Teste duplicação: {len(dados_teste)} -> {len(dados_unicos)} (deve ser 0)")
        
        # Planilha de faturamento
        if os.path.exists('controle_faturamento_geral.xlsx'):
            df_faturamento = pd.read_excel('controle_faturamento_geral.xlsx')
            print(f"   Faturamento na planilha: {len(df_faturamento)} registros")
            
            # Simular dados duplicados
            if len(df_faturamento) > 0:
                primeiro_faturamento = df_faturamento.iloc[0].to_dict()
                dados_teste = [primeiro_faturamento]  # Dados duplicados
                
                dados_unicos = verificar_duplicatas(df_faturamento, dados_teste, 'controle_faturamento_geral.xlsx')
                print(f"   Teste duplicação: {len(dados_teste)} -> {len(dados_unicos)} (deve ser 0)")
                
    except Exception as e:
        print(f"   Erro ao testar planilhas reais: {e}")
    
    print("\n✅ TESTE DE PROTEÇÃO CONCLUÍDO!")
    print("=" * 60)
    print("🛡️ PROTEÇÕES IMPLEMENTADAS:")
    print("   ✅ Chaves únicas para identificar duplicatas")
    print("   ✅ Verificação de duplicatas antes de adicionar")
    print("   ✅ Controle de arquivos já processados")
    print("   ✅ Modo reprocessamento para casos especiais")
    print("   ✅ Logs detalhados de duplicatas detectadas")

def limpar_controle_processados():
    """Limpa o arquivo de controle para testes"""
    if os.path.exists(ARQUIVO_CONTROLE_PROCESSADOS):
        os.remove(ARQUIVO_CONTROLE_PROCESSADOS)
        print("🧹 Arquivo de controle limpo para testes")

if __name__ == '__main__':
    # Opção para limpar controle antes do teste
    resposta = input("Limpar arquivo de controle antes do teste? (S/N): ").strip().upper()
    if resposta == 'S':
        limpar_controle_processados()
    
    testar_protecao_duplicacao()
