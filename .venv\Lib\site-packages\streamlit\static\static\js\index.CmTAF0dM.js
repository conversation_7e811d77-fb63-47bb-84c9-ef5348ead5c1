import{n as l,r as o,j as f,l as v}from"./index.C1z8KpLA.js";const A=l("div",{target:"ea9qfvi0"})(()=>({lineHeight:0})),g=l("audio",{target:"ea9qfvi1"})(({theme:r})=>({width:"100%",height:r.sizes.minElementHeight,margin:0,padding:0})),T=v.getLogger("Audio");function y({element:r,endpoints:s,elementMgr:a}){const i=o.useRef(null),{startTime:n,endTime:d,loop:u,autoplay:p}=r,E=o.useMemo(()=>{if(!r.id)return!0;const e=a.getElementState(r.id,"preventAutoplay");return e||a.setElementState(r.id,"preventAutoplay",!0),e??!1},[r.id,a]);o.useEffect(()=>{i.current&&(i.current.currentTime=n)},[n]),o.useEffect(()=>{const e=i.current,t=()=>{e&&(e.currentTime=r.startTime)};return e&&e.addEventListener("loadedmetadata",t),()=>{e&&e.removeEventListener("loadedmetadata",t)}},[r]),o.useEffect(()=>{const e=i.current;if(!e)return;let t=!1;const c=()=>{d>0&&e.currentTime>=d&&(u?(e.currentTime=n||0,e.play()):t||(t=!0,e.pause()))};return d>0&&e.addEventListener("timeupdate",c),()=>{e&&d>0&&e.removeEventListener("timeupdate",c)}},[d,u,n]),o.useEffect(()=>{const e=i.current;if(!e)return;const t=()=>{u&&(e.currentTime=n||0,e.play())};return e.addEventListener("ended",t),()=>{e&&e.removeEventListener("ended",t)}},[u,n]);const m=s.buildMediaURL(r.url);return f(A,{children:f(g,{className:"stAudio","data-testid":"stAudio",ref:i,controls:!0,autoPlay:p&&!E,src:m,onError:e=>{const t=e.currentTarget.src;T.error(`Client Error: Audio source error - ${t}`),s.sendClientErrorToHost("Audio","Audio source failed to load","onerror triggered",t)}})})}const S=o.memo(y);export{S as default};
