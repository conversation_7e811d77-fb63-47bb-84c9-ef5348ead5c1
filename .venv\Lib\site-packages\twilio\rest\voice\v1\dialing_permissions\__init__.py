r"""
    This code was generated by
   ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
    |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
    |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \

    Twilio - Voice
    This is the public Twilio REST API.

    NOTE: This class is auto generated by OpenAPI Generator.
    https://openapi-generator.tech
    Do not edit the class manually.
"""

from typing import Optional


from twilio.base.list_resource import ListResource
from twilio.base.version import Version

from twilio.rest.voice.v1.dialing_permissions.bulk_country_update import (
    BulkCountryUpdateList,
)
from twilio.rest.voice.v1.dialing_permissions.country import CountryList
from twilio.rest.voice.v1.dialing_permissions.settings import SettingsList


class DialingPermissionsList(ListResource):

    def __init__(self, version: Version):
        """
        Initialize the DialingPermissionsList

        :param version: Version that contains the resource

        """
        super().__init__(version)

        self._uri = "/DialingPermissions"

        self._bulk_country_updates: Optional[BulkCountryUpdateList] = None
        self._countries: Optional[CountryList] = None
        self._settings: Optional[SettingsList] = None

    @property
    def bulk_country_updates(self) -> BulkCountryUpdateList:
        """
        Access the bulk_country_updates
        """
        if self._bulk_country_updates is None:
            self._bulk_country_updates = BulkCountryUpdateList(self._version)
        return self._bulk_country_updates

    @property
    def countries(self) -> CountryList:
        """
        Access the countries
        """
        if self._countries is None:
            self._countries = CountryList(self._version)
        return self._countries

    @property
    def settings(self) -> SettingsList:
        """
        Access the settings
        """
        if self._settings is None:
            self._settings = SettingsList(self._version)
        return self._settings

    def __repr__(self) -> str:
        """
        Provide a friendly representation

        :returns: Machine friendly representation
        """
        return "<Twilio.Voice.V1.DialingPermissionsList>"
